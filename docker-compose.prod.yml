version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: beefcake-mysql-prod
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123}
      MYSQL_DATABASE: ${DB_NAME:-beefcake}
      MYSQL_USER: ${DB_USER:-beefcake_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-beefcake_pass}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - beefcake-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: beefcake-backend-prod
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-beefcake}
      DB_USER: ${DB_USER:-beefcake_user}
      DB_PASSWORD: ${DB_PASSWORD:-beefcake_pass}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      APP_ENV: production
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - beefcake-network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: beefcake-frontend-prod
    environment:
      VITE_API_BASE_URL: http://coffee.local:8080
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - beefcake-network
    restart: unless-stopped

volumes:
  mysql_data_prod:

networks:
  beefcake-network:
    driver: bridge
