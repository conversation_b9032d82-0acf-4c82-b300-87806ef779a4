{"name": "beefcake-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@material/web": "^2.3.0", "@tanstack/react-query": "^4.35.0", "antd": "^5.9.0", "axios": "^1.5.0", "dagre": "^0.8.5", "dayjs": "^1.11.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "reactflow": "^11.11.4", "recharts": "^2.8.0", "zustand": "^4.4.1"}, "devDependencies": {"@types/dagre": "^0.7.53", "@types/react": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}