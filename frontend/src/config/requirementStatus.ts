// 需求状态配置
export interface RequirementStatusConfig {
  key: string
  label: string
  color: string
  icon: string
  description: string
  order: number
  isActive: boolean
  allowedTransitions: string[] // 允许转换到的状态
}

// 默认状态配置
export const DEFAULT_REQUIREMENT_STATUS_CONFIG: RequirementStatusConfig[] = [
  {
    key: 'DRAFT',
    label: '草稿',
    color: '#d9d9d9',
    icon: '📄',
    description: '需求草稿阶段，待完善',
    order: 1,
    isActive: true,
    allowedTransitions: ['REVIEW', 'REJECTED']
  },
  {
    key: 'REVIEW',
    label: '评审中',
    color: '#faad14',
    icon: '👁️',
    description: '需求评审阶段，待批准',
    order: 2,
    isActive: true,
    allowedTransitions: ['APPROVED', 'DRAFT', 'REJECTED']
  },
  {
    key: 'APPROVED',
    label: '已批准',
    color: '#52c41a',
    icon: '✅',
    description: '需求已批准，可以开始实施',
    order: 3,
    isActive: true,
    allowedTransitions: ['IN_PROGRESS', 'REVIEW']
  },
  {
    key: 'IN_PROGRESS',
    label: '进行中',
    color: '#1890ff',
    icon: '🔄',
    description: '需求正在开发实施中',
    order: 4,
    isActive: true,
    allowedTransitions: ['TESTING', 'APPROVED', 'BLOCKED']
  },
  {
    key: 'TESTING',
    label: '测试中',
    color: '#722ed1',
    icon: '🧪',
    description: '需求功能测试阶段',
    order: 5,
    isActive: true,
    allowedTransitions: ['DELIVERED', 'IN_PROGRESS', 'BLOCKED']
  },
  {
    key: 'DELIVERED',
    label: '已交付',
    color: '#13c2c2',
    icon: '🚀',
    description: '需求已完成并交付',
    order: 6,
    isActive: true,
    allowedTransitions: ['TESTING', 'ARCHIVED'] // 允许回退到测试或归档
  },
  {
    key: 'BLOCKED',
    label: '阻塞',
    color: '#ff7875',
    icon: '🚫',
    description: '需求被阻塞，无法继续',
    order: 7,
    isActive: true,
    allowedTransitions: ['IN_PROGRESS', 'TESTING', 'REJECTED']
  },
  {
    key: 'REJECTED',
    label: '已拒绝',
    color: '#ff4d4f',
    icon: '❌',
    description: '需求被拒绝，不予实施',
    order: 8,
    isActive: true,
    allowedTransitions: ['DRAFT', 'ARCHIVED'] // 允许重新提交或归档
  },
  {
    key: 'ARCHIVED',
    label: '已归档',
    color: '#8c8c8c',
    icon: '📦',
    description: '需求已归档，不再活跃',
    order: 99,
    isActive: true,
    allowedTransitions: [] // 归档后不允许转换
  }
]

// 状态管理类
export class RequirementStatusManager {
  private statusConfigs: RequirementStatusConfig[]
  private isInitialized: boolean = false

  constructor(configs: RequirementStatusConfig[] = DEFAULT_REQUIREMENT_STATUS_CONFIG) {
    this.statusConfigs = configs.sort((a, b) => a.order - b.order)
  }

  // 初始化状态配置（从后端加载）
  async initialize(remoteConfigs?: RequirementStatusConfig[]): Promise<void> {
    if (remoteConfigs && remoteConfigs.length > 0) {
      this.statusConfigs = remoteConfigs.sort((a, b) => a.order - b.order)
    }
    this.isInitialized = true
  }

  // 检查是否已初始化
  isReady(): boolean {
    return this.isInitialized
  }

  // 获取所有激活的状态
  getActiveStatuses(): RequirementStatusConfig[] {
    return this.statusConfigs.filter(config => config.isActive)
  }

  // 根据 key 获取状态配置
  getStatusConfig(key: string): RequirementStatusConfig | undefined {
    return this.statusConfigs.find(config => config.key === key)
  }

  // 获取状态标签
  getStatusLabel(key: string): string {
    return this.getStatusConfig(key)?.label || key
  }

  // 获取状态颜色
  getStatusColor(key: string): string {
    return this.getStatusConfig(key)?.color || '#d9d9d9'
  }

  // 获取状态图标
  getStatusIcon(key: string): string {
    return this.getStatusConfig(key)?.icon || '📋'
  }

  // 获取状态描述
  getStatusDescription(key: string): string {
    return this.getStatusConfig(key)?.description || ''
  }

  // 检查状态转换是否允许
  canTransitionTo(fromStatus: string, toStatus: string): boolean {
    const config = this.getStatusConfig(fromStatus)
    return config?.allowedTransitions.includes(toStatus) || false
  }

  // 获取允许转换到的状态列表
  getAllowedTransitions(fromStatus: string): RequirementStatusConfig[] {
    const config = this.getStatusConfig(fromStatus)
    if (!config) return []
    
    return config.allowedTransitions
      .map(key => this.getStatusConfig(key))
      .filter(Boolean) as RequirementStatusConfig[]
  }

  // 更新状态配置
  updateStatusConfig(key: string, updates: Partial<RequirementStatusConfig>): void {
    const index = this.statusConfigs.findIndex(config => config.key === key)
    if (index !== -1) {
      this.statusConfigs[index] = { ...this.statusConfigs[index], ...updates }
    }
  }

  // 添加新状态
  addStatus(config: RequirementStatusConfig): void {
    this.statusConfigs.push(config)
    this.statusConfigs.sort((a, b) => a.order - b.order)
  }

  // 禁用状态
  disableStatus(key: string): void {
    this.updateStatusConfig(key, { isActive: false })
  }

  // 启用状态
  enableStatus(key: string): void {
    this.updateStatusConfig(key, { isActive: true })
  }

  // 获取看板列配置
  getKanbanColumns(): Array<{
    key: string
    title: string
    color: string
    icon: string
    description: string
    requirements: any[]
  }> {
    return this.getActiveStatuses().map(config => ({
      key: config.key,
      title: config.label,
      color: config.color,
      icon: config.icon,
      description: config.description,
      requirements: []
    }))
  }
}

// 创建默认实例
export const requirementStatusManager = new RequirementStatusManager()

// 初始化状态管理器（从后端加载配置）
export const initializeStatusManager = async (): Promise<void> => {
  try {
    const { statusConfigCache } = await import('../services/statusApi')
    const remoteConfigs = await statusConfigCache.getConfigs()

    if (remoteConfigs.length > 0) {
      await requirementStatusManager.initialize(remoteConfigs)
      console.log('状态管理器已从后端加载配置')
    } else {
      console.log('使用默认状态配置')
    }
  } catch (error) {
    console.warn('从后端加载状态配置失败，使用默认配置:', error)
  }
}

// 导出类型
export type RequirementStatusKey = string
