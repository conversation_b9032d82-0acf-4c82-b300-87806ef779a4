// 任务状态配置
export interface TaskStatusConfig {
  key: string
  label: string
  color: string
  icon: string
  description: string
  order: number
  isActive: boolean
  allowedTransitions: string[] // 允许转换到的状态
}

// 默认任务状态配置
export const DEFAULT_TASK_STATUS_CONFIG: TaskStatusConfig[] = [
  {
    key: 'TODO',
    label: '待开始',
    color: '#d9d9d9',
    icon: '📋',
    description: '任务待开始，准备阶段',
    order: 1,
    isActive: true,
    allowedTransitions: ['IN_PROGRESS', 'BLOCKED']
  },
  {
    key: 'IN_PROGRESS',
    label: '进行中',
    color: '#1890ff',
    icon: '🔄',
    description: '任务正在进行中',
    order: 2,
    isActive: true,
    allowedTransitions: ['REVIEW', 'TESTING', 'BLOCKED', 'TODO']
  },
  {
    key: 'BLOCKED',
    label: '阻塞',
    color: '#ff7875',
    icon: '🚫',
    description: '任务被阻塞，等待依赖',
    order: 3,
    isActive: true,
    allowedTransitions: ['TODO', 'IN_PROGRESS']
  },
  {
    key: 'REVIEW',
    label: '评审中',
    color: '#faad14',
    icon: '👁️',
    description: '任务代码评审阶段',
    order: 4,
    isActive: true,
    allowedTransitions: ['IN_PROGRESS', 'TESTING', 'DONE']
  },
  {
    key: 'TESTING',
    label: '测试中',
    color: '#722ed1',
    icon: '🧪',
    description: '任务功能测试阶段',
    order: 5,
    isActive: true,
    allowedTransitions: ['REVIEW', 'DONE', 'IN_PROGRESS']
  },
  {
    key: 'DONE',
    label: '已完成',
    color: '#52c41a',
    icon: '✅',
    description: '任务已完成',
    order: 6,
    isActive: true,
    allowedTransitions: ['TESTING', 'REVIEW']
  }
]

// 任务状态颜色映射
export const TASK_STATUS_COLORS: Record<string, string> = {
  TODO: '#d9d9d9',
  IN_PROGRESS: '#1890ff',
  BLOCKED: '#ff7875',
  REVIEW: '#faad14',
  TESTING: '#722ed1',
  DONE: '#52c41a'
}

// 任务状态图标映射
export const TASK_STATUS_ICONS: Record<string, string> = {
  TODO: '📋',
  IN_PROGRESS: '🔄',
  BLOCKED: '🚫',
  REVIEW: '👁️',
  TESTING: '🧪',
  DONE: '✅'
}

// 任务状态标签映射
export const TASK_STATUS_LABELS: Record<string, string> = {
  TODO: '待开始',
  IN_PROGRESS: '进行中',
  BLOCKED: '阻塞',
  REVIEW: '评审中',
  TESTING: '测试中',
  DONE: '已完成'
}

// 获取任务状态配置
export const getTaskStatusConfig = (key: string): TaskStatusConfig | undefined => {
  return DEFAULT_TASK_STATUS_CONFIG.find(config => config.key === key)
}

// 获取任务状态颜色
export const getTaskStatusColor = (status: string): string => {
  return TASK_STATUS_COLORS[status] || '#d9d9d9'
}

// 获取任务状态图标
export const getTaskStatusIcon = (status: string): string => {
  return TASK_STATUS_ICONS[status] || '📋'
}

// 获取任务状态标签
export const getTaskStatusLabel = (status: string): string => {
  return TASK_STATUS_LABELS[status] || status
}

// 检查状态转换是否允许
export const isStatusTransitionAllowed = (fromStatus: string, toStatus: string): boolean => {
  const config = getTaskStatusConfig(fromStatus)
  return config ? config.allowedTransitions.includes(toStatus) : false
}

// 获取可以转换到的状态列表
export const getAllowedTransitions = (fromStatus: string): TaskStatusConfig[] => {
  const config = getTaskStatusConfig(fromStatus)
  if (!config) return []
  
  return config.allowedTransitions
    .map(key => getTaskStatusConfig(key))
    .filter(Boolean) as TaskStatusConfig[]
}

// 任务状态统计数据接口
export interface TaskStatusStatistics {
  statusKey: string
  statusLabel: string
  count: number
  percentage: number
}

// 任务状态变更日志接口
export interface TaskStatusChangeLog {
  id: number
  taskId: number
  fromStatus: string
  toStatus: string
  fromStatusLabel: string
  toStatusLabel: string
  changedBy: number
  changedAt: string
  reason?: string
  metadata?: string
}
