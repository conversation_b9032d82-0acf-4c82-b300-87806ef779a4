// Material Web 组件导入
// 这个文件集中导入所有需要的Material Web组件

// 首先导入React
import React from 'react'

// 然后导入Material Web组件
// 按钮组件
import '@material/web/button/filled-button.js';
import '@material/web/button/outlined-button.js';
import '@material/web/button/text-button.js';
import '@material/web/button/elevated-button.js';
import '@material/web/button/filled-tonal-button.js';

// 浮动操作按钮
import '@material/web/fab/fab.js';
import '@material/web/fab/branded-fab.js';

// 文本字段组件
import '@material/web/textfield/filled-text-field.js';
import '@material/web/textfield/outlined-text-field.js';

// 选择组件
import '@material/web/select/filled-select.js';
import '@material/web/select/outlined-select.js';
import '@material/web/select/select-option.js';

// 复选框和单选框
import '@material/web/checkbox/checkbox.js';
import '@material/web/radio/radio.js';

// 开关
import '@material/web/switch/switch.js';

// 芯片组件
import '@material/web/chips/chip-set.js';
import '@material/web/chips/filter-chip.js';
import '@material/web/chips/input-chip.js';
import '@material/web/chips/assist-chip.js';
import '@material/web/chips/suggestion-chip.js';

// 卡片组件
import '@material/web/elevation/elevation.js';

// 图标按钮
import '@material/web/iconbutton/icon-button.js';
import '@material/web/iconbutton/filled-icon-button.js';
import '@material/web/iconbutton/filled-tonal-icon-button.js';
import '@material/web/iconbutton/outlined-icon-button.js';

// 菜单组件
import '@material/web/menu/menu.js';
import '@material/web/menu/menu-item.js';
import '@material/web/menu/sub-menu.js';

// 对话框
import '@material/web/dialog/dialog.js';

// 进度指示器
import '@material/web/progress/circular-progress.js';
import '@material/web/progress/linear-progress.js';

// 滑块
import '@material/web/slider/slider.js';

// 标签页
import '@material/web/tabs/tabs.js';
import '@material/web/tabs/primary-tab.js';
import '@material/web/tabs/secondary-tab.js';

// 列表
import '@material/web/list/list.js';
import '@material/web/list/list-item.js';

// 分割线
import '@material/web/divider/divider.js';

// 导航组件 - 注释掉不存在的组件
// import '@material/web/navigationbar/navigation-bar.js';
// import '@material/web/navigationtab/navigation-tab.js';
// import '@material/web/navigationdrawer/navigation-drawer.js';
// import '@material/web/navigationdrawer/navigation-drawer-modal.js';

// 应用栏
// import '@material/web/appbar/top-app-bar.js'; // 如果可用

// 卡片
// import '@material/web/card/card.js'; // 如果可用

// 徽章
// import '@material/web/badge/badge.js'; // 如果可用

// 工具提示
// import '@material/web/tooltip/tooltip.js'; // 如果可用

// 搜索
// import '@material/web/search/search-bar.js'; // 如果可用

// 导入字体样式
import { styles as typescaleStyles } from '@material/web/typography/md-typescale-styles.js';

// 应用字体样式到文档
if (typeof document !== 'undefined' && typescaleStyles.styleSheet) {
  document.adoptedStyleSheets.push(typescaleStyles.styleSheet);
}

// 导出类型定义（如果需要）
export type MaterialButton = HTMLElementTagNameMap['md-filled-button'];
export type MaterialTextField = HTMLElementTagNameMap['md-outlined-text-field'];
export type MaterialSelect = HTMLElementTagNameMap['md-outlined-select'];
export type MaterialCheckbox = HTMLElementTagNameMap['md-checkbox'];
export type MaterialRadio = HTMLElementTagNameMap['md-radio'];
export type MaterialDialog = HTMLElementTagNameMap['md-dialog'];

// 工具函数：创建Material组件的React包装器
export function createMaterialComponent<T extends HTMLElement>(
  tagName: string,
  displayName: string
) {
  const Component = React.forwardRef<T, any>((props, ref) => {
    const { children, ...otherProps } = props;

    // 处理布尔属性，确保它们正确传递给Web Components
    const processedProps = { ...otherProps };

    // 对于disabled属性，确保它作为布尔值正确传递
    if ('disabled' in processedProps) {
      if (processedProps.disabled === true) {
        processedProps.disabled = true;
      } else if (processedProps.disabled === false) {
        delete processedProps.disabled; // 移除false值，让Web Component使用默认值
      }
    }

    return React.createElement(
      tagName,
      {
        ...processedProps,
        ref,
      },
      children
    );
  });

  Component.displayName = displayName;
  return Component;
}

// 常用的Material组件包装器

// 按钮组件
export const MdFilledButton = createMaterialComponent<MaterialButton>('md-filled-button', 'MdFilledButton');
export const MdOutlinedButton = createMaterialComponent<MaterialButton>('md-outlined-button', 'MdOutlinedButton');
export const MdTextButton = createMaterialComponent<MaterialButton>('md-text-button', 'MdTextButton');
export const MdElevatedButton = createMaterialComponent<MaterialButton>('md-elevated-button', 'MdElevatedButton');
export const MdFilledTonalButton = createMaterialComponent<MaterialButton>('md-filled-tonal-button', 'MdFilledTonalButton');

// 浮动操作按钮
export const MdFab = createMaterialComponent<HTMLElement>('md-fab', 'MdFab');
export const MdBrandedFab = createMaterialComponent<HTMLElement>('md-branded-fab', 'MdBrandedFab');

// 文本字段组件
export const MdOutlinedTextField = createMaterialComponent<MaterialTextField>('md-outlined-text-field', 'MdOutlinedTextField');
export const MdFilledTextField = createMaterialComponent<MaterialTextField>('md-filled-text-field', 'MdFilledTextField');

// 选择组件
export const MdOutlinedSelect = createMaterialComponent<MaterialSelect>('md-outlined-select', 'MdOutlinedSelect');
export const MdFilledSelect = createMaterialComponent<MaterialSelect>('md-filled-select', 'MdFilledSelect');
export const MdSelectOption = createMaterialComponent<HTMLElement>('md-select-option', 'MdSelectOption');

// 表单控件
export const MdCheckbox = createMaterialComponent<MaterialCheckbox>('md-checkbox', 'MdCheckbox');
export const MdRadio = createMaterialComponent<MaterialRadio>('md-radio', 'MdRadio');
export const MdSwitch = createMaterialComponent<HTMLElement>('md-switch', 'MdSwitch');

// 对话框
export const MdDialog = createMaterialComponent<MaterialDialog>('md-dialog', 'MdDialog');

// 图标按钮
export const MdIconButton = createMaterialComponent<HTMLElement>('md-icon-button', 'MdIconButton');
export const MdFilledIconButton = createMaterialComponent<HTMLElement>('md-filled-icon-button', 'MdFilledIconButton');
export const MdFilledTonalIconButton = createMaterialComponent<HTMLElement>('md-filled-tonal-icon-button', 'MdFilledTonalIconButton');
export const MdOutlinedIconButton = createMaterialComponent<HTMLElement>('md-outlined-icon-button', 'MdOutlinedIconButton');

// 进度指示器
export const MdCircularProgress = createMaterialComponent<HTMLElement>('md-circular-progress', 'MdCircularProgress');
export const MdLinearProgress = createMaterialComponent<HTMLElement>('md-linear-progress', 'MdLinearProgress');

// 芯片组件
export const MdChipSet = createMaterialComponent<HTMLElement>('md-chip-set', 'MdChipSet');
export const MdFilterChip = createMaterialComponent<HTMLElement>('md-filter-chip', 'MdFilterChip');
export const MdInputChip = createMaterialComponent<HTMLElement>('md-input-chip', 'MdInputChip');
export const MdAssistChip = createMaterialComponent<HTMLElement>('md-assist-chip', 'MdAssistChip');
export const MdSuggestionChip = createMaterialComponent<HTMLElement>('md-suggestion-chip', 'MdSuggestionChip');

// 列表组件
export const MdList = createMaterialComponent<HTMLElement>('md-list', 'MdList');
export const MdListItem = createMaterialComponent<HTMLElement>('md-list-item', 'MdListItem');

// 菜单组件
export const MdMenu = createMaterialComponent<HTMLElement>('md-menu', 'MdMenu');
export const MdMenuItem = createMaterialComponent<HTMLElement>('md-menu-item', 'MdMenuItem');
export const MdSubMenu = createMaterialComponent<HTMLElement>('md-sub-menu', 'MdSubMenu');

// 标签页组件
export const MdTabs = createMaterialComponent<HTMLElement>('md-tabs', 'MdTabs');
export const MdPrimaryTab = createMaterialComponent<HTMLElement>('md-primary-tab', 'MdPrimaryTab');
export const MdSecondaryTab = createMaterialComponent<HTMLElement>('md-secondary-tab', 'MdSecondaryTab');

// 导航组件 - 注释掉不存在的组件
// export const MdNavigationBar = createMaterialComponent<HTMLElement>('md-navigation-bar', 'MdNavigationBar');
// export const MdNavigationTab = createMaterialComponent<HTMLElement>('md-navigation-tab', 'MdNavigationTab');
// export const MdNavigationDrawer = createMaterialComponent<HTMLElement>('md-navigation-drawer', 'MdNavigationDrawer');
// export const MdNavigationDrawerModal = createMaterialComponent<HTMLElement>('md-navigation-drawer-modal', 'MdNavigationDrawerModal');

// 其他组件
export const MdDivider = createMaterialComponent<HTMLElement>('md-divider', 'MdDivider');
export const MdSlider = createMaterialComponent<HTMLElement>('md-slider', 'MdSlider');
export const MdElevation = createMaterialComponent<HTMLElement>('md-elevation', 'MdElevation');