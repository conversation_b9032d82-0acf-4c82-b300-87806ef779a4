import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import type { WeeklyReport, WeeklyRequirementItem } from '../types'
import { getTaskStatusLabel } from '../config/taskStatus'

// PDF导出工具类
export class PDFExporter {
  private pdf: jsPDF
  private pageWidth: number
  private pageHeight: number
  private margin: number
  private currentY: number
  private lineHeight: number

  constructor() {
    this.pdf = new jsPDF('p', 'mm', 'a4')
    this.pageWidth = this.pdf.internal.pageSize.getWidth()
    this.pageHeight = this.pdf.internal.pageSize.getHeight()
    this.margin = 20
    this.currentY = this.margin
    this.lineHeight = 6
    
    // 设置中文字体支持
    this.setupChineseFont()
  }

  private setupChineseFont() {
    // 设置默认字体
    // 由于jsPDF默认不支持中文，这里使用替代方案
    this.pdf.setFont('helvetica')
  }

  // 检查是否需要换页
  private checkPageBreak(requiredHeight: number = 10): void {
    if (this.currentY + requiredHeight > this.pageHeight - this.margin) {
      this.pdf.addPage()
      this.currentY = this.margin
    }
  }

  // 添加标题
  private addTitle(text: string, fontSize: number = 16): void {
    this.checkPageBreak(15)
    this.pdf.setFontSize(fontSize)
    this.pdf.setFont('helvetica', 'bold')
    this.pdf.text(text, this.margin, this.currentY)
    this.currentY += fontSize * 0.5
  }

  // 添加普通文本
  private addText(text: string, fontSize: number = 10, indent: number = 0): void {
    this.checkPageBreak()
    this.pdf.setFontSize(fontSize)
    this.pdf.setFont('helvetica', 'normal')
    
    // 处理长文本换行
    const maxWidth = this.pageWidth - this.margin * 2 - indent
    const lines = this.pdf.splitTextToSize(text, maxWidth)
    
    for (const line of lines) {
      this.checkPageBreak()
      this.pdf.text(line, this.margin + indent, this.currentY)
      this.currentY += this.lineHeight
    }
  }

  // 添加分隔线
  private addSeparator(): void {
    this.checkPageBreak(5)
    this.pdf.setDrawColor(200, 200, 200)
    this.pdf.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY)
    this.currentY += 5
  }

  // 导出周报为PDF
  public async exportWeeklyReport(report: WeeklyReport): Promise<void> {
    // 标题
    this.addTitle(`周报 (${report.weekRange})`, 18)
    this.currentY += 10

    // 统计摘要
    this.addTitle('📊 统计摘要', 14)
    this.addText(`需求总数: ${report.summary.totalRequirements}`)
    this.addText(`任务总数: ${report.summary.totalTasks}`)
    this.addText(`活跃项目: ${report.summary.activeProjects}`)
    this.addText(`已完成需求: ${report.summary.completedRequirements}`)
    this.addText(`已完成任务: ${report.summary.completedTasks}`)
    this.currentY += 10

    // 按项目分组需求
    const projectGroups = this.groupRequirementsByProject(report.requirements)

    Object.entries(projectGroups).forEach(([projectName, requirements]) => {
      this.addSeparator()
      this.addTitle(`🏢 ${projectName}`, 14)
      this.currentY += 5

      requirements.forEach((req, index) => {
        // 需求标题
        this.addText(`${index + 1}. [${req.statusLabel}] ${req.title}`, 12, 5)
        
        // 需求描述
        const description = req.description.length > 200 
          ? req.description.substring(0, 200) + '...' 
          : req.description
        this.addText(`描述: ${description}`, 10, 10)
        
        // 负责人
        if (req.assignee) {
          this.addText(`负责人: ${req.assignee.nickname || req.assignee.username}`, 10, 10)
        }
        
        // 关联任务
        if (req.tasks.length > 0) {
          this.addText(`关联任务 (${req.tasks.length}):`, 10, 10)
          req.tasks.forEach((task, taskIndex) => {
            const taskStatusLabel = getTaskStatusLabel(task.status)
            const taskText = `${taskIndex + 1}) [${taskStatusLabel}] ${task.title}`
            this.addText(taskText, 9, 15)
            
            if (task.assignee) {
              this.addText(`负责人: ${task.assignee.nickname || task.assignee.username}`, 9, 20)
            }
          })
        }
        
        this.currentY += 5
      })
    })

    // 添加生成时间
    this.addSeparator()
    this.addText(`生成时间: ${new Date().toLocaleString()}`, 8)

    // 下载PDF
    const fileName = `周报_${report.weekRange.replace(/~/g, '至').replace(/ /g, '')}.pdf`
    this.pdf.save(fileName)
  }

  // 按项目分组需求
  private groupRequirementsByProject(requirements: WeeklyRequirementItem[]) {
    return requirements.reduce((groups, req) => {
      const projectName = req.project.name
      if (!groups[projectName]) {
        groups[projectName] = []
      }
      groups[projectName].push(req)
      return groups
    }, {} as Record<string, WeeklyRequirementItem[]>)
  }
}

// 导出HTML元素为PDF（备用方案）
export const exportElementToPDF = async (
  elementId: string, 
  filename: string = 'export.pdf'
): Promise<void> => {
  const element = document.getElementById(elementId)
  if (!element) {
    throw new Error(`Element with id '${elementId}' not found`)
  }

  try {
    // 使用html2canvas截图
    const canvas = await html2canvas(element, {
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    })

    const imgData = canvas.toDataURL('image/png')
    const pdf = new jsPDF('p', 'mm', 'a4')
    
    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = pdf.internal.pageSize.getHeight()
    const imgWidth = canvas.width
    const imgHeight = canvas.height
    
    // 计算缩放比例
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight)
    const scaledWidth = imgWidth * ratio
    const scaledHeight = imgHeight * ratio
    
    // 居中显示
    const x = (pdfWidth - scaledWidth) / 2
    const y = (pdfHeight - scaledHeight) / 2
    
    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight)
    pdf.save(filename)
  } catch (error) {
    console.error('PDF导出失败:', error)
    throw new Error('PDF导出失败，请重试')
  }
}

// 创建HTML内容用于PDF导出
const createHTMLContent = (report: WeeklyReport): string => {
  const projectGroups = report.requirements.reduce((groups, req) => {
    const projectName = req.project.name
    if (!groups[projectName]) {
      groups[projectName] = []
    }
    groups[projectName].push(req)
    return groups
  }, {} as Record<string, WeeklyRequirementItem[]>)

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        body {
          font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
          font-size: 12px;
          line-height: 1.6;
          margin: 20px;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #1890ff;
          padding-bottom: 15px;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
          margin-bottom: 10px;
        }
        .summary {
          background: #f5f5f5;
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        .summary-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #1890ff;
        }
        .summary-item {
          margin: 5px 0;
          display: inline-block;
          width: 48%;
        }
        .project-group {
          margin-bottom: 25px;
          border: 1px solid #e8e8e8;
          border-radius: 5px;
          overflow: hidden;
        }
        .project-title {
          font-size: 16px;
          font-weight: bold;
          background: #1890ff;
          color: white;
          padding: 10px 15px;
          margin: 0;
        }
        .requirement {
          margin: 15px;
          padding: 15px;
          border-left: 4px solid #1890ff;
          background: #fafafa;
        }
        .requirement-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 8px;
        }
        .requirement-desc {
          color: #666;
          margin-bottom: 8px;
          line-height: 1.5;
        }
        .assignee {
          color: #888;
          font-size: 11px;
          margin-bottom: 10px;
        }
        .tasks {
          margin-top: 10px;
          padding-left: 15px;
          border-left: 2px solid #e8e8e8;
        }
        .tasks-title {
          font-weight: bold;
          color: #666;
          margin-bottom: 5px;
        }
        .task {
          margin: 5px 0;
          padding: 5px 0;
          font-size: 11px;
        }
        .status-tag {
          display: inline-block;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 10px;
          color: white;
          margin-left: 8px;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 15px;
          border-top: 1px solid #e8e8e8;
          color: #888;
          font-size: 10px;
        }
        @media print {
          body { margin: 0; }
          .project-group { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">周报 (${report.weekRange})</div>
      </div>

      <div class="summary">
        <div class="summary-title">📊 统计摘要</div>
        <div class="summary-item">需求总数: ${report.summary.totalRequirements}</div>
        <div class="summary-item">任务总数: ${report.summary.totalTasks}</div>
        <div class="summary-item">活跃项目: ${report.summary.activeProjects}</div>
        <div class="summary-item">已完成需求: ${report.summary.completedRequirements}</div>
        <div class="summary-item">已完成任务: ${report.summary.completedTasks}</div>
      </div>

      ${Object.entries(projectGroups).map(([projectName, requirements]) => `
        <div class="project-group">
          <h3 class="project-title">🏢 ${projectName} (${requirements.length})</h3>
          ${requirements.map((req, index) => `
            <div class="requirement">
              <div class="requirement-title">
                ${index + 1}. ${req.title}
                <span class="status-tag" style="background-color: ${req.statusColor}">
                  ${req.statusLabel}
                </span>
              </div>
              <div class="requirement-desc">${req.description}</div>
              ${req.assignee ? `<div class="assignee">负责人: ${req.assignee.nickname || req.assignee.username}</div>` : ''}
              ${req.tasks.length > 0 ? `
                <div class="tasks">
                  <div class="tasks-title">关联任务 (${req.tasks.length}):</div>
                  ${req.tasks.map((task, taskIndex) => `
                    <div class="task">
                      ${taskIndex + 1}) ${task.title}
                      <span class="status-tag" style="background-color: ${getTaskStatusLabel(task.status) === '待开始' ? '#d9d9d9' :
                        getTaskStatusLabel(task.status) === '进行中' ? '#1890ff' :
                        getTaskStatusLabel(task.status) === '阻塞' ? '#ff7875' :
                        getTaskStatusLabel(task.status) === '评审中' ? '#faad14' :
                        getTaskStatusLabel(task.status) === '测试中' ? '#722ed1' :
                        getTaskStatusLabel(task.status) === '已完成' ? '#52c41a' : '#d9d9d9'}">
                        ${getTaskStatusLabel(task.status)}
                      </span>
                      ${task.assignee ? `<br><span style="color: #888; margin-left: 20px;">负责人: ${task.assignee.nickname || task.assignee.username}</span>` : ''}
                    </div>
                  `).join('')}
                </div>
              ` : ''}
            </div>
          `).join('')}
        </div>
      `).join('')}

      <div class="footer">
        生成时间: ${new Date().toLocaleString()}
      </div>
    </body>
    </html>
  `
}

// 简化的PDF导出函数 - 使用HTML转PDF方式
export const exportWeeklyReportToPDF = async (report: WeeklyReport): Promise<void> => {
  try {
    // 创建临时HTML元素
    const htmlContent = createHTMLContent(report)
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlContent
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.width = '210mm' // A4宽度
    document.body.appendChild(tempDiv)

    // 使用html2canvas截图
    const canvas = await html2canvas(tempDiv, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 794, // A4宽度像素
      windowWidth: 794
    })

    // 移除临时元素
    document.body.removeChild(tempDiv)

    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    const imgData = canvas.toDataURL('image/png')

    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = pdf.internal.pageSize.getHeight()
    const imgWidth = canvas.width
    const imgHeight = canvas.height

    // 计算需要的页数
    const ratio = pdfWidth / imgWidth
    const scaledHeight = imgHeight * ratio

    let position = 0

    // 如果内容超过一页，分页处理
    while (position < scaledHeight) {
      if (position > 0) {
        pdf.addPage()
      }

      pdf.addImage(
        imgData,
        'PNG',
        0,
        -position,
        pdfWidth,
        scaledHeight
      )

      position += pdfHeight
    }

    // 下载PDF
    const fileName = `周报_${report.weekRange.replace(/~/g, '至').replace(/ /g, '')}.pdf`
    pdf.save(fileName)
  } catch (error) {
    console.error('PDF导出失败:', error)
    throw error
  }
}
