/**
 * URL工具函数
 */

// 获取API基础URL（去掉/api后缀）
const getApiBaseUrl = (): string => {
  const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'
  // 移除末尾的 /api
  return apiUrl.replace(/\/api$/, '')
}

/**
 * 获取完整的头像URL
 * @param avatarUrl 相对路径的头像URL，如 "/api/files/avatars/avatar_1_xxx.webp"
 * @returns 完整的头像URL，如 "http://coffee.local:8080/api/files/avatars/avatar_1_xxx.webp"
 */
export const getFullAvatarUrl = (avatarUrl?: string): string | undefined => {
  if (!avatarUrl) {
    return undefined
  }
  
  // 如果已经是完整URL，直接返回
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl
  }
  
  // 如果是相对路径，拼接完整URL
  const baseUrl = getApiBaseUrl()
  return `${baseUrl}${avatarUrl}`
}

/**
 * 获取API完整URL
 * @param path API路径，如 "/users/me"
 * @returns 完整的API URL
 */
export const getFullApiUrl = (path: string): string => {
  const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'
  return `${apiUrl}${path}`
}
