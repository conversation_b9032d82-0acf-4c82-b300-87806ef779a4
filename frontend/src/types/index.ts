// 用户相关类型
export interface User {
  id: number
  username: string
  nickname?: string
  avatarUrl?: string
  userType: 'NORMAL' | 'SUPER_ADMIN'
  status: 'ACTIVE' | 'DISABLED' | 'LOCKED'
  failedLoginCount: number
  lockedUntil?: string
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  password: string
  nickname?: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface UserUpdateRequest {
  nickname?: string
  avatarUrl?: string
}

export interface PasswordChangeRequest {
  oldPassword: string
  newPassword: string
}

// 任务相关类型
export type TaskStatus =
  | 'TODO'                   // 待办
  | 'IN_PROGRESS'            // 进行中
  | 'BLOCKED'                // 阻塞
  | 'REVIEW'                 // 评审
  | 'TESTING'                // 测试
  | 'DONE'                   // 完成

export type TaskType =
  | 'DESIGN'                 // 设计
  | 'DEVELOPMENT'            // 开发
  | 'TESTING'                // 测试
  | 'DEPLOYMENT'             // 部署
  | 'DOCUMENTATION'          // 文档
  | 'RESEARCH'               // 调研

export type Priority = 'HIGH' | 'MEDIUM' | 'LOW'

export type DependencyType =
  | 'FINISH_TO_START'    // 前置任务完成后，当前任务才能开始（最常见）
  | 'START_TO_START'     // 前置任务开始后，当前任务才能开始（并行任务）
  | 'FINISH_TO_FINISH'   // 前置任务完成后，当前任务才能完成
  | 'START_TO_FINISH'    // 前置任务开始后，当前任务才能完成

export interface TaskDependency {
  id: number
  taskId: number
  dependsOnTaskId: number
  dependencyType: DependencyType
  dependsOnTask?: Task
}

export interface Task {
  id: number
  title: string
  description?: string
  requirementId: number
  taskType: TaskType
  status: TaskStatus
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedHours?: number
  actualHours?: number
  creatorId: number
  assigneeId?: number
  dueDate?: string
  startedAt?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  creator?: User
  assignee?: User
  requirement?: Requirement
  dependencies?: TaskDependency[]
  dependents?: TaskDependency[]
  canStart?: boolean
}

export interface TaskCreateRequest {
  title: string
  description?: string
  requirementId: number
  taskType?: TaskType
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedHours?: number
  assigneeId?: number
  dueDate?: string
  dependencies?: number[]
}

export interface TaskUpdateRequest {
  title?: string
  description?: string
  taskType?: TaskType
  status?: TaskStatus
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedHours?: number
  actualHours?: number
  assigneeId?: number
  dueDate?: string
}

export interface TaskListResponse {
  tasks: Task[]
  total: number
  page: number
  pageSize: number
}

export interface TaskStatusUpdateRequest {
  status: TaskStatus
  comment?: string
}

// 任务日志类型
export type ActionType = 
  | 'CREATE'
  | 'UPDATE_STATUS'
  | 'UPDATE_ASSIGNEE'
  | 'UPDATE_PRIORITY'
  | 'ADD_COMMENT'
  | 'UPLOAD_ATTACHMENT'

export interface TaskLog {
  id: number
  taskId: number
  userId: number
  actionType: ActionType
  oldValue?: string
  newValue?: string
  comment?: string
  createdAt: string
  user?: User
}

// 评论类型
export type CommentType = 'COMMENT' | 'BUG_REPORT'

export interface TaskComment {
  id: number
  taskId: number
  userId: number
  content: string
  commentType: CommentType
  parentCommentId?: number
  createdAt: string
  user?: User
  replies?: TaskComment[]
}

// 周计划类型
export type WeeklyPlanStatus = 'PLANNING' | 'IN_PROGRESS' | 'COMPLETED'

export interface WeeklyPlan {
  id: number
  weekStartDate: string
  weekEndDate: string
  planName?: string
  status: WeeklyPlanStatus
  summary?: string
  createdBy: number
  createdAt: string
  updatedAt: string
  creator?: User
  tasks?: WeeklyPlanTask[]
}

export interface WeeklyPlanTask {
  id: number
  weeklyPlanId: number
  taskId: number
  plannedHours?: number
  isEmergencyInsertion: boolean
  createdAt: string
  task?: Task
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
  code: number
}

export interface ErrorResponse {
  success: false
  message: string
  code: number
  details?: string
}

// 分页类型
export interface PaginationParams {
  page: number
  pageSize: number
}

// 周报相关类型
export interface WeeklyReport {
  requirements: WeeklyRequirementItem[]
  summary: WeeklyReportSummary
  generatedAt: string
  weekRange: string
}

export interface WeeklyRequirementItem {
  id: number
  title: string
  description: string
  status: string
  statusLabel: string
  statusColor: string
  project: ProjectInfo
  assignee?: User
  tasks: WeeklyTaskItem[]
  createdAt: string
  updatedAt: string
}

export interface WeeklyTaskItem {
  id: number
  title: string
  status: string
  statusLabel: string
  statusColor: string
  assignee?: User
  estimatedHours?: number
  actualHours?: number
  dueDate?: string
}

export interface ProjectInfo {
  id: number
  name: string
  description?: string
}

export interface WeeklyReportSummary {
  totalRequirements: number
  requirementsByStatus: Record<string, number>
  totalTasks: number
  tasksByStatus: Record<string, number>
  activeProjects: number
  completedRequirements: number
  completedTasks: number
}

export interface WeeklyReportRequest {
  startDate?: string
  endDate?: string
  projectIds?: number[]
  assigneeIds?: number[]
  includeArchived?: boolean
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 四象限类型
export interface QuadrantData {
  urgentImportant: Task[]      // 紧急且重要 - 立即处理
  notUrgentImportant: Task[]   // 不紧急但重要 - 计划处理
  urgentNotImportant: Task[]   // 紧急但不重要 - 委托处理
  notUrgentNotImportant: Task[] // 不紧急不重要 - 有空处理
}

// 四象限拖拽更新请求
export interface QuadrantUpdateRequest {
  taskId: number
  newImportance: Priority
  newUrgency: Priority
}

// 需求相关类型
// 需求状态类型 - 现在使用动态字符串而不是固定枚举
export type RequirementStatus = string

// 需求状态配置接口
export interface RequirementStatusConfig {
  key: string
  label: string
  color: string
  icon: string
  description: string
  order: number
  isActive: boolean
  allowedTransitions: string[]
  createdAt?: string
  updatedAt?: string
  createdBy?: User
  updatedBy?: User
}

// 状态统计数据已移除

// 状态转换验证响应
export interface StatusTransitionValidationResponse {
  isAllowed: boolean
  reason?: string
}

export interface Requirement {
  id: number
  title: string
  businessDescription: string        // 业务描述，面向老板
  acceptanceCriteria?: string        // 验收标准
  projectId: number                  // 关联项目
  status: RequirementStatus
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedValue?: string            // 预估价值/收益
  targetUsers?: string               // 目标用户群体
  businessGoal?: string              // 业务目标
  creatorId: number
  assigneeId?: number                // 需求负责人
  expectedDeliveryDate?: string      // 期望交付时间
  actualDeliveryDate?: string        // 实际交付时间
  createdAt: string
  updatedAt: string
  creator?: User
  assignee?: User
  project?: Project                  // 关联的项目信息
  tasks?: Task[]                     // 关联的任务列表
}

export interface RequirementCreateRequest {
  title: string
  businessDescription: string
  acceptanceCriteria?: string
  projectId: number                  // 必须选择项目
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedValue?: string
  targetUsers?: string
  businessGoal?: string
  assigneeId?: number
  expectedDeliveryDate?: string
}

export interface RequirementUpdateRequest {
  title?: string
  businessDescription?: string
  acceptanceCriteria?: string
  projectId?: number
  status?: RequirementStatus
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedValue?: string
  targetUsers?: string
  businessGoal?: string
  assigneeId?: number
  expectedDeliveryDate?: string
  actualDeliveryDate?: string
}

export interface RequirementListResponse {
  requirements: Requirement[]
  total: number
  page: number
  pageSize: number
}

// 需求分解为任务的请求
export interface RequirementBreakdownRequest {
  requirementId: number
  tasks: TaskBreakdownItem[]
}

export interface TaskBreakdownItem {
  title: string
  description?: string
  requirementDescription?: string
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedHours?: number
  assigneeId?: number
  dependencies?: number[]
}

// 业务价值报告（给老板看的）
export interface BusinessValueReport {
  requirementId: number
  title: string
  businessDescription: string
  targetUsers?: string
  businessGoal?: string
  estimatedValue?: string
  status: RequirementStatus
  progressPercentage: number
  expectedDeliveryDate?: string
  actualDeliveryDate?: string
  keyMilestones: Milestone[]
  risks: string[]
  achievements: string[]
}

export interface Milestone {
  name: string
  description: string
  targetDate: string
  actualDate?: string
  status: string // PENDING, COMPLETED, DELAYED
}

// 看板数据类型
export interface KanbanData {
  todo: Task[]
  inProgress: Task[]
  blocked: Task[]
  review: Task[]
  testing: Task[]
  done: Task[]
}

// 需求看板数据类型 - 使用动态键值对
export interface RequirementKanbanData {
  [statusKey: string]: Requirement[]
}

// 需求看板列配置
export interface RequirementKanbanColumn {
  key: string
  title: string
  color: string
  icon: string
  description: string
  requirements: Requirement[]
}

// 任务统计类型
export interface TaskStatistics {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  overdueTasks: number
  thisWeekCompleted: number
  thisWeekCreated: number
}

// 需求选项（用于任务创建时选择）
export interface RequirementOption {
  id: number
  title: string
  status: RequirementStatus
}

// 任务选项（用于选择上游任务）
export interface TaskOption {
  id: number
  title: string
  status: string
  taskType: string
}

// 项目相关类型
export interface Project {
  id: number
  name: string
  description?: string
  createdBy: number
  createdAt: string
  updatedAt: string
  creator?: User
  requirementCount?: number
}

export interface ProjectCreateRequest {
  name: string
  description?: string
}

export interface ProjectUpdateRequest {
  name?: string
  description?: string
}

export interface ProjectOption {
  id: number
  name: string
}
