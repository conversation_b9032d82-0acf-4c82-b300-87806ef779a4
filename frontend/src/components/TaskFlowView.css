/* 主容器 */
.task-flow-view {
  position: relative;
  width: 100%;
  height: 600px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
}

/* 控制面板 */
.flow-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  background: white;
  padding: 8px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 画布 */
.flow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  min-width: 1200px; /* 确保有足够的宽度显示层级布局 */
}

/* 背景网格 */
.flow-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
}

/* 节点容器 */
.flow-nodes {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* 任务节点 */
.flow-task-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.flow-task-node:hover {
  z-index: 5;
}

.flow-task-node .task-card {
  transition: all 0.3s ease;
}

.flow-task-node:hover .task-card {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.task-content {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 8px;
}

.task-title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.task-title {
  font-weight: 600;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-id {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

.task-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  font-weight: 500;
}

.task-meta {
  margin-bottom: 8px;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.task-assignee {
  font-size: 11px;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 需求节点 */
.flow-requirement-node {
  z-index: 3;
}

.requirement-card {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.requirement-content {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.requirement-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.requirement-title {
  font-weight: 600;
  font-size: 14px;
  color: #1890ff;
  flex: 1;
}

.requirement-meta {
  display: flex;
  gap: 8px;
}

/* 连接线样式 */
.flow-edges {
  pointer-events: none;
}

.flow-edges path {
  transition: all 0.3s ease;
}

.flow-edges path:hover {
  stroke-width: 3 !important;
}

/* 动画效果 */
@keyframes flowAnimation {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -20;
  }
}

.flow-edges path[stroke-dasharray] {
  animation: flowAnimation 2s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-flow-view {
    height: 500px;
  }

  .flow-controls {
    top: 8px;
    right: 8px;
    padding: 6px;
  }

  .task-title {
    font-size: 12px;
  }

  .requirement-title {
    font-size: 13px;
  }

  .task-content {
    padding: 8px;
  }

  .requirement-content {
    padding: 12px;
  }
}

/* 加载和空状态 */
.task-flow-view .ant-card-loading .ant-card-body {
  padding: 40px 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.task-flow-view .ant-empty {
  padding: 60px 20px;
}

/* 优先级颜色渐变 */
.task-card[style*="border-left: 4px solid #ff4d4f"] {
  background: linear-gradient(90deg, #fff2f0 0%, #ffffff 8%);
}

.task-card[style*="border-left: 4px solid #faad14"] {
  background: linear-gradient(90deg, #fff7e6 0%, #ffffff 8%);
}

.task-card[style*="border-left: 4px solid #52c41a"] {
  background: linear-gradient(90deg, #f6ffed 0%, #ffffff 8%);
}

/* 滚动条样式 */
.flow-canvas::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.flow-canvas::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.flow-canvas::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.flow-canvas::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 节点标签样式 */
.task-content .ant-tag {
  border-radius: 10px;
  font-size: 10px;
  padding: 1px 6px;
  line-height: 1.3;
  border: none;
}

.requirement-content .ant-tag {
  border-radius: 10px;
  font-size: 11px;
  padding: 2px 8px;
  line-height: 1.3;
}
