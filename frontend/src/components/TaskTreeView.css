.task-tree-view {
  padding: 16px;
}

.task-tree-node {
  margin-bottom: 16px;
}

/* 需求分组样式 */
.requirement-group {
  margin-bottom: 32px;
  border: 1px solid #e8f4fd;
  border-radius: 8px;
  background: #fafcff;
  overflow: hidden;
}

.requirement-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid #d6f1ff;
  border-left: 4px solid #1890ff;
}

.requirement-tasks {
  padding: 16px 20px;
}

.task-card-container {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

/* 横向流式布局 */
.task-flow-container {
  display: flex;
  align-items: flex-start;
  gap: 0;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 8px 0;
}

/* 根级任务流容器 */
.requirement-tasks .task-flow-container:first-child {
  margin-bottom: 16px;
}

/* 分支任务流容器 */
.task-branch .task-flow-container {
  flex: 1;
  min-width: 0;
}

.task-connection-line {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 100%;
  min-height: 120px;
  position: relative;
  z-index: 1;
}

.task-connection-line::before {
  content: '';
  width: 30px;
  height: 2px;
  background: #d9d9d9;
  position: relative;
}

.task-connection-line::after {
  content: '';
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid #d9d9d9;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.task-card {
  width: 280px;
  min-height: 120px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.task-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.task-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.task-title-text {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.4;
  margin-right: 8px;
}

.task-status-tag {
  flex-shrink: 0;
}

.task-meta {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.task-description {
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.task-assignee {
  font-size: 11px;
  color: #666;
  flex: 1;
}

.task-actions {
  display: flex;
  gap: 4px;
}

/* 多分支容器样式 */
.task-branches-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-left: 20px;
}

.task-branch {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.task-branch::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #1890ff 0%, #d9d9d9 50%, transparent 100%);
}

.task-branch:first-child::before {
  background: linear-gradient(to bottom, transparent 0%, #1890ff 30%, #d9d9d9 70%, transparent 100%);
}

.task-branch:last-child::before {
  background: linear-gradient(to bottom, #1890ff 0%, #d9d9d9 30%, transparent 70%);
}

.task-branch:only-child::before {
  background: #1890ff;
  height: 60px;
  top: 50%;
  transform: translateY(-50%);
  bottom: auto;
}

/* 分支连接点 */
.task-branch::after {
  content: '';
  position: absolute;
  left: -24px;
  top: 60px;
  width: 8px;
  height: 8px;
  background: #1890ff;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #1890ff;
}

/* 优先级指示器增强 */
.task-card[style*="border-left: 4px solid red"] {
  background: linear-gradient(90deg, #fff2f0 0%, #ffffff 10%);
}

.task-card[style*="border-left: 4px solid orange"] {
  background: linear-gradient(90deg, #fff7e6 0%, #ffffff 10%);
}

.task-card[style*="border-left: 4px solid green"] {
  background: linear-gradient(90deg, #f6ffed 0%, #ffffff 10%);
}

/* 连接线动画 */
.task-connection-line::before {
  transition: all 0.3s ease;
}

.task-flow-container:hover .task-connection-line::before {
  background: #1890ff;
}

.task-flow-container:hover .task-connection-line::after {
  border-left-color: #1890ff;
}

/* 分支连接线动画 */
.task-branch::before {
  transition: all 0.3s ease;
}

.task-branches-container:hover .task-branch::before {
  background: linear-gradient(to bottom, #1890ff 0%, #52c41a 50%, #1890ff 100%) !important;
}

.task-branches-container:hover .task-branch::after {
  background: #52c41a;
  box-shadow: 0 0 0 2px #52c41a;
  transform: scale(1.2);
  transition: all 0.3s ease;
}

/* 分支数量指示器 */
.task-branches-container::before {
  content: attr(data-branch-count) '个分支';
  position: absolute;
  left: -80px;
  top: 50%;
  transform: translateY(-50%);
  background: #f0f9ff;
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #d6f1ff;
  white-space: nowrap;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-card {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .task-tree-view {
    padding: 8px;
  }

  .task-flow-container {
    flex-direction: column;
    gap: 12px;
  }

  .task-connection-line {
    width: 100%;
    height: 20px;
    min-height: 20px;
  }

  .task-connection-line::before {
    width: 2px;
    height: 15px;
    background: #d9d9d9;
  }

  .task-connection-line::after {
    right: 50%;
    top: 100%;
    transform: translateX(50%) rotate(90deg);
  }

  .task-card {
    width: 100%;
    max-width: 320px;
  }

  .requirement-tasks {
    padding: 12px;
  }
}

/* 深度指示器 */
.task-card-container[style*="margin-left: 40px"] .task-card {
  border-left-color: #52c41a !important;
}

.task-card-container[style*="margin-left: 80px"] .task-card {
  border-left-color: #faad14 !important;
}

.task-card-container[style*="margin-left: 120px"] .task-card {
  border-left-color: #722ed1 !important;
}

/* 状态特定样式 */
.task-card .ant-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
}

/* 优先级指示器 */
.task-card[style*="border-left: 4px solid red"] {
  background: linear-gradient(90deg, #fff2f0 0%, #ffffff 20%);
}

.task-card[style*="border-left: 4px solid orange"] {
  background: linear-gradient(90deg, #fff7e6 0%, #ffffff 20%);
}

.task-card[style*="border-left: 4px solid green"] {
  background: linear-gradient(90deg, #f6ffed 0%, #ffffff 20%);
}

/* 动画效果 */
.task-tree-node {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 连接线样式优化 */
.task-connection-line::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  width: 30px;
  height: 1px;
  background: #d9d9d9;
  transform: translateY(-50%);
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 20px;
}

/* 加载状态样式 */
.ant-card-loading .ant-card-body {
  padding: 40px 24px;
}
