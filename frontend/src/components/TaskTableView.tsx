import React, { useMemo, useRef, useEffect } from 'react'
import { Card, Tag, Button, Tooltip, Empty } from 'antd'
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons'
import type { Task, Requirement } from '../types'
import { requirementStatusManager } from '../config/requirementStatus'

interface TaskTableViewProps {
  tasks: Task[]
  requirements: Requirement[]
  onTaskView: (task: Task) => void
  onTaskEdit: (task: Task) => void
  onTaskDelete: (task: Task) => void
  loading?: boolean
}

interface TaskCard {
  id: number
  task: Task
  row: number
  col: number
  x: number
  y: number
  width: number
  height: number
}

interface RequirementRow {
  requirement: Requirement
  y: number
  height: number
}

interface ConnectionLine {
  id: string
  from: { x: number; y: number }
  to: { x: number; y: number }
  type: 'dependency'
  color: string  // 添加颜色属性
  showArrow?: boolean  // 是否显示箭头，默认为true
  isBezier?: boolean  // 是否使用贝塞尔曲线
  controlPoint1?: { x: number; y: number }  // 贝塞尔曲线第一个控制点
  controlPoint2?: { x: number; y: number }  // 贝塞尔曲线第二个控制点
}

const TaskTableView: React.FC<TaskTableViewProps> = ({
  tasks,
  requirements,
  onTaskView,
  onTaskEdit,
  onTaskDelete,
  loading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const svgRef = useRef<SVGSVGElement>(null)

  // 获取任务状态颜色和背景色
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      TODO: '#d9d9d9',
      IN_PROGRESS: '#1890ff',
      BLOCKED: '#ff7875',
      REVIEW: '#faad14',
      TESTING: '#722ed1',
      DONE: '#52c41a'
    }
    return colors[status] || '#d9d9d9'
  }

  const getStatusName = (status: string): string => {
    const statusNames: Record<string, string> = {
      TODO: '待办',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审',
      TESTING: '测试',
      DONE: '完成'
    }
    return statusNames[status] || status
  }





  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    const icons: Record<string, React.ReactElement> = {
      TODO: <ClockCircleOutlined />,
      IN_PROGRESS: <ClockCircleOutlined />,
      BLOCKED: <ClockCircleOutlined />,
      REVIEW: <ClockCircleOutlined />,
      TESTING: <ClockCircleOutlined />,
      DONE: <ClockCircleOutlined />
    }
    return icons[status] || <ClockCircleOutlined />
  }

  // 生成随机颜色的函数
  const generateRandomColors = (count: number): string[] => {
    const colors = [
      '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
      '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb',
      '#fa541c', '#1890ff', '#389e0d', '#d48806', '#cf1322',
      '#531dab', '#08979c', '#c41d7f', '#d4380d', '#096dd9',
      '#7cb305', '#ad6800', '#a8071a', '#9254de', '#006d75',
      '#c41d7f', '#d4380d', '#096dd9', '#7cb305', '#ad6800'
    ]

    // 打乱颜色数组
    const shuffledColors = [...colors].sort(() => Math.random() - 0.5)

    // 如果需要的颜色数量超过预定义颜色，生成额外的随机颜色
    while (shuffledColors.length < count) {
      const randomColor = `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`
      shuffledColors.push(randomColor)
    }

    return shuffledColors.slice(0, count)
  }

  // 计算表格布局和连接线
  const { taskCards, connectionLines, requirementRows } = useMemo(() => {
    const cards: TaskCard[] = []
    const lines: ConnectionLine[] = []
    const reqRows: RequirementRow[] = []

    const CARD_WIDTH = 200
    const CARD_HEIGHT = 110  // 增加卡片高度从100到140
    const COL_SPACING = 80   // 列间距
    const ROW_SPACING = 50   // 增加行间距从40到50
    const COL_WIDTH = CARD_WIDTH + COL_SPACING  // 列宽 = 卡片宽度 + 列间距
    const ROW_HEIGHT = CARD_HEIGHT + ROW_SPACING // 行高 = 卡片高度 + 行间距
    const REQ_HEIGHT = 40    // 需求行高度
    const LEFT_MARGIN = 20
    const TOP_MARGIN = 20

    let currentY = TOP_MARGIN

    // 按需求分组处理任务
    requirements.forEach(requirement => {
      const requirementTasks = tasks.filter(task => task.requirementId === requirement.id)

      if (requirementTasks.length > 0) {
        // 添加需求行
        reqRows.push({
          requirement,
          y: currentY,
          height: REQ_HEIGHT
        })
        currentY += REQ_HEIGHT + 10

        // 重新设计的表格布局算法 - 按照新的逻辑
        console.log(`\n=== 处理需求 ${requirement.id}: ${requirement.title} ===`)

        // 1. 建立一个出列任务的数组存放出列的任务id
        const processedTaskIds = new Set<number>()
        const taskPositions = new Map<number, { row: number; col: number }>()
        const columnTasks: number[][] = [] // 每列的任务ID数组

        // 2. 在第一次循环时建立前置任务和后续任务的关联关系
        const taskDownstreamMap = new Map<number, number[]>() // 任务ID -> 后续任务ID数组

        // 初始化所有任务的后续任务数组
        requirementTasks.forEach(task => {
          taskDownstreamMap.set(task.id, [])
        })

        // 遍历所有任务，将有前置任务的任务放入前置任务的后续任务关联中
        requirementTasks.forEach(task => {
          if (task.dependencies && task.dependencies.length > 0) {
            task.dependencies.forEach(dep => {
              const downstreamTasks = taskDownstreamMap.get(dep.dependsOnTaskId) || []
              downstreamTasks.push(task.id)
              taskDownstreamMap.set(dep.dependsOnTaskId, downstreamTasks)
            })
          }
        })

        console.log('任务后续关系映射:', Object.fromEntries(
          Array.from(taskDownstreamMap.entries()).map(([taskId, downstreamIds]) => {
            const task = requirementTasks.find(t => t.id === taskId)
            return [`${taskId}(${task?.title})`, downstreamIds]
          })
        ))

        // 辅助函数：获取任务的后续任务最小列数（使用已确定的任务位置）
        const getMinDownstreamColumn = (taskId: number): number => {
          const downstreamTaskIds = taskDownstreamMap.get(taskId) || []

          if (downstreamTaskIds.length === 0) {
            return Infinity // 没有后续任务，返回无穷大
          }

          // 直接使用已确定的任务位置计算最小列数
          const downstreamColumns = downstreamTaskIds.map(downstreamTaskId => {
            const pos = taskPositions.get(downstreamTaskId)
            return pos ? pos.col : Infinity
          }).filter(col => col !== Infinity)

          const result = downstreamColumns.length > 0 ? Math.min(...downstreamColumns) : Infinity

          if (downstreamTaskIds.length > 0) {
            console.log(`任务 ${taskId} 后续任务最小列: ${result}`)
          }

          return result
        }

        // 辅助函数：获取任务的前置任务顺序（用于排序）
        const getPredecessorOrder = (taskId: number): number => {
          const task = requirementTasks.find(t => t.id === taskId)
          if (!task || !task.dependencies || task.dependencies.length === 0) {
            return 0 // 没有前置任务，顺序为0
          }

          // 获取前置任务的位置信息
          const predecessorPositions = task.dependencies
            .map(dep => taskPositions.get(dep.dependsOnTaskId))
            .filter(Boolean)

          if (predecessorPositions.length === 0) {
            return 0 // 前置任务还未处理，顺序为0
          }

          // 找到最右边（最大列）的前置任务
          const maxCol = Math.max(...predecessorPositions.map(pos => pos!.col))
          const maxColPredecessors = predecessorPositions.filter(pos => pos!.col === maxCol)

          // 使用最右边前置任务中的最小行号作为顺序指标
          const minRowInMaxCol = Math.min(...maxColPredecessors.map(pos => pos!.row))

          // 组合列号和行号作为排序权重：列号 * 1000 + 行号
          // 这样可以确保列号优先，行号次之
          return maxCol * 1000 + minRowInMaxCol
        }

        // 3. 循环数据，按列处理（前置任务和后续任务关联已建立）
        let currentColumn = 0
        let hasRemainingTasks = true

        while (hasRemainingTasks) {
          console.log(`\n--- 处理第 ${currentColumn} 列 ---`)

          // 4. 将任务的前置任务排除上一次已经出列的任务后，没有剩下前置任务，放入本次列
          const currentColumnCandidates = requirementTasks.filter(task => {
            // 跳过已处理的任务
            if (processedTaskIds.has(task.id)) return false

            // 检查前置任务是否都已处理
            const dependencies = task.dependencies || []
            const unprocessedDeps = dependencies.filter(dep =>
              !processedTaskIds.has(dep.dependsOnTaskId)
            )

            // 没有剩下前置任务的可以放入本次列
            return unprocessedDeps.length === 0
          })

          if (currentColumnCandidates.length === 0) {
            hasRemainingTasks = false
            break
          }

          console.log(`第 ${currentColumn} 列候选任务:`, currentColumnCandidates.map(t => `${t.id}(${t.title})`))

          // 暂时不排序，先确定所有任务的列位置
          currentColumnCandidates.forEach((task, index) => {
            taskPositions.set(task.id, { row: index, col: currentColumn })
            processedTaskIds.add(task.id)

            console.log(`任务 ${task.id}(${task.title}) 临时放入位置: 列${currentColumn}, 行${index}`)
          })

          columnTasks.push(currentColumnCandidates.map(task => task.id))
          currentColumn++
        }

        console.log(`\n=== 所有任务列位置已确定，开始排序 ===`)

        // 5. 所有任务的列确定后，对每列内的任务进行排序
        columnTasks.forEach((taskIds, colIndex) => {
          if (taskIds.length <= 1) return // 只有一个任务的列不需要排序

          console.log(`\n--- 对第 ${colIndex} 列进行排序 ---`)

          const tasksInColumn = taskIds.map(id => requirementTasks.find(t => t.id === id)!).filter(Boolean)

          // 排序：先看前置任务的排序，然后再根据后续任务的列最小值排序，无后续任务的排最后
          tasksInColumn.sort((a, b) => {
            // 第一优先级：前置任务的顺序
            const aPredecessorOrder = getPredecessorOrder(a.id)
            const bPredecessorOrder = getPredecessorOrder(b.id)

            console.log(`第一优先级-前置任务顺序比较: ${a.title}(前置顺序${aPredecessorOrder}) vs ${b.title}(前置顺序${bPredecessorOrder})`)

            if (aPredecessorOrder !== bPredecessorOrder) {
              return aPredecessorOrder - bPredecessorOrder
            }

            // 第二优先级：后续任务的最小列值（现在所有任务列位置都已确定）
            const aMinDownstreamCol = getMinDownstreamColumn(a.id)
            const bMinDownstreamCol = getMinDownstreamColumn(b.id)

            console.log(`第二优先级-后续任务列数比较: ${a.title}(后续最小列${aMinDownstreamCol === Infinity ? '无' : aMinDownstreamCol}) vs ${b.title}(后续最小列${bMinDownstreamCol === Infinity ? '无' : bMinDownstreamCol})`)

            if (aMinDownstreamCol !== bMinDownstreamCol) {
              return aMinDownstreamCol - bMinDownstreamCol // 后续任务列数小的排在前面，无后续任务(Infinity)排在最后
            }

            // 第三优先级：如果前置任务顺序和后续任务列数都相同，按创建时间排序
            console.log(`第三优先级-创建时间比较: ${a.title} vs ${b.title}`)
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          })

          // 更新排序后的任务位置
          tasksInColumn.forEach((task, index) => {
            taskPositions.set(task.id, { row: index, col: colIndex })
            console.log(`任务 ${task.id}(${task.title}) 最终位置: 列${colIndex}, 行${index}`)
          })

          // 更新columnTasks中的顺序
          columnTasks[colIndex] = tasksInColumn.map(task => task.id)
        })

        console.log(`需求 ${requirement.id} 处理完成，共 ${currentColumn} 列`)
        console.log('最终任务位置:', Array.from(taskPositions.entries()).map(([id, pos]) => {
          const task = requirementTasks.find(t => t.id === id)
          return `${id}(${task?.title}): 列${pos.col}, 行${pos.row}`
        }))

        // 6. 创建任务卡片
        taskPositions.forEach((pos, taskId) => {
          const task = requirementTasks.find(t => t.id === taskId)!
          cards.push({
            id: task.id,
            task,
            row: pos.row,
            col: pos.col,
            x: LEFT_MARGIN + pos.col * COL_WIDTH,
            y: currentY + pos.row * ROW_HEIGHT,
            width: CARD_WIDTH,
            height: CARD_HEIGHT
          })
        })

        // 计算这个需求组的最大行数
        const maxRowInGroup = Math.max(...Array.from(taskPositions.values()).map(p => p.row))
        currentY += (maxRowInGroup + 1) * ROW_HEIGHT + 30 // 需求间间距
      }
    })

    // 5. 统一的后续任务查找和调试信息生成
    const getDownstreamTasks = (taskId: number, taskCards: TaskCard[]) => {
      return taskCards.filter(otherCard =>
        otherCard.task.dependencies &&
        otherCard.task.dependencies.some(dep => dep.dependsOnTaskId === taskId)
      )
    }

    // 计算连线数量
    const totalConnectionsCount = cards.reduce((count, card) => {
      return count + getDownstreamTasks(card.task.id, cards).length
    }, 0)

    // 生成随机颜色
    const randomColors = generateRandomColors(totalConnectionsCount)
    let colorIndex = 0

    // 调试信息
    console.log('生成的随机颜色:', randomColors)
    console.log('需要的连线数量:', totalConnectionsCount)
    console.log('任务排序结果:', cards.map(card => {
      const downstreamTasks = getDownstreamTasks(card.task.id, cards)
      const isLeafTask = downstreamTasks.length === 0

      return {
        id: card.task.id,
        title: card.task.title,
        row: card.row,
        col: card.col,
        createdAt: card.task.createdAt,
        dependencies: card.task.dependencies?.map((dep: any) => dep.dependsOnTaskId) || [],
        isLeafTask,
        hasDownstreamTasks: !isLeafTask,
        downstreamTasksCount: downstreamTasks.length,
        downstreamTasks: downstreamTasks.map(dt => ({ id: dt.task.id, title: dt.task.title, col: dt.col }))
      }
    }))

    // 6. 重新设计的连接线逻辑 - 从前置任务出发到直接下游任务
    cards.forEach(card => {
      // 使用统一的后续任务查找函数
      const downstreamTasks = getDownstreamTasks(card.task.id, cards)

      // 为每个直接下游任务创建连接线
      downstreamTasks.forEach(downstreamCard => {
        // 使用随机颜色
        const lineColor = randomColors[colorIndex++]

        // 统一连线方式：从前置任务的rightCenter到下游任务的leftCenter
        const fromPoint = {
          x: card.x + CARD_WIDTH,                    // 前置任务rightCenter x
          y: card.y + CARD_HEIGHT / 2               // 前置任务rightCenter y
        }
        const toPoint = {
          x: downstreamCard.x,                      // 下游任务leftCenter x
          y: downstreamCard.y + CARD_HEIGHT / 2     // 下游任务leftCenter y
        }

        // 计算贝塞尔曲线的控制点
        const distance = toPoint.x - fromPoint.x
        const yDifference = Math.abs(toPoint.y - fromPoint.y)

        // 根据距离和高度差动态调整控制点偏移
        const baseOffset = Math.max(60, distance * 0.4) // 基础偏移量
        const yOffset = Math.min(yDifference * 0.2, 30) // 垂直偏移，最大30px

        const controlPoint1 = {
          x: fromPoint.x + baseOffset,               // 第一个控制点：向右偏移
          y: fromPoint.y + (toPoint.y > fromPoint.y ? yOffset : -yOffset) // 根据方向调整垂直偏移
        }
        const controlPoint2 = {
          x: toPoint.x - baseOffset,                 // 第二个控制点：向左偏移
          y: toPoint.y + (toPoint.y > fromPoint.y ? -yOffset : yOffset) // 反向垂直偏移
        }

        lines.push({
          id: `line-${card.id}-${downstreamCard.id}`,
          from: fromPoint,
          to: toPoint,
          controlPoint1,
          controlPoint2,
          type: 'dependency',
          color: lineColor,
          isBezier: true
        })
      })
    })

    return { taskCards: cards, connectionLines: lines, requirementRows: reqRows }
  }, [tasks, requirements])

  // 重新绘制连接线
  useEffect(() => {
    if (svgRef.current && connectionLines.length > 0) {
      // 强制重新渲染 SVG
      const svg = svgRef.current
      svg.style.display = 'none'
      // 触发重排
      void svg.getBoundingClientRect()
      svg.style.display = 'block'
    }
  }, [connectionLines])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <ClockCircleOutlined spin style={{ fontSize: '24px' }} />
        <div style={{ marginTop: '16px' }}>加载中...</div>
      </div>
    )
  }

  if (taskCards.length === 0) {
    return (
      <Empty
        description="暂无任务数据"
        style={{ padding: '50px' }}
      />
    )
  }

  const containerHeight = Math.max(600,
    requirementRows.length > 0
      ? requirementRows[requirementRows.length - 1].y + 400
      : taskCards.length * 140 + 100
  )

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: '100%',
        height: `${containerHeight}px`,
        overflow: 'auto',
        background: '#f5f5f5',
        padding: '20px'
      }}
    >
      {/* SVG 连接线 */}
      <svg
        ref={svgRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}
      >
        {/* 定义不同颜色的圆点标记 */}
        <defs>
          {/* 为每种唯一颜色创建对应的圆点标记 */}
          {Array.from(new Set(connectionLines.map(line => line.color))).map(color => {
            // 确保颜色格式一致
            const cleanColor = color.replace('#', '').toLowerCase().padStart(6, '0')
            return (
              <marker
                key={`circle-${cleanColor}`}
                id={`circle-${cleanColor}`}
                markerWidth="10"
                markerHeight="10"
                refX="5"
                refY="5"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <circle
                  cx="5"
                  cy="5"
                  r="3.5"
                  fill={color}
                  stroke="white"
                  strokeWidth="1"
                  style={{
                    filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))'
                  }}
                />
              </marker>
            )
          })}
        </defs>

        {connectionLines.map(line => {
          // 根据颜色生成圆点标记ID，确保格式一致
          const getCircleId = (color: string) => {
            // 确保颜色格式正确（移除#号，转为小写，补齐6位）
            const cleanColor = color.replace('#', '').toLowerCase().padStart(6, '0')
            return `circle-${cleanColor}`
          }

          if (line.isBezier && line.controlPoint1 && line.controlPoint2) {
            // 贝塞尔曲线路径
            const pathData = `M ${line.from.x} ${line.from.y} C ${line.controlPoint1.x} ${line.controlPoint1.y}, ${line.controlPoint2.x} ${line.controlPoint2.y}, ${line.to.x} ${line.to.y}`

            return (
              <path
                key={line.id}
                d={pathData}
                stroke={line.color}
                strokeWidth="2.5"
                fill="none"
                markerEnd={line.showArrow !== false ? `url(#${getCircleId(line.color)})` : undefined}
                style={{
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15))',
                  transition: 'all 0.3s ease'
                }}
                strokeDasharray="0"
                strokeLinecap="round"
                strokeLinejoin="round"
                opacity="0.8"
              >
                {/* 添加动画效果 */}
                <animate
                  attributeName="stroke-dasharray"
                  values="0,1000;1000,0"
                  dur="2s"
                  begin="0s"
                  fill="freeze"
                />
              </path>
            )
          } else {
            // 直线
            return (
              <line
                key={line.id}
                x1={line.from.x}
                y1={line.from.y}
                x2={line.to.x}
                y2={line.to.y}
                stroke={line.color}
                strokeWidth="2.5"
                markerEnd={line.showArrow !== false ? `url(#${getCircleId(line.color)})` : undefined}
                strokeLinecap="round"
                opacity="0.8"
                style={{
                  filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))'
                }}
              />
            )
          }
        })}
      </svg>

      {/* 需求行 */}
      {requirementRows.map(reqRow => {
        // 获取需求状态配置
        const statusConfig = requirementStatusManager.getStatusConfig(reqRow.requirement.status)
        const statusColor = statusConfig?.color || '#1890ff'
        const statusLabel = statusConfig?.label || reqRow.requirement.status

        // 根据状态设置背景色（使用状态颜色的浅色版本）
        const backgroundColor = statusColor + '15' // 添加透明度
        const borderColor = statusColor + '50'

        return (
          <div
            key={`req-${reqRow.requirement.id}`}
            style={{
              position: 'absolute',
              left: 20,
              top: reqRow.y,
              width: 'calc(100% - 40px)',
              height: reqRow.height,
              background: backgroundColor,
              border: `1px solid ${borderColor}`,
              borderRadius: '6px',
              padding: '8px 16px',
              display: 'flex',
              alignItems: 'center',
              fontWeight: 'bold',
              fontSize: '16px',
              color: statusColor,
              zIndex: 1
            }}
          >
            <span style={{ marginRight: '8px' }}>
              【{reqRow.requirement.project?.name || '未知项目'}】
            </span>
            <span style={{ marginRight: '8px' }}>
              {reqRow.requirement.title}
            </span>
            <span style={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.8
            }}>
              ({statusLabel})
            </span>
          </div>
        )
      })}

      {/* 任务卡片 */}
      {taskCards.map(card => {
        // 计算状态左竖条样式
        const getStatusLeftBar = (status: string) => {
          return {
            color: getStatusColor(status),
            width: '4px'
          }
        }

        const leftBarStyle = getStatusLeftBar(card.task.status)

        return (
          <Card
            key={card.id}
            size="small"
            style={{
              position: 'absolute',
              left: card.x,
              top: card.y,
              width: card.width,
              height: card.height,
              zIndex: 2,
              cursor: 'pointer',
              // Material 3 阴影系统
              boxShadow: card.task.status === 'DONE'
                ? '0 1px 3px rgba(0,0,0,0.08)' // 已完成任务使用较浅阴影
                : '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)', // 其他任务使用标准阴影
              // Material 3 背景色系统
              backgroundColor: card.task.status === 'DONE'
                ? '#f8f9fa' // 已完成任务使用surface-variant
                : card.task.status === 'TODO'
                ? '#fefbff' // 待办任务使用surface
                : '#ffffff', // 其他任务使用纯白
              border: 'none',
              borderRadius: '16px', // Material 3 圆角
              borderLeft: `4px solid ${leftBarStyle.color}`, // 状态指示条
              transition: 'all 0.2s cubic-bezier(0.2, 0, 0, 1)', // Material 3 动画曲线
            }}
            styles={{ body: { padding: '16px' } }}
            hoverable
          >
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* 任务标题和状态 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
              <Tooltip title={card.task.title}>
                <div style={{
                  // Material 3 Title Medium
                  fontWeight: '500',
                  fontSize: '16px',
                  lineHeight: '24px',
                  letterSpacing: '0.15px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flex: 1,
                  marginRight: '8px',
                  color: card.task.status === 'DONE' ? '#49454f' : '#1d1b20', // Material 3 on-surface colors
                }}>
                  {card.task.title}
                </div>
              </Tooltip>
              <Tag
                color={getStatusColor(card.task.status)}
                icon={getStatusIcon(card.task.status)}
                style={{
                  fontSize: '12px',
                  borderRadius: '16px', // Material 3 圆角
                  border: 'none',
                  fontWeight: '500'
                }}
              >
                {getStatusName(card.task.status)}
              </Tag>
            </div>

            {/* 任务信息 */}
            <div style={{
              flex: 1,
              // Material 3 Body Small
              fontSize: '12px',
              lineHeight: '16px',
              letterSpacing: '0.4px',
              color: card.task.status === 'DONE' ? '#79747e' : '#49454f', // Material 3 on-surface-variant
            }}>
              {card.task.assignee && (
                <div style={{
                  marginBottom: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '4px 8px',
                  backgroundColor: card.task.status === 'DONE' ? '#e7e0ec' : '#f3edf7', // Material 3 surface-container
                  borderRadius: '12px',
                  fontSize: '11px',
                  fontWeight: '500'
                }}>
                  <UserOutlined style={{ fontSize: '10px' }} />
                  {card.task.assignee.nickname || card.task.assignee.username}
                </div>
              )}

              {/* 截止日期展示 */}
              {card.task.dueDate && (
                <div style={{ marginBottom: '6px', fontSize: '11px' }}>
                  {(() => {
                    const dueDate = new Date(card.task.dueDate)
                    const now = new Date()
                    const isOverdue = dueDate < now && card.task.status !== 'DONE'
                    const isToday = dueDate.toDateString() === now.toDateString()
                    const isTomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString() === dueDate.toDateString()

                    let color = '#666666' // 默认颜色
                    let prefix = '📅'

                    if (isOverdue) {
                      color = '#ff4d4f' // 红色 - 已逾期
                      prefix = '🚨'
                    } else if (isToday) {
                      color = '#fa8c16' // 橙色 - 今天到期
                      prefix = '⏰'
                    } else if (isTomorrow) {
                      color = '#faad14' // 黄色 - 明天到期
                      prefix = '⚠️'
                    }

                    return (
                      <span style={{ color, fontWeight: isOverdue || isToday ? '600' : '400' }}>
                        {prefix} 截止: {dueDate.toLocaleDateString('zh-CN', {
                          month: 'short',
                          day: 'numeric'
                        })}
                        {isOverdue && ' (已逾期)'}
                        {/* {isToday && ' (今天)'} */}
                        {/* {isTomorrow && ' (明天)'} */}
                      </span>
                    )
                  })()}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '4px', marginTop: '12px' }}>
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  onTaskView(card.task)
                }}
              />
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  onTaskEdit(card.task)
                }}
              />
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  onTaskDelete(card.task)
                }}
              />
            </div>
          </div>
        </Card>
        )
      })}
    </div>
  )
}

export default TaskTableView
