import React, { useMemo } from 'react'
import { Card, Tag, Button, Tooltip, Typography, Empty } from 'antd'
import { 
  EyeOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ArrowRightOutlined,
  ProjectOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { Task, Requirement, Priority, TaskStatus } from '../types'
import './TaskTreeView.css'

const { Title } = Typography

interface TaskTreeViewProps {
  tasks: Task[]
  requirements: Requirement[]
  onTaskView: (task: Task) => void
  onTaskEdit: (task: Task) => void
  onTaskDelete: (task: Task) => void
  loading?: boolean
}

interface TaskNode {
  task: Task | null // null 表示这是需求节点
  requirement?: Requirement
  children: TaskNode[]
  level: number
  isRequirementNode?: boolean // 标识是否为需求节点
}

const TaskTreeView: React.FC<TaskTreeViewProps> = ({
  tasks,
  requirements,
  onTaskView,
  onTaskEdit,
  onTaskDelete,
  loading = false
}) => {
  // 构建任务依赖树
  const buildTaskTree = React.useCallback((tasks: Task[], requirement?: Requirement): TaskNode[] => {
    const taskMap = new Map<number, Task>()
    const rootTasks: Task[] = []
    const childrenMap = new Map<number, Task[]>()

    // 建立任务映射
    tasks.forEach(task => {
      taskMap.set(task.id, task)
    })

    // 找出根任务（没有上游依赖的任务）和建立父子关系
    tasks.forEach(task => {
      const hasUpstreamDependencies = task.dependencies && task.dependencies.length > 0

      if (!hasUpstreamDependencies) {
        rootTasks.push(task)
      }

      // 建立子任务映射（基于依赖关系）
      if (task.dependents && task.dependents.length > 0) {
        task.dependents.forEach(dependent => {
          const dependentId = typeof dependent === 'object' ? dependent.id : dependent
          const dependentTask = taskMap.get(dependentId)

          // 检查是否会形成循环依赖
          if (dependentTask && !wouldCreateCycle(task.id, dependentId, childrenMap)) {
            if (!childrenMap.has(task.id)) {
              childrenMap.set(task.id, [])
            }
            childrenMap.get(task.id)!.push(dependentTask)
          }
        })
      }
    })

    // 检查是否会形成循环依赖的辅助函数
    function wouldCreateCycle(parentId: number, childId: number, childrenMap: Map<number, Task[]>): boolean {
      const visited = new Set<number>()

      function hasPath(from: number, to: number): boolean {
        if (from === to) return true
        if (visited.has(from)) return false

        visited.add(from)
        const children = childrenMap.get(from) || []

        for (const child of children) {
          if (hasPath(child.id, to)) {
            return true
          }
        }

        return false
      }

      return hasPath(childId, parentId)
    }

    // 递归构建树节点（带循环依赖检测）
    const buildNode = (task: Task, level: number = 0, visited: Set<number> = new Set()): TaskNode => {
      // 防止无限递归：检查是否已访问过此任务
      if (visited.has(task.id)) {
        return {
          task,
          requirement,
          children: [], // 循环依赖时停止递归
          level
        }
      }

      // 添加当前任务到已访问集合
      const newVisited = new Set(visited)
      newVisited.add(task.id)

      // 限制最大深度，防止过深的递归
      const maxDepth = 10
      if (level >= maxDepth) {
        return {
          task,
          requirement,
          children: [], // 达到最大深度时停止递归
          level
        }
      }

      const children = childrenMap.get(task.id) || []
      return {
        task,
        requirement,
        children: children.map(child => buildNode(child, level + 1, newVisited)),
        level
      }
    }

    return rootTasks.map(task => buildNode(task))
  }, [])

  // 构建树形结构数据
  const treeData = useMemo(() => {
    try {
      // 按需求分组任务
      const requirementGroups = new Map<number, Task[]>()
      const orphanTasks: Task[] = []

      tasks.forEach(task => {
        if (task.requirementId) {
          if (!requirementGroups.has(task.requirementId)) {
            requirementGroups.set(task.requirementId, [])
          }
          requirementGroups.get(task.requirementId)!.push(task)
        } else {
          orphanTasks.push(task)
        }
      })

      // 为每个需求构建任务树
      const trees: TaskNode[] = []

      // 处理有需求的任务 - 按需求分组
      requirementGroups.forEach((groupTasks, requirementId) => {
        const requirement = requirements.find(r => r.id === requirementId)
        try {
          const taskTree = buildTaskTree(groupTasks, requirement)

          // 创建需求节点作为根节点
          if (taskTree.length > 0) {
            trees.push({
              task: null as any, // 需求节点不是任务
              requirement,
              children: taskTree,
              level: -1, // 需求级别为-1
              isRequirementNode: true
            })
          }
        } catch (error) {
          console.error(`构建需求 ${requirementId} 的任务树时出错:`, error)
          // 发生错误时，创建需求节点并将任务作为平铺列表显示
          const errorChildren = groupTasks.map(task => ({
            task,
            requirement,
            children: [],
            level: 0
          }))

          trees.push({
            task: null as any,
            requirement,
            children: errorChildren,
            level: -1,
            isRequirementNode: true
          })
        }
      })

      // 处理孤立任务（没有关联需求的任务）
      if (orphanTasks.length > 0) {
        try {
          const orphanTree = buildTaskTree(orphanTasks)

          // 为孤立任务创建一个虚拟需求分组
          if (orphanTree.length > 0) {
            trees.push({
              task: null as any,
              requirement: {
                id: -1,
                title: '未分配需求的任务',
                businessDescription: '',
                status: 'DRAFT',
                priorityImportance: 'MEDIUM',
                priorityUrgency: 'MEDIUM',
                creatorId: 0,
                createdAt: '',
                updatedAt: ''
              } as any,
              children: orphanTree,
              level: -1,
              isRequirementNode: true
            })
          }
        } catch (error) {
          console.error('构建孤立任务树时出错:', error)
          // 发生错误时，创建虚拟需求节点并将任务作为平铺列表显示
          const errorChildren = orphanTasks.map(task => ({
            task,
            requirement: undefined,
            children: [],
            level: 0
          }))

          trees.push({
            task: null as any,
            requirement: {
              id: -1,
              title: '未分配需求的任务',
              businessDescription: '',
              status: 'DRAFT',
              priorityImportance: 'MEDIUM',
              priorityUrgency: 'MEDIUM',
              creatorId: 0,
              createdAt: '',
              updatedAt: ''
            } as any,
            children: errorChildren,
            level: -1,
            isRequirementNode: true
          })
        }
      }

      return trees
    } catch (error) {
      console.error('构建树形数据时出错:', error)
      return []
    }
  }, [tasks, requirements, buildTaskTree])

  // 获取状态颜色
  const getStatusColor = (status: TaskStatus): string => {
    const colors: Record<TaskStatus, string> = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      REVIEW: 'warning',
      DONE: 'success',
      BLOCKED: 'error',
      TESTING: 'cyan'
    }
    return colors[status] || 'default'
  }

  // 获取状态图标
  const getStatusIcon = (status: TaskStatus) => {
    const icons: Record<TaskStatus, React.ReactElement> = {
      TODO: <ClockCircleOutlined />,
      IN_PROGRESS: <ExclamationCircleOutlined />,
      REVIEW: <EyeOutlined />,
      DONE: <CheckCircleOutlined />,
      BLOCKED: <ExclamationCircleOutlined />,
      TESTING: <CheckCircleOutlined />
    }
    return icons[status] || <ClockCircleOutlined />
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: Priority): string => {
    const colors = {
      HIGH: 'red',
      MEDIUM: 'orange',
      LOW: 'green'
    }
    return colors[priority] || 'default'
  }

  // 渲染横向任务流
  const renderTaskFlow = (nodes: TaskNode[]) => {
    const flows: React.ReactElement[] = []
    const processedNodes = new Set<number>()

    // 找出所有根任务（没有被其他任务依赖的任务）
    const allTaskIds = new Set(nodes.filter(n => n.task).map(n => n.task!.id))
    const dependentTaskIds = new Set<number>()

    // 收集所有被依赖的任务ID
    nodes.forEach(node => {
      if (node.task && node.children.length > 0) {
        node.children.forEach(child => {
          if (child.task) {
            dependentTaskIds.add(child.task.id)
          }
        })
      }
    })

    // 根任务 = 所有任务 - 被依赖的任务
    const rootTaskIds = new Set([...allTaskIds].filter(id => !dependentTaskIds.has(id)))

    // 为每个根任务构建完整的依赖流
    nodes.forEach((node, index) => {
      if (!node.task || !rootTaskIds.has(node.task.id) || processedNodes.has(node.task.id)) {
        return
      }

      const taskFlow = buildTaskFlow(node, processedNodes)
      if (taskFlow.length > 0) {
        flows.push(
          <div key={`root-flow-${node.task.id}-${index}`} className="task-flow-container">
            {taskFlow}
          </div>
        )
      }
    })

    // 如果还有未处理的任务（可能是孤立的或循环依赖的），也显示出来
    nodes.forEach((node, index) => {
      if (!node.task || processedNodes.has(node.task.id)) return

      const taskFlow = buildTaskFlow(node, processedNodes)
      if (taskFlow.length > 0) {
        flows.push(
          <div key={`orphan-flow-${node.task.id}-${index}`} className="task-flow-container">
            {taskFlow}
          </div>
        )
      }
    })

    return flows
  }

  // 构建单个任务流（支持多分支依赖）
  const buildTaskFlow = (startNode: TaskNode, processedNodes: Set<number>): React.ReactElement[] => {
    if (!startNode.task || processedNodes.has(startNode.task.id)) {
      return []
    }

    processedNodes.add(startNode.task.id)
    const flowElements: React.ReactElement[] = []

    // 添加当前任务卡片
    const taskCard = renderTaskCard(startNode, 0)
    if (taskCard) {
      flowElements.push(taskCard)
    }

    // 如果有子任务（依赖任务），处理所有分支
    if (startNode.children.length > 0) {
      // 如果只有一个子任务，继续线性流
      if (startNode.children.length === 1) {
        flowElements.push(
          <div key={`connector-${startNode.task.id}`} className="task-connection-line">
            <ArrowRightOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
          </div>
        )

        const childFlow = buildTaskFlow(startNode.children[0], processedNodes)
        flowElements.push(...childFlow)
      } else {
        // 多个子任务，创建分支结构
        const branches = startNode.children.map((childNode, index) => {
          if (!childNode.task || processedNodes.has(childNode.task.id)) {
            return null
          }

          const branchFlow = buildTaskFlow(childNode, processedNodes)
          if (branchFlow.length === 0) return null

          return (
            <div key={`branch-${childNode.task.id}-${index}`} className="task-branch">
              <div className="task-connection-line">
                <ArrowRightOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
              </div>
              <div className="task-flow-container">
                {branchFlow}
              </div>
            </div>
          )
        }).filter(Boolean)

        if (branches.length > 0) {
          flowElements.push(
            <div
              key={`branches-${startNode.task.id}`}
              className="task-branches-container"
              data-branch-count={branches.length}
              style={{ position: 'relative' }}
            >
              {branches}
            </div>
          )
        }
      }
    }

    return flowElements
  }

  // 渲染单个任务卡片
  const renderTaskCard = (node: TaskNode, index: number): React.ReactElement | null => {
    const { task } = node
    if (!task) return null

    return (
      <div key={`task-card-${task.id}-${index}`} className="task-card-container">
        <Card
          className="task-card"
          hoverable
          bodyStyle={{ padding: 0 }}
          style={{
            borderLeft: `4px solid ${getPriorityColor(task.priorityImportance)}`,
          }}
        >
          <div className="task-card-content">
            <div className="task-info">
              <div className="task-title">
                <div className="task-title-text">
                  {task.title}
                </div>
                <Tag
                  className="task-status-tag"
                  color={getStatusColor(task.status)}
                  icon={getStatusIcon(task.status)}
                  style={{ fontSize: '11px' }}
                >
                  {task.status}
                </Tag>
              </div>

              <div className="task-meta">
                <Tag color={getPriorityColor(task.priorityImportance)} style={{ fontSize: '11px' }}>
                  重要: {task.priorityImportance}
                </Tag>
                <Tag color={getPriorityColor(task.priorityUrgency)} style={{ fontSize: '11px' }}>
                  紧急: {task.priorityUrgency}
                </Tag>
              </div>

              {task.description && (
                <div className="task-description">
                  {task.description}
                </div>
              )}
            </div>

            <div className="task-footer">
              <div className="task-assignee">
                {task.assignee ? `负责人: ${task.assignee.nickname || task.assignee.username}` : '未分配'}
                {task.dueDate && (
                  <div>截止: {task.dueDate.split('T')[0]}</div>
                )}
              </div>

              <div className="task-actions">
                <Tooltip title="查看详情">
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => onTaskView(task)}
                  />
                </Tooltip>
                <Tooltip title="编辑">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => onTaskEdit(task)}
                  />
                </Tooltip>
                <Tooltip title="删除">
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => onTaskDelete(task)}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  // 渲染任务节点（现在只处理需求节点）
  const renderTaskNode = (node: TaskNode, index: number) => {
    const { requirement, children, isRequirementNode } = node

    // 如果是需求节点，只渲染需求标题和子任务
    if (isRequirementNode && requirement) {
      return (
        <div key={`req-${requirement.id}-${index}`} className="requirement-group">
          <div className="requirement-header">
            <ProjectOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              {requirement.title}
            </Title>
            <Tag color="blue" style={{ marginLeft: 12 }}>
              {children.length} 个任务
            </Tag>
          </div>

          {/* 渲染该需求下的所有任务 */}
          <div className="requirement-tasks">
            {renderTaskFlow(children)}
          </div>
        </div>
      )
    }

    return null
  }

  if (loading) {
    return <Card loading />
  }

  if (treeData.length === 0) {
    return (
      <Card>
        <Empty description="暂无任务数据" />
      </Card>
    )
  }

  return (
    <div className="task-tree-view">
      {treeData.map((node, index) => renderTaskNode(node, index))}
    </div>
  )
}

export default TaskTreeView
