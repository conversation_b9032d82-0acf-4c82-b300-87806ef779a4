import React from 'react'
import { Card, Tag, Typography, Space, Tooltip } from 'antd'
import { 
  UserOutlined, 
  CalendarOutlined, 
  ClockCircleOutlined,
  FireOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useDrag } from 'react-dnd'
import type { Task } from '../types'
import dayjs from 'dayjs'

const { Text } = Typography

interface DraggableTaskCardProps {
  task: Task
  onClick?: (task: Task) => void
}

const DraggableTaskCard: React.FC<DraggableTaskCardProps> = ({
  task,
  onClick
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'task',
    item: { taskId: task.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const getStatusColor = (status: string) => {
    const colors = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      BLOCKED: 'warning',
      REVIEW: 'purple',
      TESTING: 'cyan',
      DONE: 'success'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusText = (status: string) => {
    const texts = {
      TODO: '待开始',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审中',
      TESTING: '测试中',
      DONE: '已完成'
    }
    return texts[status as keyof typeof texts] || status
  }

  const getTaskTypeColor = (type: string) => {
    const colors = {
      DESIGN: 'purple',
      DEVELOPMENT: 'blue',
      TESTING: 'orange',
      DEPLOYMENT: 'green',
      DOCUMENTATION: 'cyan',
      RESEARCH: 'magenta'
    }
    return colors[type as keyof typeof colors] || 'default'
  }

  const getTaskTypeText = (type: string) => {
    const texts = {
      DESIGN: '设计',
      DEVELOPMENT: '开发',
      TESTING: '测试',
      DEPLOYMENT: '部署',
      DOCUMENTATION: '文档',
      RESEARCH: '调研'
    }
    return texts[type as keyof typeof texts] || type
  }

  const getPriorityIcon = (importance: string, urgency: string) => {
    if (importance === 'HIGH' && urgency === 'HIGH') {
      return <FireOutlined style={{ color: '#ff4d4f' }} />
    }
    if (importance === 'HIGH' || urgency === 'HIGH') {
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    }
    return null
  }

  const isOverdue = task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'DONE'

  return (
    <div
      ref={drag}
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move',
        transition: 'opacity 0.3s ease'
      }}
    >
      <Card
        size="small"
        style={{
          marginBottom: 8,
          border: isOverdue ? '1px solid #ff4d4f' : undefined,
          boxShadow: isDragging ? '0 4px 12px rgba(0,0,0,0.15)' : undefined,
          cursor: onClick ? 'pointer' : 'move'
        }}
        styles={{
          body: { padding: '8px 12px' }
        }}
        onClick={(e) => {
          e.stopPropagation()
          onClick?.(task)
        }}
      >
        <div style={{ marginBottom: 8 }}>
          <div style={{ 
            fontSize: 13, 
            fontWeight: 500, 
            marginBottom: 4,
            lineHeight: '1.4',
            display: 'flex',
            alignItems: 'center',
            gap: 8
          }}>
            {getPriorityIcon(task.priorityImportance, task.priorityUrgency)}
            <Text ellipsis={{ tooltip: task.title }}>
              {task.title}
            </Text>
          </div>

          {task.description && (
            <Text 
              type="secondary" 
              style={{ 
                fontSize: 12,
                display: 'block',
                marginBottom: 8,
                lineHeight: '1.4'
              }}
              ellipsis={{ tooltip: task.description }}
            >
              {task.description}
            </Text>
          )}

          <Space size="small" wrap>
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
            
            <Tag color={getTaskTypeColor(task.taskType)}>
              {getTaskTypeText(task.taskType)}
            </Tag>

            {isOverdue && (
              <Tag color="red">
                已逾期
              </Tag>
            )}
          </Space>

          {/* 负责人和截止日期 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 8,
            fontSize: 11,
            color: '#999'
          }}>
            {/* 负责人 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              {task.assignee ? (
                <Tooltip title={`负责人: ${task.assignee.nickname || task.assignee.username}`}>
                  <Tag
                    icon={<UserOutlined />}
                    style={{
                      fontSize: 10,
                      padding: '0 4px',
                      margin: 0,
                      borderRadius: 4,
                      lineHeight: '16px'
                    }}
                  >
                    {task.assignee.nickname || task.assignee.username}
                  </Tag>
                </Tooltip>
              ) : (
                <Tag
                  color="default"
                  style={{
                    fontSize: 10,
                    padding: '0 4px',
                    margin: 0,
                    borderRadius: 4,
                    lineHeight: '16px'
                  }}
                >
                  未分配
                </Tag>
              )}
            </div>

            {/* 截止日期和工时 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              {task.dueDate && (
                <Tooltip title={`截止时间: ${dayjs(task.dueDate).format('YYYY-MM-DD HH:mm')}`}>
                  <Tag
                    color={isOverdue ? 'error' : 'default'}
                    icon={<CalendarOutlined />}
                    style={{
                      fontSize: 10,
                      padding: '0 4px',
                      margin: 0,
                      borderRadius: 4,
                      lineHeight: '16px'
                    }}
                  >
                    {dayjs(task.dueDate).format('MM-DD HH:mm')}
                  </Tag>
                </Tooltip>
              )}

              {task.estimatedHours && (
                <Tooltip title={`预估工时: ${task.estimatedHours}小时`}>
                  <Tag
                    color="blue"
                    icon={<ClockCircleOutlined />}
                    style={{
                      fontSize: 10,
                      padding: '0 4px',
                      margin: 0,
                      borderRadius: 4,
                      lineHeight: '16px'
                    }}
                  >
                    {task.estimatedHours}h
                  </Tag>
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default DraggableTaskCard
