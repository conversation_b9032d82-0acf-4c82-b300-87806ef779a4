import React from 'react'
import { Card, Empty, Space } from 'antd'
import {
  ClockCircleOutlined,
  PlayCircleOutlined,
  StopOutlined,
  EyeOutlined,
  BugOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import { useDrop } from 'react-dnd'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { taskApi } from '../services/api'
import type { Task, KanbanData } from '../types'
import DraggableTaskCard from './DraggableTaskCard'



interface KanbanColumnProps {
  title: string
  subtitle: string
  tasks: Task[]
  status: string
  color: string
  icon: React.ReactNode
  onTaskMove: (taskId: number, newStatus: string) => void
  onTaskClick?: (task: Task) => void
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({
  title,
  subtitle,
  tasks,
  status,
  color,
  icon,
  onTaskMove,
  onTaskClick
}) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'task',
    drop: (item: { taskId: number }) => {
      onTaskMove(item.taskId, status)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  })

  return (
    <div
      ref={drop}
      style={{
        height: '100%',
        width: '100%',
        opacity: isOver ? 0.8 : 1,
        backgroundColor: isOver ? '#f0f0f0' : 'transparent',
        transition: 'all 0.3s ease',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ color }}>{icon}</span>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{title}</div>
              <div style={{ fontSize: '12px', color: '#666', fontWeight: 'normal' }}>
                {subtitle}
              </div>
            </div>
          </div>
        }
        extra={
          <span style={{
            color: '#666',
            fontSize: '12px'
          }}>
            {tasks.length} 个任务
          </span>
        }
        style={{
          height: '100%',
          border: `2px solid ${isOver ? color : '#f0f0f0'}`,
          transition: 'border-color 0.3s ease'
        }}
        styles={{
          body: {
            padding: '12px',
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            minHeight: 0
          }
        }}
      >
        {tasks.length === 0 ? (
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center' 
          }}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无任务"
            />
          </div>
        ) : (
          <div style={{ 
            flex: 1, 
            overflow: 'auto',
            paddingRight: '2px'
          }}>
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {tasks.map((task) => (
                <DraggableTaskCard
                  key={task.id}
                  task={task}
                  onClick={onTaskClick}
                />
              ))}
            </Space>
          </div>
        )}
      </Card>
    </div>
  )
}

interface TaskKanbanProps {
  data: KanbanData
  onTaskClick?: (task: Task) => void
}

const TaskKanban: React.FC<TaskKanbanProps> = ({ data, onTaskClick }) => {
  const queryClient = useQueryClient()

  // 任务状态更新
  const updateTaskStatusMutation = useMutation({
    mutationFn: ({ taskId, status }: { taskId: number; status: string }) =>
      taskApi.updateTaskStatus(taskId, { status: status as any }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kanbanData'] })
    },
  })

  const handleTaskMove = (taskId: number, newStatus: string) => {
    updateTaskStatusMutation.mutate({ taskId, status: newStatus })
  }

  // 看板列配置
  const columns = [
    {
      title: '待办',
      subtitle: '等待开始的任务',
      tasks: data.todo,
      status: 'TODO',
      color: '#8c8c8c',
      icon: <ClockCircleOutlined />,
    },
    {
      title: '进行中',
      subtitle: '正在处理的任务',
      tasks: data.inProgress,
      status: 'IN_PROGRESS',
      color: '#1890ff',
      icon: <PlayCircleOutlined />,
    },
    {
      title: '阻塞',
      subtitle: '遇到问题的任务',
      tasks: data.blocked,
      status: 'BLOCKED',
      color: '#ff4d4f',
      icon: <StopOutlined />,
    },
    {
      title: '评审',
      subtitle: '等待评审的任务',
      tasks: data.review,
      status: 'REVIEW',
      color: '#fa8c16',
      icon: <EyeOutlined />,
    },
    {
      title: '测试',
      subtitle: '正在测试的任务',
      tasks: data.testing,
      status: 'TESTING',
      color: '#722ed1',
      icon: <BugOutlined />,
    },
    {
      title: '完成',
      subtitle: '已完成的任务',
      tasks: data.done,
      status: 'DONE',
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
    },
  ]

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(6, 1fr)',
        gap: '16px',
        height: '800px',
        width: '100%'
      }}
    >
      {columns.map((column, index) => (
        <KanbanColumn
          key={index}
          {...column}
          onTaskMove={handleTaskMove}
          onTaskClick={onTaskClick}
        />
      ))}
    </div>
  )
}

export default TaskKanban
