import React from 'react'
import type { Task } from '../types'
import { MdIconButton } from '../utils/material-imports'

interface Material3TaskCardProps {
  task: Task
  onClick?: () => void
  onEdit?: () => void
  onDelete?: () => void
  compact?: boolean
  showActions?: boolean
}

const Material3TaskCard: React.FC<Material3TaskCardProps> = ({
  task,
  onClick,
  onEdit,
  onDelete,
  compact = false,
  showActions = true
}) => {
  // 获取状态颜色
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      TODO: 'var(--md-sys-color-outline)',
      IN_PROGRESS: 'var(--md-sys-color-primary)',
      BLOCKED: 'var(--md-sys-color-error)',
      REVIEW: 'var(--md-sys-color-tertiary)',
      TESTING: 'var(--md-sys-color-secondary)',
      DONE: 'var(--md-sys-color-outline-variant)'
    }
    return colors[status] || 'var(--md-sys-color-outline)'
  }

  // 获取状态文本
  const getStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      TODO: '待办',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审',
      TESTING: '测试',
      DONE: '完成'
    }
    return statusMap[status] || status
  }

  // 获取优先级颜色
  const getPriorityColor = (importance: string): string => {
    if (importance === 'HIGH') return 'var(--md-sys-color-error)'
    if (importance === 'MEDIUM') return 'var(--md-sys-color-tertiary)'
    return 'var(--md-sys-color-outline)'
  }

  // 获取优先级文本
  const getPriorityText = (importance: string): string => {
    if (importance === 'HIGH') return '高'
    if (importance === 'MEDIUM') return '中'
    return '低'
  }

  // 获取优先级图标
  const getPriorityIcon = (importance: string): string => {
    if (importance === 'HIGH') return '🔥'
    if (importance === 'MEDIUM') return '⚡'
    return '📌'
  }

  // 检查是否过期
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'DONE'

  return (
    <div
      className={`md-surface-container ${compact ? 'md-elevation-1' : 'md-elevation-2'}`}
      style={{
        padding: compact ? '12px 16px' : '16px 20px',
        borderRadius: 'var(--md-sys-shape-corner-large)',
        marginBottom: compact ? '8px' : '16px',
        cursor: onClick ? 'pointer' : 'default',
        border: isOverdue ? `1px solid var(--md-sys-color-error)` : 'none',
        backgroundColor: isOverdue 
          ? 'var(--md-sys-color-error-container)' 
          : task.status === 'DONE' 
          ? 'var(--md-sys-color-surface-container-low)' 
          : 'var(--md-sys-color-surface-container)',
        transition: 'all 0.2s cubic-bezier(0.2, 0, 0, 1)',
        position: 'relative',
        overflow: 'hidden'
      }}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.transform = 'translateY(-2px)'
          e.currentTarget.style.boxShadow = '0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.12)'
        }
      }}
      onMouseLeave={(e) => {
        if (onClick) {
          e.currentTarget.style.transform = 'translateY(0)'
          e.currentTarget.style.boxShadow = compact 
            ? '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)'
            : '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)'
        }
      }}
    >
      {/* 状态指示条 */}
      <div
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: '4px',
          backgroundColor: getStatusColor(task.status),
          borderRadius: '0 2px 2px 0'
        }}
      />

      {/* 任务标题和优先级图标 */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'flex-start', 
        justifyContent: 'space-between',
        marginBottom: '8px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flex: 1 }}>
          <span style={{ fontSize: '16px' }}>
            {getPriorityIcon(task.priorityImportance)}
          </span>
          <h3 
            className={compact ? 'md-typescale-title-small' : 'md-typescale-title-medium'}
            style={{ 
              margin: 0,
              color: task.status === 'DONE' 
                ? 'var(--md-sys-color-on-surface-variant)' 
                : 'var(--md-sys-color-on-surface)',
              textDecoration: task.status === 'DONE' ? 'line-through' : 'none',
              opacity: task.status === 'DONE' ? 0.7 : 1
            }}
          >
            {task.title}
          </h3>
        </div>
        
        {showActions && (
          <div style={{ display: 'flex', gap: '4px' }}>
            {onEdit && (
              <MdIconButton
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation()
                  onEdit()
                }}
                style={{ '--md-icon-button-icon-size': '18px' } as React.CSSProperties}
              >
                ✏️
              </MdIconButton>
            )}
            {onDelete && (
              <MdIconButton
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation()
                  onDelete()
                }}
                style={{ '--md-icon-button-icon-size': '18px' } as React.CSSProperties}
              >
                🗑️
              </MdIconButton>
            )}
          </div>
        )}
      </div>

      {/* 任务描述 */}
      {task.description && (
        <p 
          className="md-typescale-body-small"
          style={{ 
            margin: '8px 0',
            color: 'var(--md-sys-color-on-surface-variant)',
            maxHeight: compact ? '32px' : '48px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: compact ? 2 : 3,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {task.description}
        </p>
      )}

      {/* 标签区域 */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', marginBottom: '12px' }}>
        {/* 状态标签 */}
        <span
          className="md-typescale-label-small"
          style={{
            padding: '4px 12px',
            borderRadius: 'var(--md-sys-shape-corner-full)',
            backgroundColor: getStatusColor(task.status),
            color: 'var(--md-sys-color-surface)',
            fontSize: '11px',
            fontWeight: '500'
          }}
        >
          {getStatusText(task.status)}
        </span>

        {/* 优先级标签 */}
        <span
          className="md-typescale-label-small"
          style={{
            padding: '4px 12px',
            borderRadius: 'var(--md-sys-shape-corner-full)',
            backgroundColor: getPriorityColor(task.priorityImportance),
            color: 'var(--md-sys-color-surface)',
            fontSize: '11px',
            fontWeight: '500'
          }}
        >
          🏷️ {getPriorityText(task.priorityImportance)}
        </span>

        {/* 任务类型标签 */}
        {task.taskType && (
          <span
            className="md-typescale-label-small"
            style={{
              padding: '4px 12px',
              borderRadius: 'var(--md-sys-shape-corner-full)',
              backgroundColor: 'var(--md-sys-color-secondary-container)',
              color: 'var(--md-sys-color-on-secondary-container)',
              fontSize: '11px',
              fontWeight: '500'
            }}
          >
            📋 {task.taskType}
          </span>
        )}

        {/* 依赖标签 */}
        {task.dependencies && task.dependencies.length > 0 && (
          <span
            className="md-typescale-label-small"
            style={{
              padding: '4px 12px',
              borderRadius: 'var(--md-sys-shape-corner-full)',
              backgroundColor: 'var(--md-sys-color-tertiary-container)',
              color: 'var(--md-sys-color-on-tertiary-container)',
              fontSize: '11px',
              fontWeight: '500'
            }}
          >
            🔗 {task.dependencies.length}个依赖
          </span>
        )}
      </div>

      {/* 底部信息 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        fontSize: '11px',
        color: 'var(--md-sys-color-on-surface-variant)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {/* 负责人 */}
          {task.assignee && (
            <span className="md-typescale-label-small">
              👤 {task.assignee.nickname || task.assignee.username}
            </span>
          )}
          
          {/* 预估工时 */}
          {task.estimatedHours && (
            <span className="md-typescale-label-small">
              ⏱️ {task.estimatedHours}h
            </span>
          )}
        </div>

        {/* 截止时间 */}
        {task.dueDate && (
          <span 
            className="md-typescale-label-small"
            style={{
              color: isOverdue ? 'var(--md-sys-color-error)' : 'var(--md-sys-color-on-surface-variant)',
              fontWeight: isOverdue ? '500' : '400'
            }}
          >
            📅 {new Date(task.dueDate).toLocaleDateString()}
            {isOverdue && ' (已过期)'}
          </span>
        )}
      </div>

      {/* 需求信息 */}
      {task.requirement && (
        <div style={{ 
          marginTop: '12px',
          padding: '8px 12px',
          backgroundColor: 'var(--md-sys-color-surface-container-high)',
          borderRadius: 'var(--md-sys-shape-corner-medium)',
          border: `1px solid var(--md-sys-color-outline-variant)`
        }}>
          <span className="md-typescale-label-small" style={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontWeight: '500'
          }}>
            📋 需求: {task.requirement.title}
          </span>
        </div>
      )}

      {/* 过期警告 */}
      {isOverdue && (
        <div style={{
          position: 'absolute',
          top: '8px',
          right: '8px',
          fontSize: '16px',
          animation: 'pulse 2s infinite'
        }}>
          ⚠️
        </div>
      )}
    </div>
  )
}

export default Material3TaskCard
