import React from 'react'
import { Card, Typography, Tag, Space, Empty } from 'antd'
import {
  FireOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import { useDrop } from 'react-dnd'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { taskApi } from '../services/api'
import type { Task, Priority, QuadrantUpdateRequest } from '../types'
import DraggableTaskCard from './DraggableTaskCard'

const { Title, Text } = Typography

interface QuadrantProps {
  title: string
  subtitle: string
  tasks: Task[]
  color: string
  icon: React.ReactNode
  importance: Priority
  urgency: Priority
  onTaskMove: (taskId: number, newImportance: Priority, newUrgency: Priority) => void
}

const QuadrantCard: React.FC<QuadrantProps> = ({
  title,
  subtitle,
  tasks,
  color,
  icon,
  importance,
  urgency,
  onTaskMove
}) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'task',
    drop: (item: { taskId: number }) => {
      onTaskMove(item.taskId, importance, urgency)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  })



  return (
    <div
      ref={drop}
      style={{
        minHeight: '400px',
        opacity: isOver ? 0.8 : 1,
        backgroundColor: isOver ? '#f0f0f0' : 'transparent',
        transition: 'all 0.3s ease'
      }}
    >
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ color }}>{icon}</span>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{title}</div>
              <div style={{ fontSize: '12px', color: '#666', fontWeight: 'normal' }}>
                {subtitle}
              </div>
            </div>
          </div>
        }
        extra={
          <Tag color={color}>
            {tasks.length} 个任务
          </Tag>
        }
        style={{ 
          height: '100%',
          border: `2px solid ${isOver ? color : '#f0f0f0'}`,
          transition: 'border-color 0.3s ease'
        }}
        bodyStyle={{ padding: '12px' }}
      >
        {tasks.length === 0 ? (
          <Empty 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无任务"
            style={{ margin: '20px 0' }}
          />
        ) : (
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            {tasks.map((task) => (
              <DraggableTaskCard
                key={task.id}
                task={task}
              />
            ))}
          </Space>
        )}
      </Card>
    </div>
  )
}

interface QuadrantMatrixProps {
  data: {
    urgentImportant: Task[]
    notUrgentImportant: Task[]
    urgentNotImportant: Task[]
    notUrgentNotImportant: Task[]
  }
}

const QuadrantMatrix: React.FC<QuadrantMatrixProps> = ({ data }) => {
  const queryClient = useQueryClient()
  
  const updateQuadrantMutation = useMutation({
    mutationFn: taskApi.updateTaskQuadrant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quadrantData'] })
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
    },
  })

  const handleTaskMove = (taskId: number, newImportance: Priority, newUrgency: Priority) => {
    const updateData: QuadrantUpdateRequest = {
      taskId,
      newImportance,
      newUrgency
    }
    updateQuadrantMutation.mutate(updateData)
  }

  const quadrants = [
    {
      title: '紧急且重要',
      subtitle: '立即处理 - 危机处理',
      tasks: data.urgentImportant,
      color: '#ff4d4f',
      icon: <FireOutlined />,
      importance: 'HIGH' as Priority,
      urgency: 'HIGH' as Priority,
    },
    {
      title: '不紧急但重要',
      subtitle: '计划处理 - 重点关注',
      tasks: data.notUrgentImportant,
      color: '#1890ff',
      icon: <ClockCircleOutlined />,
      importance: 'HIGH' as Priority,
      urgency: 'LOW' as Priority,
    },
    {
      title: '紧急但不重要',
      subtitle: '委托处理 - 减少干扰',
      tasks: data.urgentNotImportant,
      color: '#faad14',
      icon: <ExclamationCircleOutlined />,
      importance: 'LOW' as Priority,
      urgency: 'HIGH' as Priority,
    },
    {
      title: '不紧急不重要',
      subtitle: '有空处理 - 避免浪费',
      tasks: data.notUrgentNotImportant,
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
      importance: 'LOW' as Priority,
      urgency: 'LOW' as Priority,
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={3}>四象限任务管理</Title>
        <Text type="secondary">
          基于艾森豪威尔矩阵，按重要性和紧急程度对任务进行分类管理。拖拽任务卡片可以调整优先级。
        </Text>
      </div>
      
      <div 
        style={{ 
          display: 'grid', 
          gridTemplateColumns: '1fr 1fr',
          gap: '16px',
          minHeight: '600px'
        }}
      >
        {quadrants.map((quadrant, index) => (
          <QuadrantCard
            key={index}
            {...quadrant}
            onTaskMove={handleTaskMove}
          />
        ))}
      </div>

      <div style={{ marginTop: 24 }}>
        <Card size="small">
          <Title level={5}>使用说明</Title>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li><strong>紧急且重要</strong>：需要立即处理的危机和紧急问题</li>
            <li><strong>不紧急但重要</strong>：重要的长期目标和计划，需要重点关注</li>
            <li><strong>紧急但不重要</strong>：可以委托他人处理的紧急事务</li>
            <li><strong>不紧急不重要</strong>：可以延后或取消的低价值活动</li>
          </ul>
        </Card>
      </div>
    </div>
  )
}

export default QuadrantMatrix
