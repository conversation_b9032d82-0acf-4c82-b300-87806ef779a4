import React from 'react'
import {
  Timeline,
  Card,
  Typography,
  Tag,
  Space,
  Avatar,
  Tooltip,
  Empty,
  Spin
} from 'antd'
import {
  UserOutlined,
  ClockCircleOutlined,
  SwapRightOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const { Text, Title } = Typography

interface StatusChangeLog {
  id: number
  requirementId: number
  requirementTitle: string
  fromStatus: string
  toStatus: string
  fromStatusLabel: string
  toStatusLabel: string
  changedBy: {
    id: number
    username: string
    nickname?: string
    avatar?: string
  }
  changedAt: string
  reason?: string
  metadata?: Record<string, any>
}

interface StatusChangeAuditLogProps {
  requirementId?: number
  limit?: number
  showTitle?: boolean
}

const StatusChangeAuditLog: React.FC<StatusChangeAuditLogProps> = ({
  requirementId,
  limit = 10,
  showTitle = true
}) => {
  // 获取状态变更日志
  const { data: logs, isLoading } = useQuery({
    queryKey: ['status-change-logs', requirementId, limit],
    queryFn: async (): Promise<StatusChangeLog[]> => {
      // TODO: 实现实际的状态变更日志 API
      // 目前返回空数组，等待后端实现
      return []
    }
  })

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      DRAFT: 'default',
      REVIEW: 'processing',
      APPROVED: 'success',
      IN_PROGRESS: 'warning',
      TESTING: 'purple',
      DELIVERED: 'success',
      BLOCKED: 'error',
      REJECTED: 'error'
    }
    return colorMap[status] || 'default'
  }

  const formatTime = (time: string) => {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
  }

  const getRelativeTime = (time: string) => {
    return dayjs(time).fromNow()
  }

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: 40 }}>
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!logs || logs.length === 0) {
    return (
      <Card>
        {showTitle && <Title level={4}>状态变更历史</Title>}
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无状态变更记录"
        />
      </Card>
    )
  }

  return (
    <Card>
      {showTitle && (
        <Title level={4} style={{ marginBottom: 16 }}>
          状态变更历史
        </Title>
      )}
      
      <Timeline
        items={logs.map(log => ({
          key: log.id,
          dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
          children: (
            <div>
              <div style={{ marginBottom: 8 }}>
                <Space>
                  <Avatar
                    size="small"
                    src={log.changedBy.avatar}
                    icon={<UserOutlined />}
                  />
                  <Text strong>
                    {log.changedBy.nickname || log.changedBy.username}
                  </Text>
                  <Text type="secondary">将状态从</Text>
                  <Tag color={getStatusColor(log.fromStatus)}>
                    {log.fromStatusLabel}
                  </Tag>
                  <SwapRightOutlined style={{ color: '#999' }} />
                  <Tag color={getStatusColor(log.toStatus)}>
                    {log.toStatusLabel}
                  </Tag>
                </Space>
              </div>
              
              {log.reason && (
                <div style={{ marginBottom: 8 }}>
                  <Text type="secondary">原因：</Text>
                  <Text>{log.reason}</Text>
                </div>
              )}
              
              {!requirementId && (
                <div style={{ marginBottom: 8 }}>
                  <Text type="secondary">需求：</Text>
                  <Text>{log.requirementTitle}</Text>
                </div>
              )}
              
              <div>
                <Tooltip title={formatTime(log.changedAt)}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {getRelativeTime(log.changedAt)}
                  </Text>
                </Tooltip>
              </div>
            </div>
          )
        }))}
      />
    </Card>
  )
}

export default StatusChangeAuditLog

// 状态变更统计组件（已移除模拟数据）
export const StatusChangeStatistics: React.FC = () => {
  return (
    <Card title="状态变更统计">
      <Empty
        description="状态变更统计功能暂未实现"
        style={{ padding: '40px 0' }}
      />
    </Card>
  )
}
