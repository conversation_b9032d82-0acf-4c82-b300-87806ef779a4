import React from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  message
} from 'antd'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { requirementApi, projectApi, userApi } from '../services/api'
import { statusApi } from '../services/statusApi'
import type {
  Requirement,
  RequirementUpdateRequest,
  User,
  RequirementStatusConfig
} from '../types'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Option } = Select

interface RequirementEditModalProps {
  visible: boolean
  requirement: Requirement | null
  onClose: () => void
  onSuccess?: () => void
}

const RequirementEditModal: React.FC<RequirementEditModalProps> = ({
  visible,
  requirement,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取项目选项
  const { data: projectOptions, isLoading: projectOptionsLoading, error: projectOptionsError } = useQuery({
    queryKey: ['project-options'],
    queryFn: projectApi.getProjectOptions
  })

  // 调试信息
  React.useEffect(() => {
    console.log('项目选项数据:', projectOptions)
    console.log('项目选项加载中:', projectOptionsLoading)
    console.log('项目选项错误:', projectOptionsError)
  }, [projectOptions, projectOptionsLoading, projectOptionsError])

  // 获取用户选项
  const { data: users, isLoading: usersLoading } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: userApi.getAllUsers
  })

  // 获取状态配置
  const { data: statusConfigs = [] } = useQuery<RequirementStatusConfig[]>({
    queryKey: ['requirement-status-configs'],
    queryFn: statusApi.getRequirementStatusConfigs
  })

  // 更新需求
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: RequirementUpdateRequest }) =>
      requirementApi.updateRequirement(id, data),
    onSuccess: () => {
      message.success('需求更新成功')
      onClose()
      form.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
      queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
      onSuccess?.()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新失败')
    },
  })

  // 删除需求
  const deleteMutation = useMutation({
    mutationFn: requirementApi.deleteRequirement,
    onSuccess: () => {
      message.success('需求删除成功')
      onClose()
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
      queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
      onSuccess?.()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除失败')
    },
  })

  // 处理更新需求
  const handleUpdateRequirement = async (values: any) => {
    if (!requirement) return

    const data: RequirementUpdateRequest = {
      ...values,
      expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
      actualDeliveryDate: values.actualDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
    }
    updateMutation.mutate({ id: requirement.id, data })
  }

  // 处理删除需求
  const handleDeleteRequirement = () => {
    if (!requirement) return
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除需求 "${requirement.title}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        deleteMutation.mutate(requirement.id)
      }
    })
  }

  // 当需求变化时，更新表单值
  React.useEffect(() => {
    if (requirement && visible) {
      const formValues = {
        ...requirement,
        projectId: requirement.projectId,
        expectedDeliveryDate: requirement.expectedDeliveryDate
          ? dayjs(requirement.expectedDeliveryDate)
          : undefined,
        actualDeliveryDate: requirement.actualDeliveryDate
          ? dayjs(requirement.actualDeliveryDate)
          : undefined,
      }
      console.log('设置表单值:', formValues)
      console.log('需求数据:', requirement)
      form.setFieldsValue(formValues)
    }
  }, [requirement, visible, form])

  return (
    <Modal
      title="编辑需求"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}

    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdateRequirement}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="title"
              label="需求标题"
              rules={[{ required: true, message: '请输入需求标题' }]}
            >
              <Input placeholder="请输入需求标题" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="projectId"
              label="所属项目"
              rules={[{ required: true, message: '请选择所属项目' }]}
            >
              <Select
                placeholder="请选择所属项目"
                loading={projectOptionsLoading}
                notFoundContent={projectOptionsLoading ? '加载中...' : '暂无项目'}
              >
                {projectOptions?.map((project) => (
                  <Option key={project.id} value={project.id}>
                    {project.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="businessDescription"
              label="业务描述"
              rules={[{ required: true, message: '请输入业务描述' }]}
            >
              <TextArea
                rows={4}
                placeholder="请输入面向业务人员的需求描述"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="acceptanceCriteria" label="验收标准">
              <TextArea
                rows={3}
                placeholder="请输入验收标准"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="status" label="状态">
              <Select>
                {statusConfigs.map(config => (
                  <Option key={config.key} value={config.key}>
                    {config.icon} {config.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="priorityImportance" label="重要程度">
              <Select>
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="priorityUrgency" label="紧急程度">
              <Select>
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="estimatedValue" label="预估价值">
              <Input placeholder="请输入预估价值或收益" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="targetUsers" label="目标用户">
              <Input placeholder="请输入目标用户群体" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="businessGoal" label="业务目标">
              <TextArea
                rows={2}
                placeholder="请输入业务目标"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="assigneeId" label="负责人">
              <Select placeholder="选择负责人" allowClear loading={usersLoading}>
                {users?.map((user) => (
                  <Option key={user.id} value={user.id}>
                    {user.nickname || user.username}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="expectedDeliveryDate" label="期望交付时间">
              <DatePicker
                showTime
                placeholder="选择期望交付时间"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="actualDeliveryDate" label="实际交付时间">
              <DatePicker
                showTime
                placeholder="选择实际交付时间"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button
              danger
              onClick={handleDeleteRequirement}
              loading={deleteMutation.isPending}
            >
              删除
            </Button>
            <Button onClick={onClose}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={updateMutation.isPending}
            >
              更新
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default RequirementEditModal
