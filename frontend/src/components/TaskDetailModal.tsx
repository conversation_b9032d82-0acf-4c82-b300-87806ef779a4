import React from 'react'
import {
  Modal,
  Descriptions,
  Tag,
  Space,
  List,
  Row,
  Col,
  Typography,
  Divider
} from 'antd'
import {
  ClockCircleOutlined,
  UserOutlined,
  FlagOutlined,
  LinkOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { Task, TaskDependency } from '../types'
import dayjs from 'dayjs'

const { Text, Title } = Typography

interface TaskDetailModalProps {
  task: Task | null
  visible: boolean
  onClose: () => void
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  task,
  visible,
  onClose
}) => {
  if (!task) return null

  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      REVIEW: 'warning',
      DONE: 'success',
      BLOCKED: 'error',
      TESTING: 'cyan'
    }
    return colors[status] || 'default'
  }

  const getStatusIcon = (status: string) => {
    const icons: Record<string, React.ReactElement> = {
      TODO: <ClockCircleOutlined />,
      IN_PROGRESS: <ExclamationCircleOutlined />,
      REVIEW: <ExclamationCircleOutlined />,
      DONE: <ClockCircleOutlined />,
      BLOCKED: <ExclamationCircleOutlined />,
      TESTING: <ClockCircleOutlined />
    }
    return icons[status] || <ClockCircleOutlined />
  }

  const getPriorityColor = (priority: string): string => {
    const colors: Record<string, string> = {
      HIGH: 'red',
      MEDIUM: 'orange',
      LOW: 'green'
    }
    return colors[priority] || 'default'
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '未设置'
    return dayjs(dateString).format('YYYY-MM-DD HH:mm')
  }

  return (
    <Modal
      title={
        <Space>
          <FlagOutlined />
          <span>任务详情</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 基本信息 */}
        <Descriptions
          title="基本信息"
          bordered
          column={2}
          size="small"
          style={{ marginBottom: 24 }}
        >
          <Descriptions.Item label="任务标题" span={2}>
            <Text strong style={{ fontSize: 16 }}>
              {task.title}
            </Text>
          </Descriptions.Item>
          
          <Descriptions.Item label="任务状态">
            <Tag color={getStatusColor(task.status)} icon={getStatusIcon(task.status)}>
              {task.status}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="任务ID">
            <Text code>#{task.id}</Text>
          </Descriptions.Item>
          
          <Descriptions.Item label="重要程度">
            <Tag color={getPriorityColor(task.priorityImportance)}>
              {task.priorityImportance}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="紧急程度">
            <Tag color={getPriorityColor(task.priorityUrgency)}>
              {task.priorityUrgency}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="负责人">
            <Space>
              <UserOutlined />
              <Text>
                {task.assignee
                  ? `${task.assignee.nickname || task.assignee.username}`
                  : '未分配'
                }
              </Text>
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item label="创建人">
            <Space>
              <UserOutlined />
              <Text>
                {task.creator 
                  ? `${task.creator.nickname || task.creator.username}`
                  : '未知'
                }
              </Text>
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item label="创建时间">
            <Text>{formatDate(task.createdAt)}</Text>
          </Descriptions.Item>
          
          <Descriptions.Item label="更新时间">
            <Text>{formatDate(task.updatedAt)}</Text>
          </Descriptions.Item>
        </Descriptions>

        {/* 任务描述 */}
        {task.description && (
          <>
            <Title level={5}>任务描述</Title>
            <div 
              style={{ 
                background: '#fafafa', 
                padding: 16, 
                borderRadius: 6,
                marginBottom: 24,
                whiteSpace: 'pre-wrap'
              }}
            >
              <Text>{task.description}</Text>
            </div>
          </>
        )}

        {/* 依赖关系 */}
        <Row gutter={24}>
          {/* 前置任务 */}
          <Col span={12}>
            <Title level={5}>
              <LinkOutlined /> 前置任务
            </Title>
            {task.dependencies && task.dependencies.length > 0 ? (
              <List
                size="small"
                bordered
                dataSource={task.dependencies}
                renderItem={(dep: TaskDependency) => (
                  <List.Item>
                    <Space>
                      <Text code>#{dep.dependsOnTaskId}</Text>
                      <Text>{dep.dependsOnTask?.title || '未知任务'}</Text>
                    </Space>
                  </List.Item>
                )}
              />
            ) : (
              <Text type="secondary">无前置任务</Text>
            )}
          </Col>

          {/* 后续任务 */}
          <Col span={12}>
            <Title level={5}>
              <LinkOutlined /> 后续任务
            </Title>
            {task.dependents && task.dependents.length > 0 ? (
              <List
                size="small"
                bordered
                dataSource={task.dependents}
                renderItem={(dependent: TaskDependency) => {
                  // dependent.dependsOnTask 实际上是依赖当前任务的任务（后续任务）
                  // dependent.taskId 是后续任务的ID
                  // dependent.dependsOnTaskId 是当前任务的ID
                  console.log('后续任务依赖关系:', dependent)
                  return (
                    <List.Item>
                      <Space>
                        <Text code>#{dependent.taskId}</Text>
                        <Text>{dependent.dependsOnTask?.title || '未知任务'}</Text>
                        <Text type="secondary">({dependent.dependencyType})</Text>
                      </Space>
                    </List.Item>
                  )
                }}
              />
            ) : (
              <Text type="secondary">无后续任务</Text>
            )}
          </Col>
        </Row>

        {/* 关联需求 */}
        {task.requirement && (
          <>
            <Divider />
            <Title level={5}>关联需求</Title>
            <div 
              style={{ 
                background: '#f0f9ff', 
                padding: 16, 
                borderRadius: 6,
                border: '1px solid #d1ecf1'
              }}
            >
              <Space direction="vertical" size="small">
                <Text strong>{task.requirement.title}</Text>
                <Space>
                  <Tag color="blue">{task.requirement.status}</Tag>
                </Space>
              </Space>
            </div>
          </>
        )}
      </div>
    </Modal>
  )
}

export default TaskDetailModal
