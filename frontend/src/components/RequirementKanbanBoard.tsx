import React from 'react'
import { Card, Col, Row, Badge, Empty, Spin, Typography, Tag, Tooltip } from 'antd'
import { UserOutlined, CalendarOutlined } from '@ant-design/icons'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { useQuery } from '@tanstack/react-query'
import type { RequirementKanbanData, RequirementStatus, RequirementKanbanColumn, Requirement } from '../types'
// import { requirementStatusManager } from '../config/requirementStatus'
import { statusApi } from '../services/statusApi'
import dayjs from 'dayjs'

const { Text } = Typography

interface RequirementKanbanBoardProps {
  data: RequirementKanbanData | undefined
  loading?: boolean
  onRequirementMove?: (requirementId: number, newStatus: RequirementStatus) => void
  onRequirementClick?: (requirement: Requirement) => void
}

const RequirementKanbanBoard: React.FC<RequirementKanbanBoardProps> = ({
  data,
  loading,
  onRequirementMove,
  onRequirementClick
}) => {
  // 从后端获取状态配置
  const { data: statusConfigs, isLoading: isLoadingConfigs, error: configError } = useQuery({
    queryKey: ['requirement-status-configs'],
    queryFn: statusApi.getRequirementStatusConfigs,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 1, // 只重试一次
  })

  console.log('RequirementKanbanBoard 数据:', data)

  // 使用后端状态配置生成看板列配置
  const columns: RequirementKanbanColumn[] = React.useMemo(() => {
    if (!statusConfigs || statusConfigs.length === 0) {
      return []
    }

    // 只显示激活的状态，排除归档状态，并按 order 排序
    return statusConfigs
      .filter(config => config.isActive && config.key !== 'ARCHIVED')
      .sort((a, b) => a.order - b.order)
      .map(statusConfig => ({
        key: statusConfig.key,
        title: statusConfig.label,
        color: statusConfig.color,
        icon: statusConfig.icon,
        description: statusConfig.description,
        requirements: data?.[statusConfig.key] || []
      }))
  }, [statusConfigs, data])

  const handleDragEnd = (result: DropResult) => {
    console.log('拖拽结束事件:', result)

    const { destination, source, draggableId } = result

    // 如果没有目标位置，或者位置没有变化，则不处理
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      console.log('拖拽取消或无变化')
      return
    }

    // 验证 draggableId 格式
    if (!draggableId.startsWith('requirement-')) {
      console.error('无效的 draggableId 格式:', draggableId)
      return
    }

    // 直接使用 droppableId 作为状态
    const newStatus = destination.droppableId as RequirementStatus
    const requirementId = parseInt(draggableId.replace('requirement-', ''))

    console.log('拖拽更新:', {
      draggableId,
      requirementId,
      newStatus,
      sourceId: source.droppableId,
      destinationId: destination.droppableId
    })

    // 调用回调函数更新需求状态
    if (onRequirementMove && !isNaN(requirementId)) {
      onRequirementMove(requirementId, newStatus)
    } else {
      console.error('无法更新需求状态:', { requirementId, newStatus, onRequirementMove })
    }
  }

  // 如果状态配置或需求数据正在加载，显示加载状态
  if (loading || isLoadingConfigs) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          {isLoadingConfigs ? '正在加载状态配置...' : '正在加载需求数据...'}
        </div>
      </div>
    )
  }

  // 如果状态配置加载失败，显示错误信息
  if (configError) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Empty
          description="状态配置加载失败，请刷新页面重试"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    )
  }

  // 如果没有状态配置，显示空状态
  if (!statusConfigs || statusConfigs.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Empty
          description="暂无状态配置，请先在状态管理页面创建状态配置"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    )
  }

  return (
    <DragDropContext
      onDragEnd={handleDragEnd}
      onDragStart={(start) => {
        console.log('拖拽开始:', start)
      }}
      onDragUpdate={(update) => {
        console.log('拖拽更新:', update)
      }}
    >
      <Row gutter={[16, 16]}>
        {columns.map((column) => (
          <Col key={column.key} xs={24} sm={12} md={8} lg={4} xl={4} style={{ marginBottom: 16 }}>
            <Card
              title={
                <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span style={{ fontSize: 16 }}>{column.icon}</span>
                    <div
                      style={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: column.color
                      }}
                    />
                    <span style={{ fontSize: 14, fontWeight: 600 }}>{column.title}</span>
                    <Badge
                      count={column.requirements.length}
                      style={{ backgroundColor: column.color }}
                    />
                  </div>
                  <Text
                    type="secondary"
                    style={{ fontSize: 11, lineHeight: '14px' }}
                  >
                    {column.description}
                  </Text>
                </div>
              }
              size="small"
              styles={{
                body: {
                  padding: 8,
                  minHeight: window.innerWidth < 768 ? 300 : 500,
                  maxHeight: window.innerWidth < 768 ? 400 : 700,
                  overflow: 'auto'
                },
                header: {
                  padding: '8px 12px',
                  minHeight: 'auto'
                }
              }}
            >
              <Droppable key={column.key} droppableId={column.key}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      minHeight: 400,
                      backgroundColor: snapshot.isDraggingOver ? '#f0f0f0' : 'transparent',
                      borderRadius: 4,
                      padding: 4,
                      transition: 'background-color 0.2s ease'
                    }}
                  >
                    {column.requirements.length === 0 ? (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                          <span style={{ fontSize: 12, color: '#999' }}>
                            暂无需求
                          </span>
                        }
                        style={{ margin: '40px 0' }}
                      />
                    ) : (
                      column.requirements.map((requirement, index) => {
                        const dragId = `requirement-${requirement.id}`
                        console.log('渲染需求卡片:', {
                          id: requirement.id,
                          title: requirement.title,
                          index,
                          dragId,
                          columnStatus: column.key
                        })
                        return (
                          <Draggable
                            key={dragId}
                            draggableId={dragId}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                style={{
                                  ...provided.draggableProps.style,
                                  marginBottom: 8,
                                  opacity: snapshot.isDragging ? 0.8 : 1,
                                }}
                              >
                                <div
                                  style={{
                                    padding: 12,
                                    backgroundColor: '#fff',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: 6,
                                    cursor: 'pointer',
                                    boxShadow: snapshot.isDragging ? '0 4px 8px rgba(0,0,0,0.2)' : '0 1px 3px rgba(0,0,0,0.1)'
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onRequirementClick?.(requirement)
                                  }}
                                >
                                  {/* 标题 */}
                                  <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
                                    #{requirement.id} {requirement.title}
                                  </div>

                                  {/* 负责人和截止日期 */}
                                  <div style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    fontSize: 11,
                                    color: '#999'
                                  }}>
                                    {/* 负责人 */}
                                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                                      {requirement.assignee ? (
                                        <Tooltip title={`负责人: ${requirement.assignee.nickname || requirement.assignee.username}`}>
                                          <Tag
                                            icon={<UserOutlined />}
                                            style={{
                                              fontSize: 10,
                                              padding: '0 4px',
                                              margin: 0,
                                              borderRadius: 4,
                                              lineHeight: '16px'
                                            }}
                                          >
                                            {requirement.assignee.nickname || requirement.assignee.username}
                                          </Tag>
                                        </Tooltip>
                                      ) : (
                                        <Tag
                                          color="default"
                                          style={{
                                            fontSize: 10,
                                            padding: '0 4px',
                                            margin: 0,
                                            borderRadius: 4,
                                            lineHeight: '16px'
                                          }}
                                        >
                                          未分配
                                        </Tag>
                                      )}
                                    </div>

                                    {/* 截止日期 */}
                                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                                      {requirement.expectedDeliveryDate ? (
                                        (() => {
                                          const dueDate = dayjs(requirement.expectedDeliveryDate)
                                          const now = dayjs()
                                          const isOverdue = dueDate.isBefore(now) && requirement.status !== 'DELIVERED'
                                          const isToday = dueDate.isSame(now, 'day')
                                          const isTomorrow = dueDate.isSame(now.add(1, 'day'), 'day')

                                          let color = 'default'
                                          let text = dueDate.format('MM-DD')

                                          if (isOverdue) {
                                            color = 'error'
                                            text = `${text} 逾期`
                                          } else if (isToday) {
                                            color = 'warning'
                                            text = `${text} 今天`
                                          } else if (isTomorrow) {
                                            color = 'processing'
                                            text = `${text} 明天`
                                          }

                                          return (
                                            <Tooltip title={`截止日期: ${dueDate.format('YYYY-MM-DD HH:mm')}`}>
                                              <Tag
                                                color={color}
                                                icon={<CalendarOutlined />}
                                                style={{
                                                  fontSize: 10,
                                                  padding: '0 4px',
                                                  margin: 0,
                                                  borderRadius: 4,
                                                  lineHeight: '16px'
                                                }}
                                              >
                                                {text}
                                              </Tag>
                                            </Tooltip>
                                          )
                                        })()
                                      ) : (
                                        <Tag
                                          color="default"
                                          style={{
                                            fontSize: 10,
                                            padding: '0 4px',
                                            margin: 0,
                                            borderRadius: 4,
                                            lineHeight: '16px'
                                          }}
                                        >
                                          无截止日期
                                        </Tag>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        )
                      })
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </Card>
          </Col>
        ))}
      </Row>
    </DragDropContext>
  )
}

export default RequirementKanbanBoard
