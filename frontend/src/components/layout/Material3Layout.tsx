import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'
import {
  MdList,
  MdListItem,
  MdIconButton,
  MdFilledButton,
  MdTextButton,
  MdDivider,
  MdFab
} from '../../utils/material-imports'

interface Material3LayoutProps {
  children: React.ReactNode
}

const Material3Layout: React.FC<Material3LayoutProps> = ({ children }) => {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, clearAuth } = useAuthStore()

  const handleLogout = () => {
    clearAuth()
    navigate('/login')
  }

  const navigationItems = [
    {
      key: '/',
      icon: '🏠',
      label: '工作台',
      description: '项目概览和统计'
    },
    {
      key: '/requirements',
      icon: '📋',
      label: '需求管理',
      description: '管理项目需求'
    },
    {
      key: '/requirement-kanban',
      icon: '📊',
      label: '需求看板',
      description: '可视化需求流程'
    },
    {
      key: '/archived-requirements',
      icon: '📦',
      label: '已归档需求',
      description: '查看历史需求'
    },
    {
      key: '/tasks',
      icon: '✅',
      label: '任务管理',
      description: '管理项目任务'
    },
    {
      key: '/task-kanban',
      icon: '📈',
      label: '任务看板',
      description: '可视化任务流程'
    },
    {
      key: '/quadrant',
      icon: '🎯',
      label: '四象限',
      description: '优先级管理'
    },
    {
      key: '/weekly-plan',
      icon: '📅',
      label: '周计划',
      description: '制定周计划'
    },
    {
      key: '/weekly-report',
      icon: '📝',
      label: '周报',
      description: '生成工作报告'
    }
  ]

  const adminItems = [
    {
      key: '/projects',
      icon: '🗂️',
      label: '项目管理',
      description: '管理所有项目'
    },
    {
      key: '/users',
      icon: '👥',
      label: '用户管理',
      description: '管理系统用户'
    },
    {
      key: '/status-manage',
      icon: '⚙️',
      label: '状态管理',
      description: '配置状态流程'
    }
  ]

  const allItems = user?.userType === 'SUPER_ADMIN' 
    ? [...navigationItems, ...adminItems] 
    : navigationItems

  const handleNavigation = (path: string) => {
    navigate(path)
    setDrawerOpen(false)
  }

  return (
    <div className="md-surface" style={{ 
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: 'var(--md-sys-color-surface)'
    }}>
      {/* 导航抽屉 */}
      {drawerOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
            display: 'flex'
          }}
          onClick={() => setDrawerOpen(false)}
        >
          <div
            style={{
              width: '320px',
              height: '100%',
              backgroundColor: 'var(--md-sys-color-surface-container)',
              boxShadow: '0px 8px 32px rgba(0, 0, 0, 0.24)',
              display: 'flex',
              flexDirection: 'column',
              transform: drawerOpen ? 'translateX(0)' : 'translateX(-100%)',
              transition: 'transform 0.3s cubic-bezier(0.2, 0, 0, 1)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 抽屉头部 */}
            <div style={{
              padding: '24px 16px',
              borderBottom: '1px solid var(--md-sys-color-outline-variant)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: 'var(--md-sys-shape-corner-full)',
                  backgroundColor: 'var(--md-sys-color-primary)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px'
                }}>
                  💪
                </div>
                <div>
                  <h2 className="md-typescale-title-large" style={{ margin: 0 }}>
                    猛男项目管理
                  </h2>
                  <p className="md-typescale-body-small" style={{
                    margin: 0,
                    color: 'var(--md-sys-color-on-surface-variant)'
                  }}>
                    高效协作，精准管理
                  </p>
                </div>
              </div>
            </div>

            {/* 导航列表 */}
            <div style={{ padding: '8px 0', flex: 1, overflowY: 'auto' }}>
              {allItems.map((item) => (
                <div
                  key={item.key}
                  onClick={() => handleNavigation(item.key)}
                  style={{
                    cursor: 'pointer',
                    backgroundColor: location.pathname === item.key
                      ? 'var(--md-sys-color-secondary-container)'
                      : 'transparent',
                    color: location.pathname === item.key
                      ? 'var(--md-sys-color-on-secondary-container)'
                      : 'var(--md-sys-color-on-surface)',
                    margin: '4px 12px',
                    borderRadius: 'var(--md-sys-shape-corner-full)',
                    padding: '12px 16px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (location.pathname !== item.key) {
                      e.currentTarget.style.backgroundColor = 'var(--md-sys-color-surface-container-high)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (location.pathname !== item.key) {
                      e.currentTarget.style.backgroundColor = 'transparent'
                    }
                  }}
                >
                  <div style={{ fontSize: '20px' }}>
                    {item.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <div className="md-typescale-body-large" style={{ fontWeight: '500' }}>
                      {item.label}
                    </div>
                    <div className="md-typescale-body-small" style={{
                      color: 'var(--md-sys-color-on-surface-variant)',
                      marginTop: '2px'
                    }}>
                      {item.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div style={{
              height: '1px',
              backgroundColor: 'var(--md-sys-color-outline-variant)',
              margin: '8px 16px'
            }} />

            {/* 用户信息和退出 */}
            <div style={{ padding: '16px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px',
                backgroundColor: 'var(--md-sys-color-surface-container-high)',
                borderRadius: 'var(--md-sys-shape-corner-large)',
                marginBottom: '12px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: 'var(--md-sys-shape-corner-full)',
                  backgroundColor: 'var(--md-sys-color-primary)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'var(--md-sys-color-on-primary)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  {(user?.nickname || user?.username || 'U').charAt(0).toUpperCase()}
                </div>
                <div style={{ flex: 1 }}>
                  <div className="md-typescale-body-medium" style={{ fontWeight: '500' }}>
                    {user?.nickname || user?.username}
                  </div>
                  <div className="md-typescale-body-small" style={{
                    color: 'var(--md-sys-color-on-surface-variant)'
                  }}>
                    {user?.userType === 'SUPER_ADMIN' ? '超级管理员' : '普通用户'}
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '8px' }}>
                <MdTextButton
                  onClick={() => {
                    navigate('/profile')
                    setDrawerOpen(false)
                  }}
                  style={{ flex: 1 }}
                >
                  <span slot="icon">👤</span>
                  个人设置
                </MdTextButton>
                <MdTextButton
                  onClick={handleLogout}
                  style={{
                    flex: 1,
                    '--md-text-button-label-text-color': 'var(--md-sys-color-error)'
                  } as React.CSSProperties}
                >
                  <span slot="icon">🚪</span>
                  退出登录
                </MdTextButton>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 主内容区域 */}
      <div style={{ 
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh'
      }}>
        {/* 顶部应用栏 */}
        <div className="md-surface-container md-elevation-2" style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px 16px',
          gap: '16px',
          minHeight: '64px',
          position: 'sticky',
          top: 0,
          zIndex: 100
        }}>
          <MdIconButton
            onClick={() => setDrawerOpen(true)}
            style={{ '--md-icon-button-icon-size': '24px' } as React.CSSProperties}
          >
            ☰
          </MdIconButton>
          
          <div style={{ flex: 1 }}>
            <h1 className="md-typescale-title-large" style={{ margin: 0 }}>
              {allItems.find(item => item.key === location.pathname)?.label || '猛男项目管理'}
            </h1>
          </div>

          <MdIconButton
            onClick={() => navigate('/profile')}
            style={{ '--md-icon-button-icon-size': '24px' } as React.CSSProperties}
          >
            👤
          </MdIconButton>
        </div>

        {/* 页面内容 */}
        <main style={{ 
          flex: 1,
          padding: '24px',
          backgroundColor: 'var(--md-sys-color-surface)',
          minHeight: 'calc(100vh - 64px)'
        }}>
          {children}
        </main>

        {/* 浮动操作按钮 */}
        <MdFab
          style={{
            position: 'fixed',
            bottom: '24px',
            right: '24px',
            '--md-fab-container-color': 'var(--md-sys-color-primary-container)',
            '--md-fab-icon-color': 'var(--md-sys-color-on-primary-container)'
          } as React.CSSProperties}
          onClick={() => {
            // 根据当前页面决定FAB的行为
            if (location.pathname.includes('task')) {
              // 任务相关页面 - 创建任务
              console.log('创建任务')
            } else if (location.pathname.includes('requirement')) {
              // 需求相关页面 - 创建需求
              console.log('创建需求')
            } else {
              // 默认行为
              console.log('快速操作')
            }
          }}
        >
          <span slot="icon" style={{ fontSize: '24px' }}>➕</span>
        </MdFab>
      </div>
    </div>
  )
}

export default Material3Layout
