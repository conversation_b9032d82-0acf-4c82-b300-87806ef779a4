import React from 'react'
import { Card, Tag, Avatar, Tooltip, Space, Typography, Progress } from 'antd'
import { 
  UserOutlined, 
  CalendarOutlined, 
  FlagOutlined,
  DollarOutlined,
  TeamOutlined,
  BulbOutlined
} from '@ant-design/icons'
import type { Requirement } from '../types'
import { getFullAvatarUrl } from '../utils/url'
import dayjs from 'dayjs'

const { Text, Paragraph } = Typography

interface RequirementCardProps {
  requirement: Requirement
  onClick?: (requirement: Requirement) => void
}

// 优先级配置
const PRIORITY_CONFIG = {
  HIGH: { label: '高', color: 'red', icon: '🔥' },
  MEDIUM: { label: '中', color: 'orange', icon: '⚡' },
  LOW: { label: '低', color: 'green', icon: '📋' },
}

// 计算需求进度（基于关联任务）
const calculateProgress = (requirement: Requirement): number => {
  if (!requirement.tasks || requirement.tasks.length === 0) {
    return 0
  }
  
  const completedTasks = requirement.tasks.filter(task => 
    task.status === 'DONE'
  ).length
  
  return Math.round((completedTasks / requirement.tasks.length) * 100)
}

// 获取进度颜色
const getProgressColor = (progress: number): string => {
  if (progress >= 80) return '#52c41a'
  if (progress >= 50) return '#1890ff'
  if (progress >= 20) return '#faad14'
  return '#ff4d4f'
}

const RequirementCard: React.FC<RequirementCardProps> = ({ 
  requirement, 
  onClick 
}) => {
  const progress = calculateProgress(requirement)
  const importanceConfig = PRIORITY_CONFIG[requirement.priorityImportance]
  const urgencyConfig = PRIORITY_CONFIG[requirement.priorityUrgency]
  
  // 判断是否逾期
  const isOverdue = requirement.expectedDeliveryDate && 
    dayjs().isAfter(dayjs(requirement.expectedDeliveryDate)) &&
    requirement.status !== 'DELIVERED'

  return (
    <Card
      size="small"
      hoverable
      onClick={() => onClick?.(requirement)}
      style={{
        marginBottom: 8,
        cursor: 'pointer',
        borderLeft: isOverdue ? '4px solid #ff4d4f' : undefined,
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      }}
      bodyStyle={{ padding: 12 }}
    >
      {/* 标题和ID */}
      <div style={{ marginBottom: 8 }}>
        <Space align="start" style={{ width: '100%' }}>
          <Text strong style={{ fontSize: 12, color: '#666' }}>
            #{requirement.id}
          </Text>
          <div style={{ flex: 1 }}>
            <Paragraph
              ellipsis={{ rows: 2, tooltip: requirement.title }}
              style={{ 
                margin: 0, 
                fontSize: 14, 
                fontWeight: 500,
                lineHeight: '18px'
              }}
            >
              {requirement.title}
            </Paragraph>
          </div>
        </Space>
      </div>

      {/* 业务描述 */}
      <Paragraph
        ellipsis={{ rows: 2, tooltip: requirement.businessDescription }}
        style={{ 
          margin: '8px 0', 
          fontSize: 12, 
          color: '#666',
          lineHeight: '16px'
        }}
      >
        {requirement.businessDescription}
      </Paragraph>

      {/* 优先级标签 */}
      <div style={{ marginBottom: 8 }}>
        <Space size={4}>
          <Tooltip title={`重要程度: ${importanceConfig.label}`}>
            <Tag 
              color={importanceConfig.color} 

              style={{ fontSize: 10, margin: 0 }}
            >
              {importanceConfig.icon} {importanceConfig.label}
            </Tag>
          </Tooltip>
          <Tooltip title={`紧急程度: ${urgencyConfig.label}`}>
            <Tag 
              color={urgencyConfig.color} 

              style={{ fontSize: 10, margin: 0 }}
            >
              ⏰ {urgencyConfig.label}
            </Tag>
          </Tooltip>
        </Space>
      </div>

      {/* 进度条 */}
      {requirement.tasks && requirement.tasks.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <Progress
            percent={progress}
            size="small"
            strokeColor={getProgressColor(progress)}
            format={() => (
              <span style={{ fontSize: 10 }}>
                {requirement.tasks?.filter(t => t.status === 'DONE').length || 0}/
                {requirement.tasks?.length || 0}
              </span>
            )}
          />
        </div>
      )}

      {/* 业务信息 */}
      <div style={{ marginBottom: 8 }}>
        <Space direction="vertical" size={2} style={{ width: '100%' }}>
          {requirement.estimatedValue && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <DollarOutlined style={{ fontSize: 10, color: '#52c41a' }} />
              <Text style={{ fontSize: 10, color: '#666' }}>
                {requirement.estimatedValue}
              </Text>
            </div>
          )}
          
          {requirement.targetUsers && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <TeamOutlined style={{ fontSize: 10, color: '#1890ff' }} />
              <Text 
                ellipsis={{ tooltip: requirement.targetUsers }}
                style={{ fontSize: 10, color: '#666', flex: 1 }}
              >
                {requirement.targetUsers}
              </Text>
            </div>
          )}
          
          {requirement.businessGoal && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <BulbOutlined style={{ fontSize: 10, color: '#faad14' }} />
              <Text 
                ellipsis={{ tooltip: requirement.businessGoal }}
                style={{ fontSize: 10, color: '#666', flex: 1 }}
              >
                {requirement.businessGoal}
              </Text>
            </div>
          )}
        </Space>
      </div>

      {/* 底部信息 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginTop: 8,
        paddingTop: 8,
        borderTop: '1px solid #f0f0f0'
      }}>
        {/* 负责人 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          {requirement.assignee ? (
            <Tooltip title={`负责人: ${requirement.assignee.nickname || requirement.assignee.username}`}>
              <Avatar
                size={16}
                src={getFullAvatarUrl(requirement.assignee.avatarUrl)}
                style={{ backgroundColor: '#1890ff' }}
              >
                {requirement.assignee.nickname?.[0] || requirement.assignee.username[0]}
              </Avatar>
            </Tooltip>
          ) : (
            <Tooltip title="未分配负责人">
              <Avatar size={16} icon={<UserOutlined />} style={{ backgroundColor: '#d9d9d9' }} />
            </Tooltip>
          )}
        </div>

        {/* 期望交付时间 */}
        {requirement.expectedDeliveryDate && (
          <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <CalendarOutlined 
              style={{ 
                fontSize: 10, 
                color: isOverdue ? '#ff4d4f' : '#666' 
              }} 
            />
            <Text 
              style={{ 
                fontSize: 10, 
                color: isOverdue ? '#ff4d4f' : '#666' 
              }}
            >
              {dayjs(requirement.expectedDeliveryDate).format('MM-DD')}
            </Text>
          </div>
        )}
      </div>

      {/* 逾期警告 */}
      {isOverdue && (
        <div style={{
          position: 'absolute',
          top: 4,
          right: 4,
        }}>
          <Tooltip title="已逾期">
            <FlagOutlined style={{ color: '#ff4d4f', fontSize: 12 }} />
          </Tooltip>
        </div>
      )}
    </Card>
  )
}

export default RequirementCard
