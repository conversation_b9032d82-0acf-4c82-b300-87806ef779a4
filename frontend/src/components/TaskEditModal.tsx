import React from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  message
} from 'antd'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { taskApi, requirementApi, userApi, taskStatusConfigApi } from '../services/api'
import type {
  Task,
  TaskUpdateRequest,
  User
} from '../types'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Option } = Select

interface TaskEditModalProps {
  visible: boolean
  task: Task | null
  onClose: () => void
  onSuccess?: () => void
}

const TaskEditModal: React.FC<TaskEditModalProps> = ({
  visible,
  task,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取需求选项
  const { data: requirementListData } = useQuery({
    queryKey: ['requirements'],
    queryFn: () => requirementApi.getRequirementList()
  })
  const requirements = requirementListData?.requirements || []

  // 获取用户选项
  const { data: users } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: () => userApi.getAllUsers()
  })

  // 获取任务状态配置
  const { data: taskStatusConfigs = [] } = useQuery({
    queryKey: ['task-status-configs'],
    queryFn: () => taskStatusConfigApi.getActiveTaskStatusConfigs()
  })

  // 获取上游任务选项（依赖任务）
  const { data: upstreamTaskOptions = [] } = useQuery({
    queryKey: ['upstream-task-options', task?.requirementId],
    queryFn: () => task?.requirementId ? taskApi.getUpstreamTaskOptions(task.requirementId) : Promise.resolve([]),
    enabled: !!task?.requirementId
  })

  // 更新任务
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: TaskUpdateRequest }) =>
      taskApi.updateTask(id, data),
    onSuccess: () => {
      message.success('任务更新成功')
      onClose()
      form.resetFields()
      // 刷新任务相关的所有查询缓存
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
      queryClient.invalidateQueries({ queryKey: ['kanbanData'] })
      queryClient.invalidateQueries({ queryKey: ['task-status-statistics'] })
      onSuccess?.()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新失败')
    },
  })

  // 删除任务
  const deleteMutation = useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      message.success('任务删除成功')
      onClose()
      // 刷新任务相关的所有查询缓存
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
      queryClient.invalidateQueries({ queryKey: ['kanbanData'] })
      queryClient.invalidateQueries({ queryKey: ['task-status-statistics'] })
      onSuccess?.()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除失败')
    },
  })

  // 处理更新任务
  const handleUpdateTask = async (values: any) => {
    if (!task) return

    const data: TaskUpdateRequest = {
      ...values,
      dueDate: values.dueDate ? values.dueDate.toISOString() : undefined,
      dependencies: values.dependencies || []
    }
    updateMutation.mutate({ id: task.id, data })
  }

  // 处理删除任务
  const handleDeleteTask = () => {
    if (!task) return
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除任务 "${task.title}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        deleteMutation.mutate(task.id)
      }
    })
  }

  // 当任务变化时，更新表单值
  React.useEffect(() => {
    if (task && visible) {
      const formValues = {
        ...task,
        dueDate: task.dueDate ? dayjs(task.dueDate) : undefined,
        dependencies: task.dependencies?.map(dep => dep.dependsOnTaskId) || []
      }
      form.setFieldsValue(formValues)
    }
  }, [task, visible, form])

  return (
    <Modal
      title="编辑任务"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdateTask}
      >
        <Form.Item
          name="title"
          label="任务标题"
          rules={[{ required: true, message: '请输入任务标题' }]}
        >
          <Input placeholder="请输入任务标题" />
        </Form.Item>

        <Form.Item
          name="requirementId"
          label="关联需求"
        >
          <Select
            placeholder="关联需求"
            disabled
            showSearch
            filterOption={(input, option) =>
              (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {requirements?.map((requirement) => (
              <Option key={requirement.id} value={requirement.id}>
                {requirement.title} ({requirement.status})
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item name="status" label="任务状态">
          <Select placeholder="选择任务状态">
            {taskStatusConfigs.map(config => (
              <Option key={config.key} value={config.key}>
                {config.icon} {config.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="taskType"
          label="任务类型"
          rules={[{ required: true, message: '请选择任务类型' }]}
        >
          <Select placeholder="选择任务类型">
            <Option value="DESIGN">设计</Option>
            <Option value="DEVELOPMENT">开发</Option>
            <Option value="TESTING">测试</Option>
            <Option value="DEPLOYMENT">部署</Option>
            <Option value="DOCUMENTATION">文档</Option>
            <Option value="RESEARCH">调研</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="description"
          label="任务描述"
        >
          <TextArea rows={3} placeholder="请输入任务描述" />
        </Form.Item>

        <div style={{ display: 'flex', gap: 16 }}>
          <Form.Item
            name="priorityImportance"
            label="重要程度"
            style={{ flex: 1 }}
          >
            <Select placeholder="选择重要程度">
              <Option value="HIGH">高</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="LOW">低</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priorityUrgency"
            label="紧急程度"
            style={{ flex: 1 }}
          >
            <Select placeholder="选择紧急程度">
              <Option value="HIGH">高</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="LOW">低</Option>
            </Select>
          </Form.Item>
        </div>

        <Form.Item
          name="dependencies"
          label="上游任务依赖"
          help="选择当前任务依赖的其他任务，被依赖的任务完成后当前任务才能开始"
        >
          <Select
            mode="multiple"
            placeholder="选择上游任务（可多选）"
            disabled={!task?.requirementId}
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {upstreamTaskOptions.map((taskOption: any) => (
              <Option
                key={taskOption.id}
                value={taskOption.id}
                disabled={taskOption.id === task?.id} // 不能依赖自己
              >
                {taskOption.title} ({taskOption.taskType} - {taskOption.status})
              </Option>
            ))}
          </Select>
        </Form.Item>

        <div style={{ display: 'flex', gap: 16 }}>
          <Form.Item
            name="estimatedHours"
            label="预估工时（小时）"
            style={{ flex: 1 }}
          >
            <InputNumber min={0} step={0.5} placeholder="预估工时" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="dueDate"
            label="截止时间"
            style={{ flex: 1 }}
          >
            <DatePicker
              showTime
              placeholder="选择截止时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
        </div>

        <Form.Item
          name="assigneeId"
          label="负责人"
        >
          <Select
            placeholder="选择负责人（可选）"
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {users?.map((user) => (
              <Option key={user.id} value={user.id}>
                {user.nickname || user.username}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button
              danger
              onClick={handleDeleteTask}
              loading={deleteMutation.isPending}
            >
              删除
            </Button>
            <Button onClick={onClose}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={updateMutation.isPending}
            >
              更新
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default TaskEditModal
