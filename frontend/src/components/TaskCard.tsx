import React from 'react'
import { <PERSON>, Tag, Avatar, Tooltip, Space, Typography } from 'antd'
import {
  ClockCircleOutlined,
  UserOutlined,
  FlagOutlined,
  LinkOutlined,
  FireOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'

import type { Task } from '../types'
import { getFullAvatarUrl } from '../utils/url'
import dayjs from 'dayjs'

const { Text } = Typography

interface TaskCardProps {
  task: Task
  onClick?: () => void
  showQuadrant?: boolean
  compact?: boolean
  draggable?: boolean
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onClick,
  showQuadrant = false,
  compact = false,
  draggable = false
}) => {
  // 不在这里使用 useDrag，避免上下文冲突
  const isDragging = false
  const drag = null
  const getStatusColor = (status: string) => {
    const colors = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      BLOCKED: 'error',
      REVIEW: 'warning',
      TESTING: 'purple',
      DONE: 'success'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusText = (status: string) => {
    const texts = {
      TODO: '待办',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审',
      TESTING: '测试',
      DONE: '完成'
    }
    return texts[status as keyof typeof texts] || status
  }

  const getPriorityColor = (importance: string) => {
    if (importance === 'HIGH') return '#ff4d4f'
    if (importance === 'MEDIUM') return '#fa8c16'
    return '#52c41a'
  }

  const getPriorityText = (importance: string) => {
    if (importance === 'HIGH') return '高'
    if (importance === 'MEDIUM') return '中'
    return '低'
  }

  const getPriorityIcon = (importance: string, urgency: string) => {
    if (importance === 'HIGH' && urgency === 'HIGH') {
      return <FireOutlined style={{ color: '#ff4d4f' }} />
    }
    if (importance === 'HIGH' || urgency === 'HIGH') {
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    }
    return null
  }

  const isOverdue = task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'DONE'

  return (
    <div
      ref={draggable ? drag : undefined}
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: draggable ? 'move' : 'pointer',
        transition: 'opacity 0.3s ease'
      }}
    >
      <Card
        size={compact ? 'small' : 'default'}
        hoverable
        onClick={onClick}
        style={{
          marginBottom: compact ? 8 : 16,
          // Material 3 边框和背景
          border: isOverdue ? '1px solid #ba1a1a' : 'none', // Material 3 error color
          backgroundColor: isOverdue ? '#fef7f0' :
                          task.status === 'DONE' ? '#f8f9fa' : '#fefbff', // Material 3 surface colors
          borderRadius: '16px', // Material 3 圆角
          // Material 3 阴影系统
          boxShadow: isDragging
            ? '0 4px 8px rgba(0,0,0,0.12), 0 8px 16px rgba(0,0,0,0.16)' // 拖拽时的阴影
            : task.status === 'DONE'
            ? '0 1px 3px rgba(0,0,0,0.08)' // 已完成任务的浅阴影
            : '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)', // 标准阴影
          transition: 'all 0.2s cubic-bezier(0.2, 0, 0, 1)', // Material 3 动画曲线
        }}
        styles={{
          body: { padding: compact ? '12px 16px' : '16px 20px' } // 增加内边距
        }}
      >
        <div style={{ marginBottom: 8 }}>
          <div style={{
            // Material 3 Title Small
            fontSize: compact ? 14 : 16,
            fontWeight: '500',
            lineHeight: compact ? '20px' : '24px',
            letterSpacing: '0.1px',
            marginBottom: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 8,
            color: task.status === 'DONE' ? '#49454f' : '#1d1b20', // Material 3 on-surface
          }}>
            {showQuadrant && getPriorityIcon(task.priorityImportance, task.priorityUrgency)}
            <Text ellipsis={{ tooltip: task.title }}>
              {task.title}
            </Text>
          </div>
        {task.description && (
          <div style={{
            // Material 3 Body Small
            fontSize: 12,
            lineHeight: '16px',
            letterSpacing: '0.4px',
            color: task.status === 'DONE' ? '#79747e' : '#49454f', // Material 3 on-surface-variant
            maxHeight: '32px', // 2行高度
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            marginBottom: 12
          }}>
            {task.description}
          </div>
        )}
      </div>

      <Space size={6} wrap style={{ marginBottom: 12 }}>
        <Tag
          color={getStatusColor(task.status)}
          style={{
            borderRadius: '16px', // Material 3 圆角
            border: 'none',
            fontSize: '11px',
            fontWeight: '500',
            padding: '2px 8px',
            lineHeight: '16px'
          }}
        >
          {getStatusText(task.status)}
        </Tag>

        <Tag
          color={getPriorityColor(task.priorityImportance)}
          style={{
            borderRadius: '16px', // Material 3 圆角
            border: 'none',
            fontSize: '11px',
            fontWeight: '500',
            padding: '2px 8px',
            lineHeight: '16px'
          }}
        >
          <FlagOutlined style={{ fontSize: '10px' }} />
          {getPriorityText(task.priorityImportance)}
        </Tag>

        {task.dependencies && task.dependencies.length > 0 && (
          <Tag
            color="orange"
            style={{
              borderRadius: '16px', // Material 3 圆角
              border: 'none',
              fontSize: '11px',
              fontWeight: '500',
              padding: '2px 8px',
              lineHeight: '16px'
            }}
          >
            <LinkOutlined style={{ fontSize: '10px' }} />
            {task.dependencies.length}个依赖
          </Tag>
        )}
      </Space>

      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        // Material 3 Label Small
        fontSize: 11,
        lineHeight: '16px',
        letterSpacing: '0.5px',
        color: task.status === 'DONE' ? '#79747e' : '#49454f', // Material 3 on-surface-variant
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {task.assignee ? (
            <Tooltip title={`负责人: ${task.assignee.nickname || task.assignee.username}`}>
              <Avatar
                size={20}
                src={getFullAvatarUrl(task.assignee.avatarUrl)}
                icon={<UserOutlined />}
              >
                {task.assignee.nickname?.[0] || task.assignee.username[0]}
              </Avatar>
            </Tooltip>
          ) : (
            <Tooltip title="未分配">
              <Avatar size={20} icon={<UserOutlined />} style={{ backgroundColor: '#d9d9d9' }} />
            </Tooltip>
          )}

          {task.estimatedHours && (
            <span>
              <ClockCircleOutlined style={{ marginRight: 2 }} />
              {task.estimatedHours}h
            </span>
          )}
        </div>

        {task.dueDate && (
          <div style={{ 
            color: isOverdue ? '#ff4d4f' : '#666',
            fontWeight: isOverdue ? 500 : 'normal'
          }}>
            {dayjs(task.dueDate).format('MM-DD')}
          </div>
        )}
      </div>

      {task.requirement && (
        <div style={{
          marginTop: 12,
          padding: '8px 12px',
          backgroundColor: task.status === 'DONE' ? '#e7e0ec' : '#f3edf7', // Material 3 surface-container
          borderRadius: '12px', // Material 3 圆角
          // Material 3 Label Small
          fontSize: 11,
          lineHeight: '16px',
          letterSpacing: '0.5px',
          color: task.status === 'DONE' ? '#79747e' : '#49454f', // Material 3 on-surface-variant
          fontWeight: '500'
        }}>
          需求: {task.requirement.title}
        </div>
      )}
    </Card>
    </div>
  )
}

export default TaskCard
