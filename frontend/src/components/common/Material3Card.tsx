import React from 'react'

interface Material3CardProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  elevation?: 0 | 1 | 2 | 3 | 4 | 5
  variant?: 'elevated' | 'filled' | 'outlined'
  onClick?: () => void
  className?: string
  style?: React.CSSProperties
  actions?: React.ReactNode
  header?: React.ReactNode
  footer?: React.ReactNode
}

const Material3Card: React.FC<Material3CardProps> = ({
  children,
  title,
  subtitle,
  elevation = 1,
  variant = 'elevated',
  onClick,
  className = '',
  style = {},
  actions,
  header,
  footer
}) => {
  const getCardStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      borderRadius: 'var(--md-sys-shape-corner-large)',
      padding: '16px',
      transition: 'all 0.2s cubic-bezier(0.2, 0, 0, 1)',
      cursor: onClick ? 'pointer' : 'default',
      position: 'relative',
      overflow: 'hidden',
      ...style
    }

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyles,
          backgroundColor: 'var(--md-sys-color-surface-container-low)',
          color: 'var(--md-sys-color-on-surface)',
          boxShadow: elevation === 0 ? 'none' : 
            elevation === 1 ? '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)' :
            elevation === 2 ? '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)' :
            elevation === 3 ? '0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23)' :
            elevation === 4 ? '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)' :
            '0px 19px 38px rgba(0, 0, 0, 0.30), 0px 15px 12px rgba(0, 0, 0, 0.22)'
        }
      case 'filled':
        return {
          ...baseStyles,
          backgroundColor: 'var(--md-sys-color-surface-container-highest)',
          color: 'var(--md-sys-color-on-surface)',
          boxShadow: 'none'
        }
      case 'outlined':
        return {
          ...baseStyles,
          backgroundColor: 'var(--md-sys-color-surface)',
          color: 'var(--md-sys-color-on-surface)',
          border: '1px solid var(--md-sys-color-outline-variant)',
          boxShadow: 'none'
        }
      default:
        return baseStyles
    }
  }

  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onClick && variant === 'elevated') {
      e.currentTarget.style.transform = 'translateY(-2px)'
      e.currentTarget.style.boxShadow = '0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.12)'
    }
  }

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onClick && variant === 'elevated') {
      e.currentTarget.style.transform = 'translateY(0)'
      const originalShadow = elevation === 0 ? 'none' : 
        elevation === 1 ? '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)' :
        elevation === 2 ? '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)' :
        elevation === 3 ? '0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23)' :
        elevation === 4 ? '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)' :
        '0px 19px 38px rgba(0, 0, 0, 0.30), 0px 15px 12px rgba(0, 0, 0, 0.22)'
      e.currentTarget.style.boxShadow = originalShadow
    }
  }

  return (
    <div
      className={`md-surface-container ${className}`}
      style={getCardStyles()}
      onClick={onClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 自定义头部 */}
      {header}
      
      {/* 标题区域 */}
      {(title || subtitle) && (
        <div style={{ marginBottom: '16px' }}>
          {title && (
            <h3 className="md-typescale-title-large" style={{ 
              margin: '0 0 4px 0',
              color: 'var(--md-sys-color-on-surface)'
            }}>
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="md-typescale-body-medium" style={{ 
              margin: 0,
              color: 'var(--md-sys-color-on-surface-variant)'
            }}>
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* 主要内容 */}
      <div style={{ flex: 1 }}>
        {children}
      </div>

      {/* 操作按钮区域 */}
      {actions && (
        <div style={{ 
          marginTop: '16px',
          paddingTop: '16px',
          borderTop: '1px solid var(--md-sys-color-outline-variant)',
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '8px'
        }}>
          {actions}
        </div>
      )}

      {/* 自定义底部 */}
      {footer}
    </div>
  )
}

export default Material3Card
