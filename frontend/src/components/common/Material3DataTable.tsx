import React from 'react'
import { MdCheckbox, MdIconButton } from '../../utils/material-imports'

interface Column<T = any> {
  key: string
  title: string
  dataIndex?: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  render?: (value: any, record: T, index: number) => React.ReactNode
  sortable?: boolean
  fixed?: 'left' | 'right'
}

interface Material3DataTableProps<T = any> {
  columns: Column<T>[]
  dataSource: T[]
  loading?: boolean
  rowKey?: string | ((record: T) => string)
  selectedRowKeys?: string[]
  onSelectionChange?: (selectedRowKeys: string[], selectedRows: T[]) => void
  onRowClick?: (record: T, index: number) => void
  pagination?: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  emptyText?: string
  size?: 'small' | 'medium' | 'large'
  striped?: boolean
}

const Material3DataTable = <T extends Record<string, any>>({
  columns,
  dataSource,
  loading = false,
  rowKey = 'id',
  selectedRowKeys = [],
  onSelectionChange,
  onRowClick,
  pagination,
  emptyText = '暂无数据',
  size = 'medium',
  striped = true
}: Material3DataTableProps<T>) => {
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record)
    }
    return record[rowKey] || index.toString()
  }

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return
    
    if (checked) {
      const allKeys = dataSource.map((record, index) => getRowKey(record, index))
      onSelectionChange(allKeys, dataSource)
    } else {
      onSelectionChange([], [])
    }
  }

  const handleSelectRow = (record: T, index: number, checked: boolean) => {
    if (!onSelectionChange) return
    
    const key = getRowKey(record, index)
    let newSelectedKeys: string[]
    let newSelectedRows: T[]
    
    if (checked) {
      newSelectedKeys = [...selectedRowKeys, key]
      newSelectedRows = dataSource.filter((item, idx) => 
        newSelectedKeys.includes(getRowKey(item, idx))
      )
    } else {
      newSelectedKeys = selectedRowKeys.filter(k => k !== key)
      newSelectedRows = dataSource.filter((item, idx) => 
        newSelectedKeys.includes(getRowKey(item, idx))
      )
    }
    
    onSelectionChange(newSelectedKeys, newSelectedRows)
  }

  const isAllSelected = dataSource.length > 0 && selectedRowKeys.length === dataSource.length
  const isIndeterminate = selectedRowKeys.length > 0 && selectedRowKeys.length < dataSource.length

  const getCellPadding = () => {
    switch (size) {
      case 'small': return '8px 12px'
      case 'large': return '16px 20px'
      default: return '12px 16px'
    }
  }

  if (loading) {
    return (
      <div className="md-surface-container md-elevation-1" style={{
        borderRadius: 'var(--md-sys-shape-corner-large)',
        padding: '40px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>⏳</div>
        <p className="md-typescale-body-large">加载中...</p>
      </div>
    )
  }

  return (
    <div className="md-surface-container md-elevation-1" style={{
      borderRadius: 'var(--md-sys-shape-corner-large)',
      overflow: 'hidden'
    }}>
      {/* 表格容器 */}
      <div style={{ overflowX: 'auto' }}>
        <table style={{ 
          width: '100%', 
          borderCollapse: 'collapse',
          backgroundColor: 'var(--md-sys-color-surface)'
        }}>
          {/* 表头 */}
          <thead>
            <tr style={{ 
              backgroundColor: 'var(--md-sys-color-surface-container-high)',
              borderBottom: '1px solid var(--md-sys-color-outline-variant)'
            }}>
              {/* 选择列 */}
              {onSelectionChange && (
                <th style={{ 
                  padding: getCellPadding(),
                  width: '48px',
                  textAlign: 'center'
                }}>
                  <MdCheckbox
                    checked={isAllSelected}
                    indeterminate={isIndeterminate}
                    onChange={(e: any) => handleSelectAll(e.target.checked)}
                  />
                </th>
              )}
              
              {/* 数据列 */}
              {columns.map((column) => (
                <th
                  key={column.key}
                  style={{
                    padding: getCellPadding(),
                    textAlign: column.align || 'left',
                    width: column.width,
                    color: 'var(--md-sys-color-on-surface)',
                    fontWeight: '500'
                  }}
                  className="md-typescale-title-small"
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    {column.title}
                    {column.sortable && (
                      <MdIconButton style={{ '--md-icon-button-icon-size': '16px' } as React.CSSProperties}>
                        ↕️
                      </MdIconButton>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          
          {/* 表体 */}
          <tbody>
            {dataSource.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length + (onSelectionChange ? 1 : 0)}
                  style={{ 
                    padding: '40px',
                    textAlign: 'center',
                    color: 'var(--md-sys-color-on-surface-variant)'
                  }}
                >
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
                  <p className="md-typescale-body-large">{emptyText}</p>
                </td>
              </tr>
            ) : (
              dataSource.map((record, index) => {
                const key = getRowKey(record, index)
                const isSelected = selectedRowKeys.includes(key)
                
                return (
                  <tr
                    key={key}
                    style={{
                      backgroundColor: isSelected 
                        ? 'var(--md-sys-color-secondary-container)' 
                        : striped && index % 2 === 1 
                        ? 'var(--md-sys-color-surface-container-low)' 
                        : 'var(--md-sys-color-surface)',
                      borderBottom: '1px solid var(--md-sys-color-outline-variant)',
                      cursor: onRowClick ? 'pointer' : 'default',
                      transition: 'background-color 0.2s ease'
                    }}
                    onClick={() => onRowClick?.(record, index)}
                    onMouseEnter={(e) => {
                      if (onRowClick && !isSelected) {
                        e.currentTarget.style.backgroundColor = 'var(--md-sys-color-surface-container)'
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (onRowClick && !isSelected) {
                        e.currentTarget.style.backgroundColor = striped && index % 2 === 1 
                          ? 'var(--md-sys-color-surface-container-low)' 
                          : 'var(--md-sys-color-surface)'
                      }
                    }}
                  >
                    {/* 选择列 */}
                    {onSelectionChange && (
                      <td style={{ 
                        padding: getCellPadding(),
                        textAlign: 'center'
                      }}>
                        <MdCheckbox
                          checked={isSelected}
                          onChange={(e: any) => {
                            e.stopPropagation()
                            handleSelectRow(record, index, e.target.checked)
                          }}
                        />
                      </td>
                    )}
                    
                    {/* 数据列 */}
                    {columns.map((column) => {
                      const value = column.dataIndex ? record[column.dataIndex] : record
                      const content = column.render 
                        ? column.render(value, record, index)
                        : value
                      
                      return (
                        <td
                          key={column.key}
                          style={{
                            padding: getCellPadding(),
                            textAlign: column.align || 'left',
                            color: 'var(--md-sys-color-on-surface)'
                          }}
                          className="md-typescale-body-medium"
                        >
                          {content}
                        </td>
                      )
                    })}
                  </tr>
                )
              })
            )}
          </tbody>
        </table>
      </div>
      
      {/* 分页 */}
      {pagination && (
        <div style={{
          padding: '16px 24px',
          borderTop: '1px solid var(--md-sys-color-outline-variant)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'var(--md-sys-color-surface-container-low)'
        }}>
          <span className="md-typescale-body-small" style={{ 
            color: 'var(--md-sys-color-on-surface-variant)' 
          }}>
            共 {pagination.total} 条记录
          </span>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <MdIconButton
              disabled={pagination.current <= 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              ←
            </MdIconButton>
            
            <span className="md-typescale-body-medium">
              {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            
            <MdIconButton
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              →
            </MdIconButton>
          </div>
        </div>
      )}
    </div>
  )
}

export default Material3DataTable
