import React from 'react'
import { Result, Button } from 'antd'
import { useAuthStore } from '../stores/authStore'

interface PermissionGuardProps {
  children: React.ReactNode
  requiredRole?: 'SUPER_ADMIN' | 'ADMIN' | 'USER'
  requiredPermissions?: string[]
  fallback?: React.ReactNode
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredRole = 'USER',
  requiredPermissions = [],
  fallback
}) => {
  const { user } = useAuthStore()

  // 检查用户是否已登录
  if (!user) {
    return fallback || (
      <Result
        status="403"
        title="需要登录"
        subTitle="请先登录后再访问此页面"
        extra={
          <Button type="primary" onClick={() => window.location.href = '/login'}>
            去登录
          </Button>
        }
      />
    )
  }

  // 检查角色权限
  const hasRequiredRole = () => {
    if (requiredRole === 'SUPER_ADMIN') {
      return user.userType === 'SUPER_ADMIN'
    }
    if (requiredRole === 'ADMIN') {
      return user.userType === 'SUPER_ADMIN' // 目前只有 SUPER_ADMIN 角色
    }
    return true // USER 级别所有人都可以访问
  }

  // 检查特定权限
  const hasRequiredPermissions = () => {
    if (requiredPermissions.length === 0) return true

    // 超级管理员拥有所有权限
    if (user.userType === 'SUPER_ADMIN') return true

    // 这里可以扩展更复杂的权限检查逻辑
    // 目前简化为基于角色的权限检查
    return requiredPermissions.every(permission => {
      switch (permission) {
        case 'manage_status_config':
          return user.userType === 'SUPER_ADMIN'
        case 'view_status_config':
          return user.userType === 'SUPER_ADMIN'
        case 'manage_requirements':
          return user.userType === 'SUPER_ADMIN'
        case 'view_requirements':
          return true
        default:
          return false
      }
    })
  }

  if (!hasRequiredRole() || !hasRequiredPermissions()) {
    return fallback || (
      <Result
        status="403"
        title="权限不足"
        subTitle="您没有访问此页面的权限，请联系管理员"
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回上一页
          </Button>
        }
      />
    )
  }

  return <>{children}</>
}

export default PermissionGuard

// 权限检查 Hook
export const usePermission = () => {
  const { user } = useAuthStore()

  const hasRole = (role: 'SUPER_ADMIN' | 'ADMIN' | 'USER') => {
    if (!user) return false

    if (role === 'SUPER_ADMIN') {
      return user.userType === 'SUPER_ADMIN'
    }
    if (role === 'ADMIN') {
      return user.userType === 'SUPER_ADMIN' // 目前只有 SUPER_ADMIN 角色
    }
    return true
  }

  const hasPermission = (permission: string) => {
    if (!user) return false

    // 超级管理员拥有所有权限
    if (user.userType === 'SUPER_ADMIN') return true

    switch (permission) {
      case 'manage_status_config':
        return false // 只有超级管理员可以管理状态配置
      case 'view_status_config':
        return false // 只有超级管理员可以查看状态配置
      case 'manage_requirements':
        return false // 只有超级管理员可以管理需求
      case 'view_requirements':
        return true // 所有用户都可以查看需求
      default:
        return false
    }
  }

  const isSuperAdmin = () => hasRole('SUPER_ADMIN')
  const isAdmin = () => hasRole('ADMIN')

  return {
    hasRole,
    hasPermission,
    isSuperAdmin,
    isAdmin,
    user
  }
}
