import api from './api'
import type { ApiResponse, WeeklyReport, WeeklyReportRequest } from '../types'

export const weeklyReportApi = {
  // 获取周报（默认本周）
  getWeeklyReport: async (params?: {
    projectId?: number[]
    assigneeId?: number[]
    includeArchived?: boolean
  }): Promise<WeeklyReport> => {
    const searchParams = new URLSearchParams()
    
    if (params?.projectId) {
      params.projectId.forEach(id => searchParams.append('projectId', id.toString()))
    }
    
    if (params?.assigneeId) {
      params.assigneeId.forEach(id => searchParams.append('assigneeId', id.toString()))
    }
    
    if (params?.includeArchived) {
      searchParams.append('includeArchived', params.includeArchived.toString())
    }

    const response = await api.get<ApiResponse<WeeklyReport>>(
      `/weekly-report?${searchParams.toString()}`
    )
    return response.data.data!
  },

  // 生成自定义周报
  generateWeeklyReport: async (request: WeeklyReportRequest): Promise<WeeklyReport> => {
    const response = await api.post<ApiResponse<WeeklyReport>>('/weekly-report', request)
    return response.data.data!
  }
}
