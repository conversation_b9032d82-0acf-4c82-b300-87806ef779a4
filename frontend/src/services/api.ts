import axios, { AxiosResponse } from 'axios'
import { message } from 'antd'
import { useAuthStore } from '../stores/authStore'
import type {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  UserUpdateRequest,
  PasswordChangeRequest,
  Task,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskListResponse,
  TaskStatusUpdateRequest,
  KanbanData,
  QuadrantData,
  QuadrantUpdateRequest,
  TaskStatistics,
  TaskLog,
  Requirement,
  RequirementCreateRequest,
  RequirementUpdateRequest,
  RequirementListResponse,
  RequirementKanbanData,
  RequirementBreakdownRequest,
  RequirementStatus,
  BusinessValueReport,
  RequirementOption,
  TaskOption,
  Project,
  ProjectCreateRequest,
  ProjectUpdateRequest,
  ProjectOption
} from '../types'
import type { TaskStatusConfig, TaskStatusStatistics } from '../config/taskStatus'

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证 token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<any>>) => {
    return response
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      // 401 未授权 - 清除认证信息并跳转登录
      if (status === 401) {
        useAuthStore.getState().clearAuth()
        window.location.href = '/login'
        message.error('登录已过期，请重新登录')
        return Promise.reject(error)
      }
      
      // 显示错误消息
      const errorMessage = data?.message || '请求失败'
      message.error(errorMessage)
    } else if (error.request) {
      message.error('网络连接失败，请检查网络')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 认证相关 API
export const authApi = {
  // 用户登录
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<ApiResponse<LoginResponse>>('/auth/login', data)
    return response.data.data!
  },

  // 用户注册
  register: async (data: RegisterRequest): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/auth/register', data)
    return response.data.data!
  },
}

// 用户相关 API
export const userApi = {
  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<ApiResponse<User>>('/users/me')
    return response.data.data!
  },

  // 更新用户信息
  updateProfile: async (data: UserUpdateRequest): Promise<User> => {
    const response = await api.put<ApiResponse<User>>('/users/me', data)
    return response.data.data!
  },

  // 修改密码
  changePassword: async (data: PasswordChangeRequest): Promise<void> => {
    await api.post<ApiResponse<void>>('/users/change-password', data)
  },

  // 获取所有用户（用于分配负责人）
  getAllUsers: async (): Promise<User[]> => {
    const response = await api.get<ApiResponse<User[]>>('/users/all')
    return response.data.data!
  },

  // 获取用户列表（管理员）
  getUserList: async (page: number = 1, pageSize: number = 20) => {
    const response = await api.get<ApiResponse<{
      users: User[]
      total: number
      page: number
      pageSize: number
    }>>('/users/list', {
      params: { page, pageSize }
    })
    return response.data.data!
  },

  // 重置用户密码（管理员）
  resetPassword: async (userId: number): Promise<void> => {
    await api.post<ApiResponse<void>>('/users/reset-password', { userId })
  },

  // 管理员编辑用户信息
  updateUserByAdmin: async (userId: number, data: UserUpdateRequest): Promise<User> => {
    const response = await api.put<ApiResponse<User>>(`/users/${userId}`, data)
    return response.data.data!
  },

  // 管理员更新用户状态
  updateUserStatus: async (userId: number, status: string): Promise<void> => {
    await api.put<ApiResponse<void>>(`/users/${userId}/status`, { status })
  },

  // 上传头像
  uploadAvatar: async (file: File): Promise<{ avatarUrl: string }> => {
    const formData = new FormData()
    formData.append('avatar', file)

    const response = await api.post<ApiResponse<{ avatarUrl: string }>>('/users/upload-avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data.data!
  },
}

// 任务相关 API
export const taskApi = {
  // 创建任务
  createTask: async (data: TaskCreateRequest): Promise<Task> => {
    const response = await api.post<ApiResponse<Task>>('/tasks', data)
    return response.data.data!
  },

  // 获取任务列表
  getTaskList: async (params?: any): Promise<TaskListResponse> => {
    const response = await api.get<ApiResponse<TaskListResponse>>('/tasks', { params })
    return response.data.data!
  },

  // 获取单个任务
  getTask: async (id: number): Promise<Task> => {
    const response = await api.get<ApiResponse<Task>>(`/tasks/${id}`)
    return response.data.data!
  },

  // 更新任务
  updateTask: async (id: number, data: TaskUpdateRequest): Promise<Task> => {
    const response = await api.put<ApiResponse<Task>>(`/tasks/${id}`, data)
    return response.data.data!
  },

  // 更新任务状态
  updateTaskStatus: async (id: number, data: TaskStatusUpdateRequest): Promise<Task> => {
    const response = await api.patch<ApiResponse<Task>>(`/tasks/${id}/status`, data)
    return response.data.data!
  },

  // 删除任务
  deleteTask: async (id: number): Promise<void> => {
    await api.delete(`/tasks/${id}`)
  },

  // 获取任务日志
  getTaskLogs: async (id: number): Promise<TaskLog[]> => {
    const response = await api.get<ApiResponse<TaskLog[]>>(`/tasks/${id}/logs`)
    return response.data.data!
  },

  // 获取看板数据
  getKanbanData: async (): Promise<KanbanData> => {
    const response = await api.get<ApiResponse<KanbanData>>('/tasks/kanban')
    return response.data.data!
  },

  // 获取四象限数据
  getQuadrantData: async (): Promise<QuadrantData> => {
    const response = await api.get<ApiResponse<QuadrantData>>('/tasks/quadrant')
    return response.data.data!
  },

  // 更新任务优先级（四象限拖拽）
  updateTaskQuadrant: async (data: QuadrantUpdateRequest): Promise<Task> => {
    const response = await api.patch<ApiResponse<Task>>(`/tasks/${data.taskId}/quadrant`, {
      priorityImportance: data.newImportance,
      priorityUrgency: data.newUrgency
    })
    return response.data.data!
  },

  // 获取任务统计
  getTaskStatistics: async (): Promise<TaskStatistics> => {
    const response = await api.get<ApiResponse<TaskStatistics>>('/tasks/statistics')
    return response.data.data!
  },

  // 获取上游任务选项（用于选择依赖任务）
  getUpstreamTaskOptions: async (requirementId: number): Promise<TaskOption[]> => {
    const response = await api.get<ApiResponse<TaskOption[]>>('/tasks/upstream-options', {
      params: { requirementId }
    })
    return response.data.data!
  },
}

// 需求相关 API
export const requirementApi = {
  // 创建需求
  createRequirement: async (data: RequirementCreateRequest): Promise<Requirement> => {
    const response = await api.post<ApiResponse<Requirement>>('/requirements', data)
    return response.data.data!
  },

  // 获取需求列表
  getRequirementList: async (params?: {
    status?: string
    assigneeId?: number
    creatorId?: number
    search?: string
    page?: number
    pageSize?: number
  }): Promise<RequirementListResponse> => {
    const response = await api.get<ApiResponse<RequirementListResponse>>('/requirements', { params })
    return response.data.data!
  },

  // 获取单个需求
  getRequirement: async (id: number): Promise<Requirement> => {
    const response = await api.get<ApiResponse<Requirement>>(`/requirements/${id}`)
    return response.data.data!
  },

  // 更新需求
  updateRequirement: async (id: number, data: RequirementUpdateRequest): Promise<Requirement> => {
    const response = await api.put<ApiResponse<Requirement>>(`/requirements/${id}`, data)
    return response.data.data!
  },

  // 删除需求
  deleteRequirement: async (id: number): Promise<void> => {
    await api.delete(`/requirements/${id}`)
  },

  // 更新需求状态
  updateRequirementStatus: async (id: number, status: RequirementStatus): Promise<Requirement> => {
    const response = await api.patch<ApiResponse<Requirement>>(`/requirements/${id}/status`, { status })
    return response.data.data!
  },

  // 获取需求看板数据
  getRequirementKanbanData: async (): Promise<RequirementKanbanData> => {
    const response = await api.get<ApiResponse<RequirementKanbanData>>('/requirements/kanban')
    return response.data.data!
  },

  // 需求分解为任务
  breakdownRequirement: async (data: RequirementBreakdownRequest): Promise<Task[]> => {
    const response = await api.post<ApiResponse<Task[]>>(`/requirements/${data.requirementId}/breakdown`, data)
    return response.data.data!
  },

  // 获取业务价值报告
  getBusinessValueReport: async (id: number): Promise<BusinessValueReport> => {
    const response = await api.get<ApiResponse<BusinessValueReport>>(`/requirements/${id}/business-report`)
    return response.data.data!
  },

  // 获取需求选项（用于任务创建时选择）
  getRequirementOptions: async (): Promise<RequirementOption[]> => {
    const response = await api.get<ApiResponse<RequirementOption[]>>('/requirements/options')
    return response.data.data!
  },

  // 获取有任务的需求列表（用于任务管理页面）
  getRequirementsWithTasks: async (params: {
    status?: string
    assigneeId?: number
    creatorId?: number
    search?: string
    page?: number
    pageSize?: number
  } = {}): Promise<RequirementListResponse> => {
    const response = await api.get<ApiResponse<RequirementListResponse>>('/requirements/with-tasks', { params })
    return response.data.data!
  },

  // 获取已归档需求列表
  getArchivedRequirements: async (params: {
    assigneeId?: number
    creatorId?: number
    search?: string
    page?: number
    pageSize?: number
  } = {}): Promise<RequirementListResponse> => {
    const response = await api.get<ApiResponse<RequirementListResponse>>('/requirements/archived', { params })
    return response.data.data!
  },
}

// 任务状态配置 API
export const taskStatusConfigApi = {
  // 获取所有任务状态配置
  getAllTaskStatusConfigs: async (): Promise<TaskStatusConfig[]> => {
    const response = await api.get<ApiResponse<TaskStatusConfig[]>>('/task-status-configs')
    return response.data.data!
  },

  // 获取激活的任务状态配置
  getActiveTaskStatusConfigs: async (): Promise<TaskStatusConfig[]> => {
    const response = await api.get<ApiResponse<TaskStatusConfig[]>>('/task-status-configs/active')
    return response.data.data!
  },

  // 根据 key 获取状态配置
  getTaskStatusConfigByKey: async (key: string): Promise<TaskStatusConfig> => {
    const response = await api.get<ApiResponse<TaskStatusConfig>>(`/task-status-configs/${key}`)
    return response.data.data!
  },

  // 创建任务状态配置
  createTaskStatusConfig: async (data: {
    key: string
    label: string
    color: string
    icon: string
    description: string
    order: number
    isActive?: boolean
    allowedTransitions?: string[]
  }): Promise<TaskStatusConfig> => {
    const response = await api.post<ApiResponse<TaskStatusConfig>>('/task-status-configs', data)
    return response.data.data!
  },

  // 更新任务状态配置
  updateTaskStatusConfig: async (key: string, data: {
    label?: string
    color?: string
    icon?: string
    description?: string
    order?: number
    isActive?: boolean
    allowedTransitions?: string[]
  }): Promise<TaskStatusConfig> => {
    const response = await api.put<ApiResponse<TaskStatusConfig>>(`/task-status-configs/${key}`, data)
    return response.data.data!
  },

  // 删除任务状态配置
  deleteTaskStatusConfig: async (key: string): Promise<void> => {
    await api.delete(`/task-status-configs/${key}`)
  },

  // 批量更新任务状态配置
  batchUpdateTaskStatusConfigs: async (configs: TaskStatusConfig[]): Promise<TaskStatusConfig[]> => {
    const response = await api.put<ApiResponse<TaskStatusConfig[]>>('/task-status-configs/batch', { configs })
    return response.data.data!
  },

  // 验证状态转换
  validateStatusTransition: async (fromStatus: string, toStatus: string): Promise<{
    isAllowed: boolean
    reason?: string
  }> => {
    const response = await api.post<ApiResponse<{
      isAllowed: boolean
      reason?: string
    }>>('/task-status-configs/validate-transition', { fromStatus, toStatus })
    return response.data.data!
  },

  // 获取任务状态统计
  getTaskStatusStatistics: async (): Promise<TaskStatusStatistics[]> => {
    const response = await api.get<ApiResponse<TaskStatusStatistics[]>>('/task-status-configs/statistics')
    return response.data.data!
  },

  // 重置为默认配置
  resetToDefaultConfigs: async (): Promise<TaskStatusConfig[]> => {
    const response = await api.post<ApiResponse<TaskStatusConfig[]>>('/task-status-configs/reset-defaults')
    return response.data.data!
  }
}

// 周任务相关 API
export const weeklyTaskApi = {
  // 获取本周任务
  getWeeklyTasks: async (): Promise<Task[]> => {
    const response = await api.get<ApiResponse<Task[]>>('/weekly-tasks')
    return response.data.data!
  },

  // 获取指定日期范围内的任务
  getWeeklyTasksByDateRange: async (startDate: string, endDate: string): Promise<Task[]> => {
    const response = await api.get<ApiResponse<Task[]>>('/weekly-tasks', {
      params: {
        startDate,
        endDate
      }
    })
    return response.data.data!
  }
}

// 项目相关 API
export const projectApi = {
  // 获取所有项目
  getAllProjects: async (): Promise<Project[]> => {
    const response = await api.get<ApiResponse<Project[]>>('/projects')
    return response.data.data!
  },

  // 获取单个项目
  getProject: async (id: number): Promise<Project> => {
    const response = await api.get<ApiResponse<Project>>(`/projects/${id}`)
    return response.data.data!
  },

  // 创建项目
  createProject: async (data: ProjectCreateRequest): Promise<Project> => {
    const response = await api.post<ApiResponse<Project>>('/projects', data)
    return response.data.data!
  },

  // 更新项目
  updateProject: async (id: number, data: ProjectUpdateRequest): Promise<Project> => {
    const response = await api.put<ApiResponse<Project>>(`/projects/${id}`, data)
    return response.data.data!
  },

  // 删除项目
  deleteProject: async (id: number): Promise<void> => {
    await api.delete(`/projects/${id}`)
  },

  // 检查项目是否可以删除
  canDeleteProject: async (id: number): Promise<boolean> => {
    const response = await api.get<ApiResponse<{ canDelete: boolean }>>(`/projects/${id}/can-delete`)
    return response.data.data!.canDelete
  },

  // 获取项目选项
  getProjectOptions: async (): Promise<ProjectOption[]> => {
    const response = await api.get<ApiResponse<ProjectOption[]>>('/projects/options')
    return response.data.data!
  }
}

export default api
