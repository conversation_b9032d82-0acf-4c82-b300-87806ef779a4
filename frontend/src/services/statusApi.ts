import api from './api'
import type { RequirementStatusConfig, StatusTransitionValidationResponse } from '../types'

// 状态管理 API
export const statusApi = {
  // 获取需求状态配置
  getRequirementStatusConfigs: async (): Promise<RequirementStatusConfig[]> => {
    try {
      const response = await api.get<RequirementStatusConfig[]>('/requirement-status/configs')
      return response.data || []
    } catch (error) {
      // 如果后端不支持，返回默认配置
      console.warn('后端不支持状态配置 API，使用默认配置:', error)
      return []
    }
  },

  // 创建新的需求状态
  createRequirementStatus: async (config: {
    key: string
    label: string
    color: string
    icon: string
    description: string
    order: number
    isActive?: boolean
    allowedTransitions?: string[]
  }): Promise<RequirementStatusConfig> => {
    const response = await api.post<RequirementStatusConfig>('/requirement-status/configs', {
      ...config,
      isActive: config.isActive ?? true,
      allowedTransitions: config.allowedTransitions ?? []
    })
    return response.data
  },

  // 更新需求状态配置
  updateRequirementStatusConfig: async (key: string, config: {
    label?: string
    color?: string
    icon?: string
    description?: string
    order?: number
    isActive?: boolean
    allowedTransitions?: string[]
  }): Promise<RequirementStatusConfig> => {
    const response = await api.put<RequirementStatusConfig>(`/requirement-status/configs/${key}`, config)
    return response.data
  },

  // 删除需求状态
  deleteRequirementStatus: async (key: string): Promise<void> => {
    await api.delete(`/requirement-status/configs/${key}`)
  },

  // 获取单个状态配置
  getRequirementStatusConfig: async (key: string): Promise<RequirementStatusConfig> => {
    const response = await api.get<RequirementStatusConfig>(`/requirement-status/configs/${key}`)
    return response.data
  },

  // 批量更新功能暂未实现

  // 状态统计功能已移除

  // 验证状态转换
  validateStatusTransition: async (fromStatus: string, toStatus: string): Promise<StatusTransitionValidationResponse> => {
    try {
      const response = await api.post<StatusTransitionValidationResponse>('/requirement-status/validate-transition', {
        fromStatus,
        toStatus
      })
      return response.data
    } catch (error) {
      // 如果后端不支持，返回默认允许
      console.warn('后端不支持状态转换验证 API，默认允许转换:', error)
      return { isAllowed: true }
    }
  },

  // 初始化默认配置
  initializeDefaultConfigs: async (): Promise<void> => {
    await api.post('/requirement-status/configs/initialize')
  }
}

// 状态配置缓存管理
class StatusConfigCache {
  private cache: RequirementStatusConfig[] | null = null
  private cacheTime: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  async getConfigs(): Promise<RequirementStatusConfig[]> {
    const now = Date.now()
    
    // 如果缓存有效，直接返回
    if (this.cache && (now - this.cacheTime) < this.CACHE_DURATION) {
      return this.cache
    }

    try {
      // 尝试从后端获取配置
      const configs = await statusApi.getRequirementStatusConfigs()
      this.cache = configs
      this.cacheTime = now
      return configs
    } catch (error) {
      console.error('获取状态配置失败:', error)
      // 返回空数组，让状态管理器使用默认配置
      return []
    }
  }

  clearCache(): void {
    this.cache = null
    this.cacheTime = 0
  }

  updateCache(configs: RequirementStatusConfig[]): void {
    this.cache = configs
    this.cacheTime = Date.now()
  }
}

export const statusConfigCache = new StatusConfigCache()
