import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '../stores/authStore'
import { taskApi } from '../services/api'
import {
  MdFilledButton,
  MdOutlinedButton,
  MdFilledTonalButton,
  MdCircularProgress,
  MdAssistChip
} from '../utils/material-imports'

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()

  // 获取任务统计数据
  const { data: stats, isLoading } = useQuery({
    queryKey: ['taskStatistics'],
    queryFn: taskApi.getTaskStatistics
  })

  const quickActions = [
    {
      title: '创建需求',
      description: '快速创建新的业务需求',
      icon: '📋',
      action: () => navigate('/requirements'),
      color: 'var(--md-sys-color-primary)'
    },
    {
      title: '需求管理',
      description: '查看和管理所有需求',
      icon: '📊',
      action: () => navigate('/requirements'),
      color: 'var(--md-sys-color-secondary)'
    },
    {
      title: '创建任务',
      description: '快速创建新的任务',
      icon: '✅',
      action: () => navigate('/material3-tasks'),
      color: 'var(--md-sys-color-tertiary)'
    },
    {
      title: '查看四象限',
      description: '按优先级查看任务分布',
      icon: '🎯',
      action: () => navigate('/quadrant'),
      color: 'var(--md-sys-color-primary)'
    },
    {
      title: '制定周计划',
      description: '规划本周的工作安排',
      icon: '📅',
      action: () => navigate('/weekly-plan'),
      color: 'var(--md-sys-color-secondary)'
    },
    {
      title: '生成周报',
      description: '查看工作总结报告',
      icon: '📝',
      action: () => navigate('/weekly-report'),
      color: 'var(--md-sys-color-tertiary)'
    }
  ]

  const statisticsData = [
    {
      title: '总任务数',
      value: stats?.totalTasks || 0,
      icon: '📋',
      color: 'var(--md-sys-color-primary)',
      bgColor: 'var(--md-sys-color-primary-container)'
    },
    {
      title: '已完成',
      value: stats?.completedTasks || 0,
      icon: '✅',
      color: 'var(--md-sys-color-secondary)',
      bgColor: 'var(--md-sys-color-secondary-container)'
    },
    {
      title: '进行中',
      value: stats?.inProgressTasks || 0,
      icon: '⚡',
      color: 'var(--md-sys-color-tertiary)',
      bgColor: 'var(--md-sys-color-tertiary-container)'
    },
    {
      title: '待处理',
      value: stats?.pendingTasks || 0,
      icon: '⏳',
      color: 'var(--md-sys-color-error)',
      bgColor: 'var(--md-sys-color-error-container)'
    }
  ]

  return (
    <div className="md-surface" style={{
      padding: '0',
      minHeight: '100vh'
    }}>
      {/* 欢迎区域 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '32px 24px',
        borderRadius: 'var(--md-sys-shape-corner-extra-large)',
        marginBottom: '24px',
        background: 'linear-gradient(135deg, var(--md-sys-color-primary-container) 0%, var(--md-sys-color-secondary-container) 100%)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 装饰性背景 */}
        <div style={{
          position: 'absolute',
          top: '-20px',
          right: '-20px',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          backgroundColor: 'var(--md-sys-color-primary)',
          opacity: 0.1
        }} />

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{
            width: '56px',
            height: '56px',
            borderRadius: 'var(--md-sys-shape-corner-large)',
            backgroundColor: 'var(--md-sys-color-primary)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px'
          }}>
            👋
          </div>
          <div>
            <h1 className="md-typescale-display-small" style={{ margin: '0 0 4px 0' }}>
              工作台
            </h1>
            <p className="md-typescale-body-large" style={{
              margin: 0,
              color: 'var(--md-sys-color-on-surface-variant)'
            }}>
              欢迎回来，{user?.nickname || user?.username}！
            </p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px',
        marginBottom: '24px'
      }}>
        {isLoading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gridColumn: '1 / -1',
            padding: '40px'
          }}>
            <MdCircularProgress indeterminate />
          </div>
        ) : (
          statisticsData.map((stat, index) => (
            <div
              key={index}
              className="md-surface-container md-elevation-2"
              style={{
                padding: '24px',
                borderRadius: 'var(--md-sys-shape-corner-large)',
                backgroundColor: stat.bgColor,
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: 'var(--md-sys-shape-corner-medium)',
                  backgroundColor: stat.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  color: 'white'
                }}>
                  {stat.icon}
                </div>
                <div>
                  <div className="md-typescale-display-medium" style={{
                    margin: '0 0 4px 0',
                    color: stat.color,
                    fontWeight: '600'
                  }}>
                    {stat.value}
                  </div>
                  <div className="md-typescale-body-medium" style={{
                    margin: 0,
                    color: 'var(--md-sys-color-on-surface-variant)'
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* 快速操作 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '24px',
        borderRadius: 'var(--md-sys-shape-corner-large)',
        marginBottom: '24px'
      }}>
        <h2 className="md-typescale-title-large" style={{ marginBottom: '20px' }}>
          快速操作
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '16px'
        }}>
          {quickActions.map((action, index) => (
            <div
              key={index}
              className="md-surface-container-high md-elevation-1"
              style={{
                padding: '20px',
                borderRadius: 'var(--md-sys-shape-corner-large)',
                cursor: 'pointer',
                transition: 'all 0.2s cubic-bezier(0.2, 0, 0, 1)',
                border: '1px solid var(--md-sys-color-outline-variant)'
              }}
              onClick={action.action}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.12)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: 'var(--md-sys-shape-corner-medium)',
                  backgroundColor: action.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  color: 'white'
                }}>
                  {action.icon}
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="md-typescale-title-medium" style={{ margin: '0 0 4px 0' }}>
                    {action.title}
                  </h3>
                  <p className="md-typescale-body-small" style={{
                    margin: 0,
                    color: 'var(--md-sys-color-on-surface-variant)'
                  }}>
                    {action.description}
                  </p>
                </div>
                <div style={{ fontSize: '16px', color: 'var(--md-sys-color-on-surface-variant)' }}>
                  →
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 最近任务 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '24px',
        borderRadius: 'var(--md-sys-shape-corner-large)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 className="md-typescale-title-large" style={{ margin: 0 }}>
            最近任务
          </h2>
          <MdOutlinedButton onClick={() => navigate('/material3-tasks')}>
            <span slot="icon">👁️</span>
            查看全部
          </MdOutlinedButton>
        </div>

        <div style={{
          textAlign: 'center',
          padding: '60px 20px',
          backgroundColor: 'var(--md-sys-color-surface-container-low)',
          borderRadius: 'var(--md-sys-shape-corner-large)',
          border: '2px dashed var(--md-sys-color-outline-variant)'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
          <p className="md-typescale-body-large" style={{
            margin: '0 0 16px 0',
            color: 'var(--md-sys-color-on-surface-variant)'
          }}>
            暂无任务数据
          </p>
          <MdFilledButton onClick={() => navigate('/material3-tasks')}>
            <span slot="icon">➕</span>
            创建第一个任务
          </MdFilledButton>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
