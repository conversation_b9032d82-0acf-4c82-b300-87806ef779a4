import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Typography,
  Button,
  Space,
  message,
  Statistic,
  Card,
  Tooltip,
  Alert
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { requirementApi } from '../services/api'
import { statusApi } from '../services/statusApi'
import type {
  Requirement,
  RequirementStatus,
  RequirementKanbanData
} from '../types'
import RequirementKanbanBoard from '../components/RequirementKanbanBoard'
import RequirementEditModal from '../components/RequirementEditModal'

const { Title, Text } = Typography

const RequirementKanbanPage: React.FC = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  // 编辑功能状态
  const [editingRequirement, setEditingRequirement] = useState<Requirement | null>(null)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)

  // 获取需求看板数据
  const { data: kanbanData, isLoading, error } = useQuery({
    queryKey: ['requirement-kanban'],
    queryFn: requirementApi.getRequirementKanbanData,
    refetchInterval: 30000, // 30秒自动刷新
  })

  // 获取状态配置
  const { data: statusConfigs, isLoading: isLoadingConfigs } = useQuery({
    queryKey: ['requirement-status-configs'],
    queryFn: statusApi.getRequirementStatusConfigs,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  })

  console.log('需求看板数据:', kanbanData)
  console.log('状态配置:', statusConfigs)

  // 更新需求状态
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: number; status: RequirementStatus }) =>
      requirementApi.updateRequirementStatus(id, status),
    onSuccess: () => {
      message.success('需求状态更新成功')
      queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '状态更新失败')
    },
  })

  // 处理编辑需求
  const handleEditRequirement = (requirement: Requirement) => {
    setEditingRequirement(requirement)
    setIsEditModalVisible(true)
  }

  // 处理需求拖拽移动
  const handleRequirementMove = (requirementId: number, newStatus: RequirementStatus) => {
    updateStatusMutation.mutate({ id: requirementId, status: newStatus })
  }

  // 处理需求点击
  // const handleRequirementClick = (requirement: Requirement) => {
  //   setSelectedRequirement(requirement)
  //   setIsDetailModalVisible(true)
  // }

  // 刷新数据
  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
    message.success('数据已刷新')
  }

  // 计算统计数据
  const getStatistics = (data: RequirementKanbanData | undefined, configs: any[] | undefined) => {
    if (!data || !configs) return { total: 0, statusStats: [], overdue: 0 }

    const total = Object.values(data).flat().length

    // 为每个激活的状态生成统计
    const statusStats = configs
      .filter(config => config.isActive)
      .sort((a, b) => a.order - b.order)
      .map(config => ({
        key: config.key,
        label: config.label,
        color: config.color,
        icon: config.icon,
        count: (data[config.key] || []).length
      }))

    // 计算逾期需求
    const now = new Date()
    const overdue = Object.values(data).flat().filter(req =>
      req.expectedDeliveryDate &&
      new Date(req.expectedDeliveryDate) < now &&
      req.status !== 'DELIVERED'
    ).length

    return { total, statusStats, overdue }
  }

  const statistics = getStatistics(kanbanData || {}, statusConfigs)

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="加载失败"
          description="无法加载需求看板数据，请检查网络连接或稍后重试。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      </div>
    )
  }

  if (isLoading || isLoadingConfigs) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <div>加载中...</div>
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              需求看板
            </Title>
            <Text type="secondary">
              可视化管理需求流程，拖拽卡片更新状态
            </Text>
          </div>
          <Space>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={isLoading}
              />
            </Tooltip>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/requirements')}
            >
              创建需求
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: 24,
        overflowX: 'auto',
        paddingBottom: '4px'
      }}>
        {/* 总需求统计 */}
        <div style={{ minWidth: '140px', flex: '0 0 140px' }}>
          <Card size="small">
            <Statistic
              title="总需求"
              value={statistics.total}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </div>

        {/* 各状态统计 */}
        {statistics.statusStats.map((stat) => (
          <div key={stat.key} style={{ minWidth: '140px', flex: '0 0 140px' }}>
            <Card size="small">
              <Statistic
                title={stat.label}
                value={stat.count}
                prefix={<span style={{ color: stat.color }}>{stat.icon}</span>}
                valueStyle={{ color: stat.color }}
              />
            </Card>
          </div>
        ))}

        {/* 逾期统计 */}
        <div style={{ minWidth: '140px', flex: '0 0 140px' }}>
          <Card size="small">
            <Statistic
              title="逾期"
              value={statistics.overdue}
              prefix={<span style={{ color: statistics.overdue > 0 ? '#ff4d4f' : '#52c41a' }}>⚠️</span>}
              valueStyle={{ color: statistics.overdue > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </div>
      </div>



      {/* 看板 */}
      <RequirementKanbanBoard
        data={kanbanData}
        loading={isLoading}
        onRequirementMove={handleRequirementMove}
        onRequirementClick={handleEditRequirement}
      />

      {/* 需求编辑模态框 */}
      <RequirementEditModal
        visible={isEditModalVisible}
        requirement={editingRequirement}
        onClose={() => {
          setIsEditModalVisible(false)
          setEditingRequirement(null)
        }}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['requirement-kanban'] })
          queryClient.invalidateQueries({ queryKey: ['requirements'] })
        }}
      />
    </div>
  )
}

export default RequirementKanbanPage
