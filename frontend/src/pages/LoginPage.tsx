import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { authApi } from '../services/api'
import { useAuthStore } from '../stores/authStore'
import {
  MdFilledButton,
  MdTextButton,
  MdOutlinedTextField,
  MdCircularProgress
} from '../utils/material-imports'

interface LoginForm {
  username: string
  password: string
}

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const { setAuth } = useAuthStore()
  const [formData, setFormData] = useState<LoginForm>({
    username: '',
    password: ''
  })
  const [errors, setErrors] = useState<Partial<LoginForm>>({})

  const loginMutation = useMutation({
    mutationFn: authApi.login,
    onSuccess: (data) => {
      setAuth(data.token, data.user)
      navigate('/')
    },
    onError: (error: any) => {
      console.error('登录失败:', error.response?.data?.message || '登录失败，请检查用户名和密码')
    }
  })

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginForm> = {}

    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名'
    } else if (formData.username.length < 3) {
      newErrors.username = '用户名至少3个字符'
    }

    if (!formData.password) {
      newErrors.password = '请输入密码'
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6个字符'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      loginMutation.mutate(formData)
    }
  }

  const handleInputChange = (field: keyof LoginForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // 检查表单是否可以提交
  const isFormValid = () => {
    return formData.username.trim().length >= 3 && formData.password.length >= 6
  }

  return (
    <div className="md-surface" style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, var(--md-sys-color-primary) 0%, var(--md-sys-color-tertiary) 100%)',
      padding: '24px'
    }}>
      {/* 登录卡片 */}
      <div className="md-surface md-elevation-3" style={{
        width: '100%',
        maxWidth: '400px',
        borderRadius: 'var(--md-sys-shape-corner-extra-large)',
        padding: '48px 32px',
        backgroundColor: 'var(--md-sys-color-surface)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 装饰性背景 */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          right: '-50px',
          width: '100px',
          height: '100px',
          borderRadius: '50%',
          backgroundColor: 'var(--md-sys-color-primary-container)',
          opacity: 0.1
        }} />

        {/* 头部 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            borderRadius: 'var(--md-sys-shape-corner-large)',
            backgroundColor: 'var(--md-sys-color-primary-container)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            fontSize: '32px'
          }}>
            💪
          </div>
          <h1 className="md-typescale-display-small" style={{
            margin: '0 0 8px 0',
            color: 'var(--md-sys-color-on-surface)'
          }}>
            猛男项目管理
          </h1>
          <p className="md-typescale-body-large" style={{
            margin: 0,
            color: 'var(--md-sys-color-on-surface-variant)'
          }}>
            欢迎回来，请登录您的账户
          </p>
        </div>

        {/* 登录表单 */}
        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          <MdOutlinedTextField
            label="用户名"
            required
            value={formData.username}
            onInput={(e: any) => handleInputChange('username', e.target.value)}
            error={!!errors.username}
            errorText={errors.username}
            style={{ width: '100%' }}
            supportingText="请输入您的用户名"
          />

          <MdOutlinedTextField
            label="密码"
            type="password"
            required
            value={formData.password}
            onInput={(e: any) => handleInputChange('password', e.target.value)}
            error={!!errors.password}
            errorText={errors.password}
            style={{ width: '100%' }}
            supportingText="请输入您的密码"
          />

          {/* 错误提示 */}
          {loginMutation.isError && (
            <div style={{
              padding: '12px 16px',
              backgroundColor: 'var(--md-sys-color-error-container)',
              color: 'var(--md-sys-color-on-error-container)',
              borderRadius: 'var(--md-sys-shape-corner-medium)',
              fontSize: '14px'
            }}>
              ⚠️ {(loginMutation.error as any)?.response?.data?.message || '登录失败，请检查用户名和密码'}
            </div>
          )}

          <MdFilledButton
            type="submit"
            disabled={loginMutation.isPending || !isFormValid()}
            style={{
              width: '100%',
              height: '48px',
              marginTop: '8px',
              opacity: (loginMutation.isPending || !isFormValid()) ? 0.6 : 1
            }}
          >
            {loginMutation.isPending ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <MdCircularProgress
                  indeterminate
                  style={{ '--md-circular-progress-size': '20px' } as React.CSSProperties}
                />
                登录中...
              </div>
            ) : (
              <>
                <span slot="icon">🔐</span>
                登录
              </>
            )}
          </MdFilledButton>
        </form>

        {/* 底部链接 */}
        <div style={{
          textAlign: 'center',
          marginTop: '32px',
          paddingTop: '24px',
          borderTop: '1px solid var(--md-sys-color-outline-variant)'
        }}>
          <p className="md-typescale-body-medium" style={{
            margin: '0 0 12px 0',
            color: 'var(--md-sys-color-on-surface-variant)'
          }}>
            还没有账户？
          </p>
          <Link to="/register" style={{ textDecoration: 'none' }}>
            <MdTextButton>
              <span slot="icon">✨</span>
              立即注册
            </MdTextButton>
          </Link>

          {/* 测试账户提示 */}
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: 'var(--md-sys-color-surface-container-high)',
            borderRadius: 'var(--md-sys-shape-corner-medium)',
            border: '1px solid var(--md-sys-color-outline-variant)'
          }}>
            <p className="md-typescale-body-small" style={{
              margin: 0,
              color: 'var(--md-sys-color-on-surface-variant)'
            }}>
              💡 测试账户：super / root
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
