import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  ColorPicker,
  Switch,
  Select,
  InputNumber,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Progress
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { taskStatusConfigApi } from '../services/api'
import type { TaskStatusConfig } from '../config/taskStatus'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { TextArea } = Input

const TaskStatusManagePage: React.FC = () => {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingConfig, setEditingConfig] = useState<TaskStatusConfig | null>(null)
  const [showStatistics, setShowStatistics] = useState(false)

  // 处理颜色值转换 - ColorPicker 可能返回对象，需要转换为字符串
  const processColorValue = (color: any): string => {
    if (typeof color === 'string') {
      return color
    }
    if (color && typeof color === 'object') {
      // Ant Design ColorPicker 返回的对象可能有不同的方法
      if (color.toHexString) {
        return color.toHexString()
      }
      if (color.toHex) {
        return color.toHex()
      }
      if (color.hex) {
        return color.hex
      }
      // 如果是 RGB 对象
      if (color.r !== undefined && color.g !== undefined && color.b !== undefined) {
        return `rgb(${color.r}, ${color.g}, ${color.b})`
      }
    }
    // 默认返回灰色
    return '#d9d9d9'
  }

  // 获取任务状态配置
  const { data: statusConfigs = [], isLoading } = useQuery({
    queryKey: ['task-status-configs'],
    queryFn: taskStatusConfigApi.getAllTaskStatusConfigs
  })

  // 获取任务状态统计
  const { data: statistics = [] } = useQuery({
    queryKey: ['task-status-statistics'],
    queryFn: taskStatusConfigApi.getTaskStatusStatistics,
    enabled: showStatistics
  })

  // 创建状态配置
  const createMutation = useMutation({
    mutationFn: taskStatusConfigApi.createTaskStatusConfig,
    onSuccess: () => {
      message.success('创建任务状态配置成功')
      queryClient.invalidateQueries({ queryKey: ['task-status-configs'] })
      setIsModalVisible(false)
      form.resetFields()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '创建任务状态配置失败')
    }
  })

  // 更新状态配置
  const updateMutation = useMutation({
    mutationFn: ({ key, data }: { key: string; data: any }) =>
      taskStatusConfigApi.updateTaskStatusConfig(key, data),
    onSuccess: () => {
      message.success('更新任务状态配置成功')
      queryClient.invalidateQueries({ queryKey: ['task-status-configs'] })
      setIsModalVisible(false)
      setEditingConfig(null)
      form.resetFields()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新任务状态配置失败')
    }
  })

  // 删除状态配置
  const deleteMutation = useMutation({
    mutationFn: taskStatusConfigApi.deleteTaskStatusConfig,
    onSuccess: () => {
      message.success('删除任务状态配置成功')
      queryClient.invalidateQueries({ queryKey: ['task-status-configs'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除任务状态配置失败')
    }
  })

  // 批量更新功能暂未实现

  // 重置为默认配置
  const resetMutation = useMutation({
    mutationFn: taskStatusConfigApi.resetToDefaultConfigs,
    onSuccess: () => {
      message.success('重置任务状态配置成功')
      queryClient.invalidateQueries({ queryKey: ['task-status-configs'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '重置任务状态配置失败')
    }
  })

  const handleCreate = () => {
    setEditingConfig(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (config: TaskStatusConfig) => {
    setEditingConfig(config)
    setIsModalVisible(true)
    form.setFieldsValue({
      ...config,
      color: config.color // 确保颜色值正确设置
    })
  }

  const handleDelete = (key: string) => {
    deleteMutation.mutate(key)
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      // 处理颜色值 - ColorPicker 可能返回对象，需要转换为字符串
      const processedValues = {
        ...values,
        color: processColorValue(values.color),
        allowedTransitions: values.allowedTransitions || []
      }

      if (editingConfig) {
        updateMutation.mutate({
          key: editingConfig.key,
          data: processedValues
        })
      } else {
        createMutation.mutate(processedValues)
      }
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleReset = () => {
    Modal.confirm({
      title: '确认重置',
      content: '这将重置所有任务状态配置为默认设置，确定继续吗？',
      onOk: () => resetMutation.mutate()
    })
  }

  const columns: ColumnsType<TaskStatusConfig> = [
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      width: 80,
      sorter: (a, b) => a.order - b.order
    },
    {
      title: '状态键',
      dataIndex: 'key',
      key: 'key',
      width: 120,
      render: (key: string) => <Text code>{key}</Text>
    },
    {
      title: '显示标签',
      dataIndex: 'label',
      key: 'label',
      width: 120
    },
    {
      title: '颜色',
      dataIndex: 'color',
      key: 'color',
      width: 100,
      render: (color: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <div
            style={{
              width: 20,
              height: 20,
              backgroundColor: color,
              borderRadius: 4,
              border: '1px solid #d9d9d9'
            }}
          />
          <Text style={{ fontSize: 12 }}>{color}</Text>
        </div>
      )
    },
    {
      title: '图标',
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      render: (icon: string) => <span style={{ fontSize: 16 }}>{icon}</span>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      )
    },
    {
      title: '允许转换',
      dataIndex: 'allowedTransitions',
      key: 'allowedTransitions',
      width: 200,
      render: (transitions: string[]) => (
        <Space wrap>
          {transitions.map(transition => (
            <Tag key={transition}>
              {statusConfigs.find(c => c.key === transition)?.label || transition}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="确定删除这个状态配置吗？"
            onConfirm={() => handleDelete(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 0 }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0 }}>任务状态配置</Title>
        <Text type="secondary">
          管理任务的状态配置，包括状态名称、颜色、转换规则等
        </Text>
      </div>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div></div>
          <Space>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => setShowStatistics(!showStatistics)}
            >
              {showStatistics ? '隐藏统计' : '显示统计'}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置默认
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建状态
            </Button>
          </Space>
        </div>

        {/* 统计信息 */}
        {showStatistics && statistics.length > 0 && (
          <Card style={{ marginBottom: 16 }} title="任务状态统计">
            <Row gutter={16}>
              {statistics.map(stat => (
                <Col span={6} key={stat.statusKey}>
                  <Card size="small">
                    <Statistic
                      title={stat.statusLabel}
                      value={stat.count}
                      suffix={`(${stat.percentage.toFixed(1)}%)`}
                    />
                    <Progress
                      percent={stat.percentage}
                      size="small"
                      showInfo={false}
                      strokeColor={statusConfigs.find(c => c.key === stat.statusKey)?.color}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        )}

        <Table
          columns={columns}
          dataSource={statusConfigs}
          rowKey="key"
          loading={isLoading}
          pagination={false}
          size="small"
        />
      </Card>

      {/* 创建/编辑弹窗 */}
      <Modal
        title={editingConfig ? '编辑任务状态' : '新建任务状态'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingConfig(null)
          form.resetFields()
        }}
        confirmLoading={createMutation.isPending || updateMutation.isPending}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isActive: true,
            allowedTransitions: []
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="key"
                label="状态键"
                rules={[{ required: true, message: '请输入状态键' }]}
              >
                <Input placeholder="如: TODO, IN_PROGRESS" disabled={!!editingConfig} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="label"
                label="显示标签"
                rules={[{ required: true, message: '请输入显示标签' }]}
              >
                <Input placeholder="如: 待开始, 进行中" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="color"
                label="颜色"
                rules={[{ required: true, message: '请选择颜色' }]}
              >
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="icon"
                label="图标"
                rules={[{ required: true, message: '请输入图标' }]}
              >
                <Input placeholder="如: 📋, 🔄" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order"
                label="排序"
                rules={[{ required: true, message: '请输入排序' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入状态描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="是否激活"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="allowedTransitions"
                label="允许转换到"
              >
                <Select
                  mode="multiple"
                  placeholder="选择允许转换的状态"
                  options={statusConfigs
                    .filter(config => !editingConfig || config.key !== editingConfig.key)
                    .map(config => ({
                      label: config.label,
                      value: config.key
                    }))}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default TaskStatusManagePage
