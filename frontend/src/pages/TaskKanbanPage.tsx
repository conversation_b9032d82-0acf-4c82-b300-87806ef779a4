import React, { useState } from 'react'
import { Typo<PERSON>, Spin, Alert, Card } from 'antd'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { taskApi } from '../services/api'
import { useAuthStore } from '../stores/authStore'
import TaskKanban from '../components/TaskKanban'
import TaskEditModal from '../components/TaskEditModal'
import type { Task } from '../types'

const { Title, Text } = Typography

const TaskKanbanPage: React.FC = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const isSuperAdmin = user?.userType === 'SUPER_ADMIN'

  // 编辑功能状态
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)

  // 处理编辑任务
  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setIsEditModalVisible(true)
  }

  // 获取看板数据
  const { data: kanbanData, isLoading, error } = useQuery({
    queryKey: ['kanbanData'],
    queryFn: () => taskApi.getKanbanData(),
    refetchInterval: 30000, // 30秒自动刷新
  })

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <Spin size="large" tip="加载任务数据中..." />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="加载失败"
          description="无法加载任务数据，请检查网络连接或稍后重试。"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!kanbanData) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="暂无数据"
          description="当前没有任务数据可显示。"
          type="info"
          showIcon
        />
      </div>
    )
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div style={{ padding: '24px' }}>
        {/* 权限提示卡片 */}
        <Card
          size="small"
          style={{
            marginBottom: '16px',
            backgroundColor: isSuperAdmin ? '#f6ffed' : '#fff7e6',
            borderColor: isSuperAdmin ? '#b7eb8f' : '#ffd591'
          }}
        >
          <Text style={{
            color: isSuperAdmin ? '#52c41a' : '#fa8c16',
            fontWeight: 500
          }}>
            {isSuperAdmin ? (
              <>🔧 超级管理员视图：显示所有用户的任务</>
            ) : (
              <>👤 个人视图：仅显示您负责的任务</>
            )}
          </Text>
        </Card>

        <div style={{ marginBottom: 24 }}>
          <Title level={2}>任务看板</Title>
          <Text type="secondary">
            按任务状态分类管理，拖拽任务卡片可以更改状态。
          </Text>
        </div>

        <TaskKanban data={kanbanData} onTaskClick={handleEditTask} />

        {/* 任务编辑模态框 */}
        <TaskEditModal
          visible={isEditModalVisible}
          task={editingTask}
          onClose={() => {
            setIsEditModalVisible(false)
            setEditingTask(null)
          }}
          onSuccess={() => {
            queryClient.invalidateQueries({ queryKey: ['kanbanData'] })
          }}
        />
      </div>
    </DndProvider>
  )
}

export default TaskKanbanPage
