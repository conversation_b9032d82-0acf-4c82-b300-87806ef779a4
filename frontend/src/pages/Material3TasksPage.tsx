import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { taskApi, requirementApi, userApi } from '../services/api'
import type { Task, TaskCreateRequest } from '../types'
import { useAuthStore } from '../stores/authStore'
import {
  MdFilledButton,
  MdOutlinedButton,
  MdTextButton,
  MdOutlinedTextField,
  MdOutlinedSelect,
  MdSelectOption,
  MdCircularProgress
} from '../utils/material-imports'
import Material3TaskCard from '../components/Material3TaskCard'

const Material3TasksPage: React.FC = () => {
  const queryClient = useQueryClient()
  const { user: currentUser } = useAuthStore()

  // 状态管理
  const [searchParams, setSearchParams] = useState<any>({
    page: 1,
    pageSize: 5
  })
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  // const [selectedRequirementId, setSelectedRequirementId] = useState<number | null>(null)
  // const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  // const [viewingTask, setViewingTask] = useState<Task | null>(null)

  // 表单数据
  const [createFormData, setCreateFormData] = useState({
    title: '',
    description: '',
    requirementId: '',
    assigneeId: currentUser?.id || '',
    priorityImportance: 'MEDIUM',
    taskType: 'DEVELOPMENT',
    estimatedHours: '',
    dueDate: '',
    dependencies: [] as number[]
  })

  // 获取需求列表
  const { data: requirementListData, isLoading: requirementsLoading } = useQuery({
    queryKey: ['requirements-with-tasks', searchParams],
    queryFn: () => requirementApi.getRequirementsWithTasks({
      page: searchParams.page,
      pageSize: searchParams.pageSize,
      search: searchParams.requirementSearch
    })
  })

  // 获取任务列表
  const requirements = requirementListData?.requirements || []
  const requirementIds = requirements.map(req => req.id)

  const { data: tasksData, isLoading: tasksLoading } = useQuery({
    queryKey: ['tasks-by-requirements', requirementIds],
    queryFn: async () => {
      if (requirementIds.length === 0) {
        return { tasks: [], total: 0 }
      }

      const taskPromises = requirementIds.map(reqId =>
        taskApi.getTaskList({
          requirementId: reqId,
          page: 1,
          pageSize: 1000
        })
      )

      const taskResults = await Promise.all(taskPromises)
      const allTasks = taskResults.reduce((acc, result) => {
        if (result.tasks) {
          acc.push(...result.tasks)
        }
        return acc
      }, [] as any[])

      return {
        tasks: allTasks,
        total: allTasks.length
      }
    },
    enabled: requirementIds.length > 0
  })

  // 获取用户列表
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: () => userApi.getUserList()
  })

  // 获取需求选项
  const { data: requirementOptions } = useQuery({
    queryKey: ['requirement-options'],
    queryFn: () => requirementApi.getRequirementList({ status: 'IN_PROGRESS' })
  })

  // 创建任务
  const createTaskMutation = useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: () => {
      setIsCreateModalVisible(false)
      setCreateFormData({
        title: '',
        description: '',
        requirementId: '',
        assigneeId: currentUser?.id || '',
        priorityImportance: 'MEDIUM',
        taskType: 'DEVELOPMENT',
        estimatedHours: '',
        dueDate: '',
        dependencies: []
      })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
      queryClient.invalidateQueries({ queryKey: ['requirements-with-tasks'] })
    },
    onError: (error: any) => {
      console.error('创建任务失败:', error)
    }
  })

  const isLoading = requirementsLoading || tasksLoading
  const taskListData = {
    tasks: tasksData?.tasks || [],
    total: tasksData?.total || 0
  }

  // 处理表单提交
  const handleCreateTask = (e: React.FormEvent) => {
    e.preventDefault()
    
    const taskData: TaskCreateRequest = {
      title: createFormData.title,
      description: createFormData.description,
      requirementId: parseInt(createFormData.requirementId),
      assigneeId: createFormData.assigneeId ? parseInt(createFormData.assigneeId.toString()) : undefined,
      priorityImportance: createFormData.priorityImportance as any,
      taskType: createFormData.taskType as any,
      estimatedHours: createFormData.estimatedHours ? parseFloat(createFormData.estimatedHours) : undefined,
      dueDate: createFormData.dueDate || undefined,
      dependencies: createFormData.dependencies || []
    }
    
    createTaskMutation.mutate(taskData)
  }

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const requirementSearch = formData.get('requirementSearch') as string
    
    setSearchParams({
      requirementSearch,
      page: 1,
      pageSize: searchParams.pageSize
    })
  }

  // 重置搜索
  const handleResetSearch = () => {
    setSearchParams({
      page: 1,
      pageSize: 5
    })
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    setSearchParams({
      ...searchParams,
      page: page
    })
  }

  // 查看任务详情
  const handleViewTask = (task: Task) => {
    console.log('查看任务详情:', task)
    // TODO: 实现任务详情查看
  }

  return (
    <div className="md-surface" style={{ 
      padding: '24px',
      minHeight: '100vh'
    }}>
      {/* Material 3 页面标题区域 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '32px 24px',
        borderRadius: 'var(--md-sys-shape-corner-extra-large)',
        marginBottom: '24px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              padding: '12px',
              backgroundColor: 'var(--md-sys-color-primary-container)',
              borderRadius: 'var(--md-sys-shape-corner-large)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ 
                fontSize: '24px',
                color: 'var(--md-sys-color-primary)'
              }}>📋</span>
            </div>
            <h1 className="md-typescale-display-small" style={{ margin: 0 }}>
              任务管理
            </h1>
          </div>
          <MdFilledButton
            onClick={() => {
              setIsCreateModalVisible(true)
              setCreateFormData(prev => ({
                ...prev,
                assigneeId: currentUser?.id?.toString() || ''
              }))
            }}
          >
            <span slot="icon">➕</span>
            创建任务
          </MdFilledButton>
        </div>
        <p className="md-typescale-body-large" style={{ margin: 0 }}>
          管理和跟踪项目任务，支持按需求分组查看和编辑
        </p>
      </div>

      {/* Material 3 搜索筛选区域 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '24px',
        borderRadius: 'var(--md-sys-shape-corner-large)',
        marginBottom: '24px'
      }}>
        <h2 className="md-typescale-title-medium" style={{ marginBottom: '16px' }}>
          需求搜索
        </h2>
        <form onSubmit={handleSearch} style={{ display: 'flex', gap: '16px', alignItems: 'flex-end' }}>
          <MdOutlinedTextField
            label="关联需求"
            name="requirementSearch"
            placeholder="请输入需求标题进行搜索"
            style={{ flex: 1 }}
          />
          <MdFilledButton type="submit">
            <span slot="icon">🔍</span>
            搜索
          </MdFilledButton>
          <MdOutlinedButton type="button" onClick={handleResetSearch}>
            <span slot="icon">🔄</span>
            重置
          </MdOutlinedButton>
        </form>
      </div>

      {/* 任务列表区域 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '24px',
        borderRadius: 'var(--md-sys-shape-corner-large)',
        marginBottom: '24px'
      }}>
        <h2 className="md-typescale-title-medium" style={{ marginBottom: '16px' }}>
          任务列表
        </h2>
        
        {isLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
            <MdCircularProgress indeterminate />
          </div>
        ) : (
          <div>
            {taskListData.tasks.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <p className="md-typescale-body-large">暂无任务数据</p>
              </div>
            ) : (
              <div style={{ display: 'grid', gap: '16px' }}>
                {taskListData.tasks.map((task: Task) => (
                  <Material3TaskCard
                    key={task.id}
                    task={task}
                    onClick={() => handleViewTask(task)}
                    onEdit={() => console.log('编辑任务:', task.id)}
                    onDelete={() => console.log('删除任务:', task.id)}
                    compact={false}
                    showActions={true}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* 分页控件 */}
      <div className="md-surface-container md-elevation-1" style={{
        padding: '16px 24px',
        borderRadius: 'var(--md-sys-shape-corner-large)',
        textAlign: 'center'
      }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '16px' }}>
          <MdOutlinedButton
            disabled={searchParams.page <= 1}
            onClick={() => handlePageChange(searchParams.page - 1)}
          >
            上一页
          </MdOutlinedButton>
          <span className="md-typescale-body-medium">
            第 {searchParams.page} 页，共 {requirementListData?.total || 0} 条需求
          </span>
          <MdOutlinedButton
            disabled={!requirementListData || searchParams.page >= Math.ceil(requirementListData.total / searchParams.pageSize)}
            onClick={() => handlePageChange(searchParams.page + 1)}
          >
            下一页
          </MdOutlinedButton>
        </div>
      </div>

      {/* 创建任务对话框 */}
      {isCreateModalVisible && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={() => setIsCreateModalVisible(false)}
        >
          <div
            className="md-surface md-elevation-3"
            style={{
              width: '90%',
              maxWidth: '600px',
              maxHeight: '90vh',
              borderRadius: 'var(--md-sys-shape-corner-extra-large)',
              padding: '32px',
              overflow: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="md-typescale-headline-small" style={{ margin: '0 0 24px 0' }}>
              创建任务
            </h2>

            <form onSubmit={handleCreateTask} style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              <MdOutlinedTextField
                label="任务标题"
                required
                value={createFormData.title}
                onInput={(e: any) => setCreateFormData(prev => ({ ...prev, title: e.target.value }))}
                style={{ width: '100%' }}
              />

              <MdOutlinedTextField
                label="任务描述"
                type="textarea"
                rows={3}
                value={createFormData.description}
                onInput={(e: any) => setCreateFormData(prev => ({ ...prev, description: e.target.value }))}
                style={{ width: '100%' }}
              />

              <MdOutlinedSelect
                label="关联需求"
                required
                value={createFormData.requirementId}
                onSelectionChange={(e: any) => setCreateFormData(prev => ({ ...prev, requirementId: e.target.value }))}
                style={{ width: '100%' }}
              >
                {requirementOptions?.requirements?.map((requirement) => (
                  <MdSelectOption key={requirement.id} value={requirement.id.toString()}>
                    {requirement.title} ({requirement.status})
                  </MdSelectOption>
                ))}
              </MdOutlinedSelect>

              <MdOutlinedSelect
                label="负责人"
                value={createFormData.assigneeId}
                onSelectionChange={(e: any) => setCreateFormData(prev => ({ ...prev, assigneeId: e.target.value }))}
                style={{ width: '100%' }}
              >
                <MdSelectOption value="">请选择负责人</MdSelectOption>
                {users?.users?.map((user: any) => (
                  <MdSelectOption key={user.id} value={user.id.toString()}>
                    {user.nickname || user.username}
                  </MdSelectOption>
                ))}
              </MdOutlinedSelect>

              <MdOutlinedSelect
                label="优先级"
                value={createFormData.priorityImportance}
                onSelectionChange={(e: any) => setCreateFormData(prev => ({ ...prev, priorityImportance: e.target.value }))}
                style={{ width: '100%' }}
              >
                <MdSelectOption value="HIGH">🔥 高</MdSelectOption>
                <MdSelectOption value="MEDIUM">⚡ 中</MdSelectOption>
                <MdSelectOption value="LOW">📌 低</MdSelectOption>
              </MdOutlinedSelect>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <MdOutlinedTextField
                  label="预估工时"
                  type="number"
                  step="0.5"
                  min="0"
                  value={createFormData.estimatedHours}
                  onInput={(e: any) => setCreateFormData(prev => ({ ...prev, estimatedHours: e.target.value }))}
                />

                <MdOutlinedTextField
                  label="截止时间"
                  type="datetime-local"
                  value={createFormData.dueDate}
                  onInput={(e: any) => setCreateFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '12px',
                marginTop: '24px',
                paddingTop: '24px',
                borderTop: '1px solid var(--md-sys-color-outline-variant)'
              }}>
                <MdTextButton
                  type="button"
                  onClick={() => setIsCreateModalVisible(false)}
                >
                  取消
                </MdTextButton>
                <MdFilledButton
                  type="submit"
                  disabled={createTaskMutation.isPending}
                >
                  {createTaskMutation.isPending ? (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <MdCircularProgress
                        indeterminate
                        style={{ '--md-circular-progress-size': '16px' } as React.CSSProperties}
                      />
                      创建中...
                    </div>
                  ) : (
                    '创建任务'
                  )}
                </MdFilledButton>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Material3TasksPage
