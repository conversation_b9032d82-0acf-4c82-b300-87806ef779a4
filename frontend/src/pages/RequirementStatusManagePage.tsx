import React, { useState } from 'react'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  ColorPicker,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { statusApi } from '../services/statusApi'
import type { RequirementStatusConfig } from '../types'

const { Title, Text } = Typography
const { TextArea } = Input

const RequirementStatusManagePage: React.FC = () => {
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingConfig, setEditingConfig] = useState<RequirementStatusConfig | null>(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 处理颜色值的辅助函数
  const processColorValue = (color: any): string => {
    if (typeof color === 'string') {
      return color
    }
    if (color && typeof color === 'object') {
      // Ant Design ColorPicker 返回的对象可能有不同的方法
      if (color.toHexString) {
        return color.toHexString()
      }
      if (color.toHex) {
        return color.toHex()
      }
      if (color.hex) {
        return color.hex
      }
      // 如果是 RGB 对象
      if (color.r !== undefined && color.g !== undefined && color.b !== undefined) {
        return `#${((1 << 24) + (color.r << 16) + (color.g << 8) + color.b).toString(16).slice(1)}`
      }
    }
    // 默认返回原值或黑色
    return color || '#000000'
  }

  // 获取状态配置列表
  const { data: statusConfigs, isLoading } = useQuery({
    queryKey: ['requirement-status-configs'],
    queryFn: statusApi.getRequirementStatusConfigs
  })

  // 移除状态统计功能

  // 创建状态配置
  const createStatusMutation = useMutation({
    mutationFn: statusApi.createRequirementStatus,
    onSuccess: () => {
      message.success('状态配置创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirement-status-configs'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '状态配置创建失败')
    }
  })

  // 更新状态配置
  const updateStatusMutation = useMutation({
    mutationFn: ({ key, config }: { key: string, config: Partial<RequirementStatusConfig> }) =>
      statusApi.updateRequirementStatusConfig(key, config),
    onSuccess: () => {
      message.success('状态配置更新成功')
      setIsEditModalVisible(false)
      setEditingConfig(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirement-status-configs'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '状态配置更新失败')
    }
  })

  // 删除状态配置
  const deleteStatusMutation = useMutation({
    mutationFn: statusApi.deleteRequirementStatus,
    onSuccess: () => {
      message.success('状态配置删除成功')
      queryClient.invalidateQueries({ queryKey: ['requirement-status-configs'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '状态配置删除失败')
    }
  })

  // 批量更新功能暂未实现

  const handleCreateStatus = (values: any) => {
    // 处理颜色值 - ColorPicker 可能返回对象，需要转换为字符串
    const processedValues = {
      ...values,
      color: processColorValue(values.color),
      allowedTransitions: values.allowedTransitions || []
    }

    console.log('创建状态配置数据:', processedValues) // 调试日志
    createStatusMutation.mutate(processedValues)
  }

  const handleEditStatus = (config: RequirementStatusConfig) => {
    setEditingConfig(config)
    editForm.setFieldsValue({
      ...config,
      color: config.color
    })
    setIsEditModalVisible(true)
  }

  const handleUpdateStatus = (values: any) => {
    if (!editingConfig) return

    // 处理颜色值 - ColorPicker 可能返回对象，需要转换为字符串
    const processedValues = {
      ...values,
      color: processColorValue(values.color),
      allowedTransitions: values.allowedTransitions || []
    }

    console.log('更新状态配置数据:', processedValues) // 调试日志
    updateStatusMutation.mutate({
      key: editingConfig.key,
      config: processedValues
    })
  }

  const handleDeleteStatus = (key: string) => {
    deleteStatusMutation.mutate(key)
  }

  // 批量更新功能暂未实现

  const columns = [
    {
      title: '状态键',
      dataIndex: 'key',
      key: 'key',
      width: 120,
      render: (key: string) => <Text code>{key}</Text>
    },
    {
      title: '显示名称',
      dataIndex: 'label',
      key: 'label',
      render: (label: string, record: RequirementStatusConfig) => (
        <Space>
          <span style={{ fontSize: '16px' }}>{record.icon}</span>
          <Text>{label}</Text>
        </Space>
      )
    },
    {
      title: '颜色',
      dataIndex: 'color',
      key: 'color',
      width: 100,
      render: (color: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <div
            style={{
              width: 20,
              height: 20,
              backgroundColor: color,
              borderRadius: 4,
              border: '1px solid #d9d9d9'
            }}
          />
          <Text style={{ fontSize: 12 }}>{color}</Text>
        </div>
      )
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      width: 80,
      sorter: (a: RequirementStatusConfig, b: RequirementStatusConfig) => a.order - b.order
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      )
    },
    {
      title: '允许转换',
      dataIndex: 'allowedTransitions',
      key: 'allowedTransitions',
      render: (transitions: string[]) => (
        <Space wrap>
          {transitions.map(transition => (
            <Tag key={transition}>{transition}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: RequirementStatusConfig) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditStatus(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个状态配置吗？"
            description="删除后无法恢复，且不能删除正在使用的状态。"
            onConfirm={() => handleDeleteStatus(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 0 }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0 }}>需求状态配置</Title>
        <Text type="secondary">
          管理需求的状态配置，包括状态名称、颜色、转换规则等
        </Text>
      </div>

      {/* 统计卡片已移除 */}

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            创建状态
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => queryClient.invalidateQueries({ queryKey: ['requirement-status-configs'] })}
          >
            刷新
          </Button>
          {/* 批量更新功能暂未实现 */}
          <Button icon={<ExportOutlined />}>
            导出配置
          </Button>
          <Button icon={<ImportOutlined />}>
            导入配置
          </Button>
        </Space>
      </div>

      {/* 状态配置表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={statusConfigs || []}
          rowKey="key"
          loading={isLoading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建状态模态框 */}
      <Modal
        title="创建状态配置"
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateStatus}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="key"
                label="状态键"
                rules={[
                  { required: true, message: '请输入状态键' },
                  { pattern: /^[A-Z_]+$/, message: '状态键只能包含大写字母和下划线' }
                ]}
              >
                <Input placeholder="如：CUSTOM_STATUS" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="label"
                label="显示名称"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="如：自定义状态" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="icon"
                label="图标"
                rules={[{ required: true, message: '请输入图标' }]}
              >
                <Input placeholder="如：🎯" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="color"
                label="颜色"
                rules={[{ required: true, message: '请选择颜色' }]}
              >
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order"
                label="排序"
                rules={[{ required: true, message: '请输入排序' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入状态描述" />
          </Form.Item>

          <Form.Item
            name="allowedTransitions"
            label="允许转换到的状态"
          >
            <Select
              mode="multiple"
              placeholder="选择允许转换到的状态"
              options={statusConfigs?.map(config => ({
                label: config.label,
                value: config.key
              }))}
            />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="是否激活"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createStatusMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑状态模态框 */}
      <Modal
        title="编辑状态配置"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateStatus}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="状态键">
                <Input value={editingConfig?.key} disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="label"
                label="显示名称"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="如：自定义状态" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="icon"
                label="图标"
                rules={[{ required: true, message: '请输入图标' }]}
              >
                <Input placeholder="如：🎯" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="color"
                label="颜色"
                rules={[{ required: true, message: '请选择颜色' }]}
              >
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order"
                label="排序"
                rules={[{ required: true, message: '请输入排序' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入状态描述" />
          </Form.Item>

          <Form.Item
            name="allowedTransitions"
            label="允许转换到的状态"
          >
            <Select
              mode="multiple"
              placeholder="选择允许转换到的状态"
              options={statusConfigs?.filter(config => config.key !== editingConfig?.key)
                .map(config => ({
                  label: config.label,
                  value: config.key
                }))}
            />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="是否激活"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsEditModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateStatusMutation.isPending}
              >
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default RequirementStatusManagePage
