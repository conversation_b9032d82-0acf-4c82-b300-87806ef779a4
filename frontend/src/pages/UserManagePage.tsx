import React, { useState } from 'react'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tooltip,
  Popconfirm,
  Avatar
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  KeyOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userApi, authApi } from '../services/api'
import type { User, RegisterRequest } from '../types'
import { getFullAvatarUrl } from '../utils/url'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

const UserManagePage: React.FC = () => {
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取用户列表
  const { data: userListData, isLoading } = useQuery({
    queryKey: ['userList'],
    queryFn: () => userApi.getUserList(1, 100), // 获取所有用户
  })

  // 创建用户
  const createUserMutation = useMutation({
    mutationFn: authApi.register,
    onSuccess: () => {
      message.success('用户创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['userList'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '创建用户失败')
    },
  })

  // 重置密码
  const resetPasswordMutation = useMutation({
    mutationFn: userApi.resetPassword,
    onSuccess: () => {
      message.success('密码重置成功，新密码为：1234')
      queryClient.invalidateQueries({ queryKey: ['userList'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '密码重置失败')
    },
  })

  // 编辑用户
  const editUserMutation = useMutation({
    mutationFn: ({ userId, data }: { userId: number; data: any }) =>
      userApi.updateUserByAdmin(userId, data),
    onSuccess: () => {
      message.success('用户信息更新成功')
      setIsEditModalVisible(false)
      setEditingUser(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['userList'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新用户信息失败')
    },
  })

  const handleCreateUser = async (values: RegisterRequest) => {
    createUserMutation.mutate(values)
  }

  const handleResetPassword = (userId: number) => {
    resetPasswordMutation.mutate(userId)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    editForm.setFieldsValue({
      nickname: user.nickname,
      userType: user.userType,
    })
    setIsEditModalVisible(true)
  }

  const getUserStatusTag = (user: User) => {
    if (user.status === 'LOCKED') {
      return <Tag color="red" icon={<LockOutlined />}>已锁定</Tag>
    }
    if (user.status === 'DISABLED') {
      return <Tag color="orange">已禁用</Tag>
    }
    return <Tag color="green" icon={<UnlockOutlined />}>正常</Tag>
  }

  const getUserTypeTag = (userType: string) => {
    return userType === 'SUPER_ADMIN' ? (
      <Tag color="purple">超级管理员</Tag>
    ) : (
      <Tag color="blue">普通用户</Tag>
    )
  }

  const columns = [
    {
      title: '用户',
      key: 'user',
      render: (record: User) => (
        <Space>
          <Avatar
            src={getFullAvatarUrl(record.avatarUrl)}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.nickname || record.username}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              @{record.username}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '用户类型',
      dataIndex: 'userType',
      key: 'userType',
      render: (userType: string) => getUserTypeTag(userType),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: User) => getUserStatusTag(record),
    },
    {
      title: '登录失败次数',
      dataIndex: 'failedLoginCount',
      key: 'failedLoginCount',
      render: (count: number) => (
        <Tag color={count > 0 ? 'orange' : 'default'}>
          {count} 次
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: string) => dayjs(createdAt).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: User) => (
        <Space size="small">
          <Tooltip title="编辑用户">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要重置该用户的密码吗？"
            description="密码将重置为：1234"
            onConfirm={() => handleResetPassword(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="重置密码">
              <Button
                type="text"
                icon={<KeyOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>用户管理</Title>
        <Text type="secondary">
          管理系统用户，包括创建用户、重置密码、修改权限等
        </Text>
      </div>

      {/* 统计卡片 */}
      <div style={{ marginBottom: 24 }}>
        <Space size="large">
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {userListData?.total || 0}
              </div>
              <div style={{ color: '#666', fontSize: 12 }}>总用户数</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {userListData?.users?.filter(u => u.status === 'ACTIVE').length || 0}
              </div>
              <div style={{ color: '#666', fontSize: 12 }}>正常用户</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                {userListData?.users?.filter(u => u.userType === 'SUPER_ADMIN').length || 0}
              </div>
              <div style={{ color: '#666', fontSize: 12 }}>管理员</div>
            </div>
          </Card>
        </Space>
      </div>

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            创建用户
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => queryClient.invalidateQueries({ queryKey: ['userList'] })}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 用户列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={userListData?.users || []}
          rowKey="id"
          loading={isLoading}
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 创建用户模态框 */}
      <Modal
        title="创建用户"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false)
          createForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateUser}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
          >
            <Input placeholder="请输入昵称（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false)
                createForm.resetFields()
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createUserMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑用户模态框 */}
      <Modal
        title="编辑用户"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false)
          setEditingUser(null)
          editForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingUser) {
              editUserMutation.mutate({
                userId: editingUser.id,
                data: values
              })
            }
          }}
        >
          <Form.Item
            name="nickname"
            label="昵称"
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>

          <Form.Item
            name="userType"
            label="用户类型"
          >
            <Select placeholder="请选择用户类型">
              <Option value="NORMAL">普通用户</Option>
              <Option value="SUPER_ADMIN">超级管理员</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditModalVisible(false)
                setEditingUser(null)
                editForm.resetFields()
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={editUserMutation.isPending}
              >
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserManagePage
