import React, { useState } from 'react'
import { Card, Tabs, Typography } from 'antd'
import { SettingOutlined, FileTextOutlined, ProjectOutlined } from '@ant-design/icons'
import RequirementStatusManagePage from './RequirementStatusManagePage'
import TaskStatusManagePage from './TaskStatusManagePage'

const { Title } = Typography

const StatusManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('requirement')

  const tabItems = [
    {
      key: 'requirement',
      label: (
        <span>
          <FileTextOutlined />
          需求状态管理
        </span>
      ),
      children: <RequirementStatusManagePage />
    },
    {
      key: 'task',
      label: (
        <span>
          <ProjectOutlined />
          任务状态管理
        </span>
      ),
      children: <TaskStatusManagePage />
    }
  ]

  return (
    <div style={{ padding: 0 }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
            <SettingOutlined />
            状态管理
          </Title>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{
            marginBottom: 24,
            borderBottom: '2px solid #f0f0f0'
          }}
        />
      </Card>
    </div>
  )
}

export default StatusManagePage
