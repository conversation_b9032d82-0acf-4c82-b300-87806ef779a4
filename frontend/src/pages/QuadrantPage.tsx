import React from 'react'
import { Spin, Alert, Card, Typography } from 'antd'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { useQuery } from '@tanstack/react-query'
import { taskApi } from '../services/api'
import { useAuthStore } from '../stores/authStore'
import QuadrantMatrix from '../components/QuadrantMatrix'

const { Text } = Typography



const QuadrantPage: React.FC = () => {
  const { user } = useAuthStore()
  const isSuperAdmin = user?.userType === 'SUPER_ADMIN'

  const { data: quadrantData, isLoading, error } = useQuery({
    queryKey: ['quadrantData'],
    queryFn: taskApi.getQuadrantData,
    refetchInterval: 30000, // 每30秒刷新一次
  })

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <Spin size="large" tip="加载四象限数据中..." />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="加载失败"
          description="无法加载四象限数据，请检查网络连接或稍后重试。"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!quadrantData) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="暂无数据"
          description="当前没有任务数据可显示。"
          type="info"
          showIcon
        />
      </div>
    )
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div style={{ padding: '24px' }}>
        {/* 权限提示卡片 */}
        <Card
          size="small"
          style={{
            marginBottom: '16px',
            backgroundColor: isSuperAdmin ? '#f6ffed' : '#fff7e6',
            borderColor: isSuperAdmin ? '#b7eb8f' : '#ffd591'
          }}
        >
          <Text style={{
            color: isSuperAdmin ? '#52c41a' : '#fa8c16',
            fontWeight: 500
          }}>
            {isSuperAdmin ? (
              <>🔧 超级管理员视图：显示所有用户的任务</>
            ) : (
              <>👤 个人视图：仅显示您负责的任务</>
            )}
          </Text>
        </Card>

        <QuadrantMatrix data={quadrantData} />
      </div>
    </DndProvider>
  )
}

export default QuadrantPage
