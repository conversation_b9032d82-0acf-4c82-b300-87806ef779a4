import React, { useState, useMemo } from 'react'
import {
  Typo<PERSON>,
  Card,
  List,
  Tag,
  Space,
  Tooltip,
  Badge,
  Empty,
  Button,
  DatePicker,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  CalendarOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FlagOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { weeklyTaskApi } from '../services/api'
import type { Task, TaskDependency } from '../types'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import isoWeek from 'dayjs/plugin/isoWeek'

// 扩展dayjs插件
dayjs.extend(weekOfYear)
dayjs.extend(isoWeek)

const { Title, Text } = Typography

const WeeklyPlanPage: React.FC = () => {
  // 当前选择的周（默认为本周）
  const [selectedWeek, setSelectedWeek] = useState(dayjs())

  // 计算周的开始和结束日期
  const weekStart = selectedWeek.startOf('isoWeek')
  const weekEnd = selectedWeek.endOf('isoWeek')

  // 获取指定周的任务
  const { data: weeklyTasks = [], isLoading } = useQuery({
    queryKey: ['weekly-tasks', weekStart.format('YYYY-MM-DD'), weekEnd.format('YYYY-MM-DD')],
    queryFn: () => weeklyTaskApi.getWeeklyTasksByDateRange(
      weekStart.format('YYYY-MM-DD'),
      weekEnd.format('YYYY-MM-DD')
    )
  })

  // 翻页处理函数
  const handlePreviousWeek = () => {
    setSelectedWeek(prev => prev.subtract(1, 'week'))
  }

  const handleNextWeek = () => {
    setSelectedWeek(prev => prev.add(1, 'week'))
  }

  const handleToday = () => {
    setSelectedWeek(dayjs())
  }

  // 日历选择处理
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setSelectedWeek(date)
    }
  }

  // 按需求分组任务
  const tasksByRequirement = useMemo(() => {
    const grouped: { [key: string]: { requirement: any, tasks: Task[] } } = {}

    weeklyTasks.forEach(task => {
      const requirementKey = task.requirement ? `req_${task.requirement.id}` : 'no_requirement'

      if (!grouped[requirementKey]) {
        grouped[requirementKey] = {
          requirement: task.requirement || {
            id: 0,
            title: '未关联需求',
            businessDescription: '这些任务暂未关联到具体需求'
          },
          tasks: []
        }
      }

      grouped[requirementKey].tasks.push(task)
    })

    return Object.values(grouped).sort((a, b) => {
      // 未关联需求的放在最后
      if (a.requirement.id === 0) return 1
      if (b.requirement.id === 0) return -1
      return a.requirement.id - b.requirement.id
    })
  }, [weeklyTasks])

  // 计算任务统计
  const taskStats = {
    total: weeklyTasks.length,
    completed: weeklyTasks.filter(task => task.status === 'DONE').length,
    inProgress: weeklyTasks.filter(task => task.status === 'IN_PROGRESS').length,
    overdue: weeklyTasks.filter(task =>
      task.status !== 'DONE' &&
      task.dueDate &&
      dayjs(task.dueDate).isBefore(dayjs(), 'day')
    ).length,
    requirements: tasksByRequirement.length
  }

  // 获取优先级颜色
  const getPriorityColor = (importance: string, urgency: string): string => {
    if (importance === 'HIGH' && urgency === 'HIGH') return 'red'
    if (importance === 'HIGH' || urgency === 'HIGH') return 'orange'
    if (importance === 'MEDIUM' || urgency === 'MEDIUM') return 'blue'
    return 'default'
  }

  // 获取优先级文本
  const getPriorityText = (importance: string, urgency: string): string => {
    if (importance === 'HIGH' && urgency === 'HIGH') return '紧急重要'
    if (importance === 'HIGH') return '重要'
    if (urgency === 'HIGH') return '紧急'
    if (importance === 'MEDIUM' || urgency === 'MEDIUM') return '中等'
    return '一般'
  }

  // 检查上游任务是否有延期风险
  const hasUpstreamDelayRisk = (task: Task): boolean => {
    if (!task.dependencies || task.dependencies.length === 0) return false
    if (!task.dueDate) return false

    const taskDueDate = dayjs(task.dueDate)
    return task.dependencies.some(dep => {
      if (!dep.dependsOnTask?.dueDate) return false
      const depDueDate = dayjs(dep.dependsOnTask.dueDate)
      return depDueDate.isAfter(taskDueDate)
    })
  }

  // 检查任务是否过期（已完成的任务不算过期）
  const isTaskOverdue = (task: Task): boolean => {
    if (!task.dueDate) return false
    if (task.status === 'DONE') return false // 已完成的任务不算过期
    return dayjs(task.dueDate).isBefore(dayjs(), 'day')
  }

  // 获取任务状态对应的样式（极简版本，无背景和边框）
  const getTaskStatusStyle = (_task: Task) => {
    // 返回空对象，不应用任何背景色和边框
    return {}
  }

  // 获取状态标签的颜色
  const getStatusTagColor = (status: string): string => {
    const statusColors: Record<string, string> = {
      'TODO': 'default',
      'IN_PROGRESS': 'processing',
      'BLOCKED': 'warning',
      'DONE': 'success',
      'CANCELLED': 'error'
    }
    return statusColors[status] || 'default'
  }

  // 获取状态的中文显示
  const getStatusText = (status: string): string => {
    const statusTexts: Record<string, string> = {
      'TODO': '待开始',
      'IN_PROGRESS': '进行中',
      'BLOCKED': '阻塞',
      'DONE': '已完成',
      'CANCELLED': '已取消'
    }
    return statusTexts[status] || status
  }

  // 渲染上游任务信息
  const renderUpstreamTasks = (dependencies: TaskDependency[]) => {
    if (!dependencies || dependencies.length === 0) {
      return <Text type="secondary">无上游任务</Text>
    }

    return (
      <div>
        {dependencies.map(dep => (
          <div key={dep.id} style={{ marginBottom: '4px' }}>
            <Space size="small">
              <Text strong>{dep.dependsOnTask?.title}</Text>
              {dep.dependsOnTask?.assignee && (
                <Text type="secondary">
                  <UserOutlined /> {dep.dependsOnTask.assignee.nickname || dep.dependsOnTask.assignee.username}
                </Text>
              )}
              {dep.dependsOnTask?.dueDate && (
                <Text type="secondary">
                  <ClockCircleOutlined /> {dayjs(dep.dependsOnTask.dueDate).format('MM-DD')}
                </Text>
              )}
            </Space>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div style={{
      // padding: '24px',
      // backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      {/* 页面标题和周选择器 */}
      <div style={{
        marginBottom: '32px',
        backgroundColor: '#fff',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        border: '1px solid #f0f0f0'
      }}>
        <Row justify="space-between" align="middle" style={{ marginBottom: '20px' }}>
          <Col>
            <Title level={2} style={{ margin: 0, color: '#262626' }}>
              <CalendarOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
              周计划
            </Title>
          </Col>
          <Col>
            <Space>
              <Button onClick={handleToday}>今天</Button>
              <DatePicker
                value={selectedWeek}
                onChange={handleDateChange}
                picker="week"
                format="YYYY年第WW周"
                placeholder="选择周"
                allowClear={false}
              />
            </Space>
          </Col>
        </Row>

        {/* 周导航和信息 */}
        <Row justify="space-between" align="middle" style={{ marginBottom: '16px' }}>
          <Col>
            <Space size="large">
              <Button
                icon={<LeftOutlined />}
                onClick={handlePreviousWeek}
                type="text"
              >
                上一周
              </Button>
              <div style={{ textAlign: 'center', minWidth: '200px' }}>
                <Text strong style={{ fontSize: '16px' }}>
                  {weekStart.format('YYYY年第WW周')}
                </Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {weekStart.format('MM月DD日')} - {weekEnd.format('MM月DD日')}
                </Text>
              </div>
              <Button
                icon={<RightOutlined />}
                onClick={handleNextWeek}
                type="text"
              >
                下一周
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 任务统计 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={5}>
            <Card size="small">
              <Statistic
                title="关联需求"
                value={taskStats.requirements}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={5}>
            <Card size="small">
              <Statistic
                title="总任务"
                value={taskStats.total}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={5}>
            <Card size="small">
              <Statistic
                title="已完成"
                value={taskStats.completed}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={5}>
            <Card size="small">
              <Statistic
                title="进行中"
                value={taskStats.inProgress}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="已过期"
                value={taskStats.overdue}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>

        <Text type="secondary">
          显示 {weekStart.format('YYYY年MM月DD日')} 至 {weekEnd.format('YYYY年MM月DD日')} 期间截止的任务
        </Text>
      </div>

      {weeklyTasks.length === 0 && !isLoading ? (
        <Card>
          <Empty
            description={
              selectedWeek.isSame(dayjs(), 'week')
                ? "本周暂无任务"
                : `${weekStart.format('MM月DD日')} - ${weekEnd.format('MM月DD日')} 期间暂无任务`
            }
          />
        </Card>
      ) : (
        <div style={{ padding: '0 8px' }}>
          {tasksByRequirement.map((group, index) => (
            <Card
              key={group.requirement.id || `no-req-${index}`}
              style={{
                marginBottom: '24px',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                border: '1px solid #f0f0f0'
              }}
              styles={{ body: { padding: '16px 24px' } }}
              title={
                <div style={{ padding: '8px 0' }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    <Text strong style={{
                      fontSize: '18px',
                      color: group.requirement.id === 0 ? '#666' : '#1890ff',
                      marginRight: '12px'
                    }}>
                      📋 {group.requirement.title}
                    </Text>
                    <Badge
                      count={group.tasks.length}
                      style={{
                        backgroundColor: group.requirement.id === 0 ? '#d9d9d9' : '#52c41a',
                        fontSize: '12px',
                        height: '20px',
                        lineHeight: '20px',
                        minWidth: '20px'
                      }}
                    />
                  </div>
                  {group.requirement.businessDescription && (
                    <Text style={{
                      fontSize: '14px',
                      color: '#8c8c8c',
                      lineHeight: '1.5',
                      display: 'block',
                      marginTop: '4px'
                    }}>
                      {group.requirement.businessDescription}
                    </Text>
                  )}
                </div>
              }
            >
              <List
                dataSource={group.tasks}
                split={false}
                renderItem={(task: Task, index: number) => {
                  const hasDelayRisk = hasUpstreamDelayRisk(task)
                  const isOverdue = isTaskOverdue(task)
                  const taskStyle = getTaskStatusStyle(task)
                  const isLastItem = index === group.tasks.length - 1

                  return (
                    <List.Item style={{
                      padding: '16px 0',
                      marginBottom: '0',
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderBottom: isLastItem ? 'none' : '1px solid #f0f0f0',
                      borderRadius: 'none',
                      boxShadow: 'none',
                      transition: 'all 0.2s ease',
                      cursor: 'default',
                      position: 'relative'
                    }}>
                      <div style={{
                        width: '100%',
                        ...taskStyle,
                        position: 'relative'
                      }}>
                        {/* 左侧状态指示条 */}
                        <div style={{
                          position: 'absolute',
                          left: '-8px',
                          top: '0',
                          bottom: '0',
                          width: '3px',
                          backgroundColor: task.status === 'DONE' ? '#52c41a' :
                            task.status === 'BLOCKED' ? '#ff7875' :
                              isOverdue ? '#ff4d4f' : '#1890ff',
                          borderRadius: '2px'
                        }} />
                        {/* 任务标题和状态指示器 */}
                        <div style={{ marginBottom: '12px' }}>
                          <Space align="start">
                            <Title level={5} style={{
                              margin: 0,
                              color: task.status === 'DONE' ? '#52c41a' : '#262626',
                              fontSize: '16px',
                              fontWeight: 600,
                              lineHeight: '1.4'
                            }}>
                              {task.status === 'DONE' && '✓ '}
                              {task.title}
                            </Title>
                            {hasDelayRisk && (
                              <Tooltip title="上游任务截止时间晚于本任务，存在延期风险">
                                <Badge
                                  count={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                                />
                              </Tooltip>
                            )}
                            {isOverdue && (
                              <Tooltip title="任务已过期">
                                <Badge
                                  count="过期"
                                  style={{ backgroundColor: '#ff4d4f' }}
                                />
                              </Tooltip>
                            )}
                            {task.status === 'BLOCKED' && (
                              <Tooltip title="任务被阻塞">
                                <Badge
                                  count="阻塞"
                                  style={{ backgroundColor: '#fa8c16' }}
                                />
                              </Tooltip>
                            )}
                          </Space>
                        </div>

                        {/* 任务信息 */}
                        <div style={{ marginBottom: '16px' }}>
                          <Space wrap size={[8, 8]}>
                            {/* 优先级 */}
                            <Tag
                              color={getPriorityColor(task.priorityImportance, task.priorityUrgency)}
                              icon={<FlagOutlined />}
                              style={{
                                borderRadius: '12px',
                                fontSize: '12px',
                                padding: '4px 10px',
                                border: 'none',
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                              }}
                            >
                              {getPriorityText(task.priorityImportance, task.priorityUrgency)}
                            </Tag>

                            {/* 负责人 */}
                            {task.assignee && (
                              <Tag
                                icon={<UserOutlined />}
                                style={{
                                  borderRadius: '12px',
                                  fontSize: '12px',
                                  padding: '4px 10px',
                                  backgroundColor: '#f0f2f5',
                                  color: '#595959',
                                  border: 'none',
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)'
                                }}
                              >
                                {task.assignee.nickname || task.assignee.username}
                              </Tag>
                            )}

                            {/* 截止日期 */}
                            {task.dueDate && (
                              <Tag
                                icon={<ClockCircleOutlined />}
                                color={isTaskOverdue(task) ? 'red' : 'blue'}
                                style={{
                                  borderRadius: '12px',
                                  fontSize: '12px',
                                  padding: '4px 10px',
                                  border: 'none',
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                                }}
                              >
                                {dayjs(task.dueDate).format('MM-DD dddd')}
                                {isTaskOverdue(task) && ' (已过期)'}
                              </Tag>
                            )}

                            {/* 状态 */}
                            <Tag
                              color={getStatusTagColor(task.status)}
                              style={{
                                borderRadius: '12px',
                                fontSize: '12px',
                                padding: '4px 10px',
                                border: 'none',
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                              }}
                            >
                              {getStatusText(task.status)}
                            </Tag>
                          </Space>
                        </div>

                        {/* 上游任务信息 */}
                        {task.dependencies && task.dependencies.length > 0 && (
                          <div style={{
                            fontSize: '12px',
                            color: '#8c8c8c',
                            backgroundColor: 'transparent',
                            padding: '8px 0',
                            borderRadius: 'none',
                            marginBottom: '12px',
                            border: 'none',
                            borderLeft: '2px solid #e8e8e8',
                            paddingLeft: '12px'
                          }}>
                            <Text strong style={{ color: '#595959', fontSize: '12px' }}>上游任务：</Text>
                            <div style={{ marginTop: '6px' }}>
                              {renderUpstreamTasks(task.dependencies)}
                            </div>
                          </div>
                        )}

                        {/* 延期风险警告 */}
                        {hasDelayRisk && (
                          <div style={{
                            marginTop: '12px',
                            padding: '8px 0',
                            fontSize: '12px',
                            color: '#fa8c16',
                            borderLeft: '3px solid #fa8c16',
                            paddingLeft: '12px',
                            backgroundColor: 'transparent'
                          }}>
                            <span style={{ fontWeight: 'bold' }}>⚠️ 延期风险：</span>
                            上游任务的截止时间晚于本任务，可能影响按时完成
                          </div>
                        )}
                      </div>
                    </List.Item>
                  )
                }}
              />
            </Card>
          ))}
        </div>


      )}
    </div>
  )
}

export default WeeklyPlanPage
