import React, { useState } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Card,
  Form,
  Input,
  Select,
  Tooltip,
  Empty,
  Modal,
  Descriptions,
  List,
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  InboxOutlined,
  RollbackOutlined,
} from '@ant-design/icons'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { requirementApi, userApi } from '../services/api'
import type {
  Requirement,
  User,
} from '../types'
import dayjs from 'dayjs'
import { usePermission } from '../components/PermissionGuard'
import { message } from 'antd'

const { Title, Text } = Typography
const { Option } = Select

const ArchivedRequirementsPage: React.FC = () => {
  const [searchForm] = Form.useForm()
  const queryClient = useQueryClient()
  const { isSuperAdmin, user } = usePermission()

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 20,
    assigneeId: undefined as number | undefined,
    creatorId: undefined as number | undefined,
    search: undefined as string | undefined,
  })

  // 详情查看状态
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [viewingRequirement, setViewingRequirement] = useState<Requirement | null>(null)

  // 撤回操作状态
  const [isRollbacking, setIsRollbacking] = useState(false)

  // 获取已归档需求列表
  const { data: requirementData, isLoading, refetch } = useQuery({
    queryKey: ['archived-requirements', searchParams],
    queryFn: () => requirementApi.getArchivedRequirements(searchParams),
  })

  // 获取用户列表（用于筛选）
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: userApi.getAllUsers,
  })

  // 处理搜索
  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1, // 重置到第一页
      assigneeId: values.assigneeId,
      creatorId: values.creatorId,
      search: values.search?.trim() || undefined,
    })
  }

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields()
    setSearchParams({
      page: 1,
      pageSize: 20,
      assigneeId: undefined,
      creatorId: undefined,
      search: undefined,
    })
  }

  // 处理分页
  const handleTableChange = (page: number, pageSize: number) => {
    setSearchParams({
      ...searchParams,
      page,
      pageSize,
    })
  }

  // 处理查看详情
  const handleViewDetail = (requirement: Requirement) => {
    setViewingRequirement(requirement)
    setIsDetailModalVisible(true)
  }

  // 关闭详情模态框
  const handleCloseDetailModal = () => {
    setIsDetailModalVisible(false)
    setViewingRequirement(null)
  }

  // 检查是否可以撤回需求
  const canRollbackRequirement = (requirement: Requirement): boolean => {
    // 超级管理员可以撤回任何需求
    if (isSuperAdmin()) return true

    // 创建者可以撤回自己的需求
    if (requirement.creatorId === user?.id) return true

    // 负责人可以撤回自己负责的需求
    if (requirement.assigneeId === user?.id) return true

    return false
  }

  // 处理撤回需求
  const handleRollbackRequirement = async (requirement: Requirement) => {
    if (!canRollbackRequirement(requirement)) {
      message.error('您没有权限撤回此需求')
      return
    }

    setIsRollbacking(true)
    try {
      await requirementApi.updateRequirementStatus(requirement.id, 'DELIVERED')
      message.success('需求已撤回并设置为已交付状态')

      // 刷新数据
      queryClient.invalidateQueries({ queryKey: ['archived-requirements'] })

      // 如果当前正在查看详情，关闭详情模态框
      if (viewingRequirement?.id === requirement.id) {
        setViewingRequirement(null)
        setIsDetailModalVisible(false)
      }
    } catch (error) {
      console.error('撤回需求失败:', error)
      message.error('撤回需求失败，请重试')
    } finally {
      setIsRollbacking(false)
    }
  }

  // 获取优先级颜色
  const getPriorityColor = (importance: string, urgency: string): string => {
    if (importance === 'HIGH' && urgency === 'HIGH') return 'red'
    if (importance === 'HIGH' || urgency === 'HIGH') return 'orange'
    if (importance === 'MEDIUM' || urgency === 'MEDIUM') return 'blue'
    return 'default'
  }

  // 获取优先级文本
  const getPriorityText = (importance: string, urgency: string): string => {
    if (importance === 'HIGH' && urgency === 'HIGH') return '紧急重要'
    if (importance === 'HIGH') return '重要'
    if (urgency === 'HIGH') return '紧急'
    if (importance === 'MEDIUM' || urgency === 'MEDIUM') return '中等'
    return '一般'
  }

  // 表格列定义
  const columns = [
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      width: 300,
      render: (text: string, record: Requirement) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.id}
          </Text>
        </div>
      ),
    },
    {
      title: '所属项目',
      dataIndex: 'project',
      key: 'project',
      width: 150,
      render: (project: any) => (
        <Tag color="blue">{project?.name || '未知项目'}</Tag>
      ),
    },
    {
      title: '优先级',
      key: 'priority',
      width: 120,
      render: (record: Requirement) => (
        <Tag color={getPriorityColor(record.priorityImportance, record.priorityUrgency)}>
          {getPriorityText(record.priorityImportance, record.priorityUrgency)}
        </Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (assignee: User) => (
        assignee ? (
          <Tag>{assignee.nickname || assignee.username}</Tag>
        ) : (
          <Text type="secondary">未分配</Text>
        )
      ),
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 120,
      render: (creator: User) => (
        <Tag color="green">{creator?.nickname || creator?.username}</Tag>
      ),
    },
    {
      title: '任务数量',
      dataIndex: 'tasks',
      key: 'taskCount',
      width: 100,
      render: (tasks: any[]) => (
        <Tag color="purple">{tasks?.length || 0} 个任务</Tag>
      ),
    },
    {
      title: '归档时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (date: string) => (
        <Text>{dayjs(date).format('YYYY-MM-DD HH:mm')}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: Requirement) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {canRollbackRequirement(record) && (
            <Tooltip title="撤回归档，设置为已交付">
              <Button
                type="text"
                icon={<RollbackOutlined />}
                loading={isRollbacking}
                onClick={() => handleRollbackRequirement(record)}
                style={{ color: '#fa8c16' }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{
        backgroundColor: '#ffffff',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        marginBottom: '24px'
      }}>
        <Space align="center" style={{ marginBottom: '16px' }}>
          <InboxOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <Title level={2} style={{ margin: 0 }}>已归档需求</Title>
        </Space>
        <Text type="secondary">
          查看和管理已归档的需求，支持按标题、负责人等条件筛选
        </Text>
      </div>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: '16px' }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ width: '100%' }}
        >
          <Form.Item name="search" style={{ minWidth: 200 }}>
            <Input
              placeholder="搜索需求标题"
              allowClear
            />
          </Form.Item>
          <Form.Item name="assigneeId" style={{ minWidth: 150 }}>
            <Select
              placeholder="选择负责人"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="creatorId" style={{ minWidth: 150 }}>
            <Select
              placeholder="选择创建者"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                重置
              </Button>
              <Button onClick={() => refetch()} icon={<ReloadOutlined />}>
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 统计信息 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Text type="secondary">
            共 {requirementData?.total || 0} 条已归档需求
          </Text>
        </div>
      </div>

      {/* 需求列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={requirementData?.requirements || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: requirementData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          locale={{
            emptyText: (
              <Empty
                description="暂无已归档需求"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ),
          }}
        />
      </Card>

      {/* 详情查看模态框 */}
      <Modal
        title="需求详情"
        open={isDetailModalVisible}
        onCancel={handleCloseDetailModal}
        footer={[
          <Button key="close" onClick={handleCloseDetailModal}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingRequirement && (
          <div>
            <Descriptions
              title="基本信息"
              bordered
              column={2}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="需求ID">
                {viewingRequirement.id}
              </Descriptions.Item>
              <Descriptions.Item label="需求标题">
                {viewingRequirement.title}
              </Descriptions.Item>
              <Descriptions.Item label="所属项目">
                <Tag color="blue">
                  {viewingRequirement.project?.name || '未知项目'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color="default">已归档</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="优先级">
                <Tag color={getPriorityColor(viewingRequirement.priorityImportance, viewingRequirement.priorityUrgency)}>
                  {getPriorityText(viewingRequirement.priorityImportance, viewingRequirement.priorityUrgency)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                {viewingRequirement.assignee ? (
                  <Tag>{viewingRequirement.assignee.nickname || viewingRequirement.assignee.username}</Tag>
                ) : (
                  <span style={{ color: '#999' }}>未分配</span>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="创建者">
                <Tag color="green">
                  {viewingRequirement.creator?.nickname || viewingRequirement.creator?.username}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(viewingRequirement.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="归档时间">
                {dayjs(viewingRequirement.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="期望交付时间">
                {viewingRequirement.expectedDeliveryDate
                  ? dayjs(viewingRequirement.expectedDeliveryDate).format('YYYY-MM-DD HH:mm:ss')
                  : '未设置'
                }
              </Descriptions.Item>
              <Descriptions.Item label="实际交付时间">
                {viewingRequirement.actualDeliveryDate
                  ? dayjs(viewingRequirement.actualDeliveryDate).format('YYYY-MM-DD HH:mm:ss')
                  : '未设置'
                }
              </Descriptions.Item>
              <Descriptions.Item label="预估价值">
                {viewingRequirement.estimatedValue || '未设置'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions
              title="详细描述"
              bordered
              column={1}
              style={{ marginBottom: 24 }}
            >
              <Descriptions.Item label="业务描述">
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {viewingRequirement.businessDescription || '无'}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="验收标准">
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {viewingRequirement.acceptanceCriteria || '无'}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="目标用户">
                {viewingRequirement.targetUsers || '未设置'}
              </Descriptions.Item>
              <Descriptions.Item label="业务目标">
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {viewingRequirement.businessGoal || '未设置'}
                </div>
              </Descriptions.Item>
            </Descriptions>

            {/* 关联任务列表 */}
            {viewingRequirement.tasks && viewingRequirement.tasks.length > 0 && (
              <div>
                <Typography.Title level={5} style={{ marginBottom: 16 }}>
                  关联任务 ({viewingRequirement.tasks.length} 个)
                </Typography.Title>
                <List
                  dataSource={viewingRequirement.tasks}
                  renderItem={(task: any) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <span>{task.title}</span>
                            <Tag color={task.status === 'DONE' ? 'green' : 'blue'}>
                              {task.status === 'DONE' ? '已完成' :
                               task.status === 'IN_PROGRESS' ? '进行中' :
                               task.status === 'TODO' ? '待开始' : task.status}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space>
                            {task.assignee && (
                              <span>负责人: {task.assignee.nickname || task.assignee.username}</span>
                            )}
                            {task.dueDate && (
                              <span>截止: {dayjs(task.dueDate).format('MM-DD HH:mm')}</span>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ArchivedRequirementsPage
