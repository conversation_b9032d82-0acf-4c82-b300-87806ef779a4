import React, { useState } from 'react'
import {
  Typography,
  Card,
  List,
  Tag,
  Space,
  Avatar,
  Button,
  DatePicker,
  Select,
  Switch,
  Row,
  Col,
  Statistic,
  Empty,
  Spin,
  message,
} from 'antd'
import {
  UserOutlined,
  ProjectOutlined,
  CalendarOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { weeklyReportApi } from '../services/weeklyReportApi'
import { projectApi, userApi } from '../services/api'
import type { WeeklyReport, WeeklyRequirementItem, WeeklyTaskItem, User } from '../types'
import { getFullAvatarUrl } from '../utils/url'
import { getTaskStatusLabel, getTaskStatusColor } from '../config/taskStatus'
import { exportWeeklyReportToPDF } from '../utils/pdfExport'
import dayjs from 'dayjs'

const { Title, Text, Paragraph } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

const WeeklyReportPage: React.FC = () => {
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([])
  const [selectedAssigneeIds, setSelectedAssigneeIds] = useState<number[]>([])
  const [includeArchived, setIncludeArchived] = useState(false)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)

  // 获取项目选项
  const { data: projectOptions = [] } = useQuery({
    queryKey: ['project-options'],
    queryFn: () => projectApi.getProjectOptions()
  })

  // 获取用户列表
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: userApi.getAllUsers
  })

  // 获取周报数据
  const {
    data: weeklyReport,
    isLoading,
    refetch
  } = useQuery<WeeklyReport>({
    queryKey: ['weeklyReport', selectedProjectIds, selectedAssigneeIds, includeArchived, dateRange],
    queryFn: () => {
      if (dateRange) {
        return weeklyReportApi.generateWeeklyReport({
          startDate: dateRange[0].format('YYYY-MM-DD'),
          endDate: dateRange[1].format('YYYY-MM-DD'),
          projectIds: selectedProjectIds.length > 0 ? selectedProjectIds : undefined,
          assigneeIds: selectedAssigneeIds.length > 0 ? selectedAssigneeIds : undefined,
          includeArchived
        })
      } else {
        return weeklyReportApi.getWeeklyReport({
          projectId: selectedProjectIds.length > 0 ? selectedProjectIds : undefined,
          assigneeId: selectedAssigneeIds.length > 0 ? selectedAssigneeIds : undefined,
          includeArchived
        })
      }
    }
  })

  const handleRefresh = () => {
    refetch()
    message.success('周报已刷新')
  }

  const handleExport = async () => {
    if (!weeklyReport) {
      message.error('暂无数据可导出')
      return
    }

    try {
      message.loading('正在生成PDF...', 0)
      await exportWeeklyReportToPDF(weeklyReport)
      message.destroy()
      message.success('PDF导出成功')
    } catch (error) {
      message.destroy()
      console.error('PDF导出失败:', error)
      message.error('PDF导出失败，请重试')
    }
  }



  // 按项目分组需求
  const groupRequirementsByProject = (requirements: WeeklyRequirementItem[]) => {
    return requirements.reduce((groups, req) => {
      const projectName = req.project.name
      if (!groups[projectName]) {
        groups[projectName] = []
      }
      groups[projectName].push(req)
      return groups
    }, {} as Record<string, WeeklyRequirementItem[]>)
  }

  // 渲染项目分组
  const renderProjectGroup = (projectName: string, requirements: WeeklyRequirementItem[]) => (
    <Card key={projectName} style={{ marginBottom: 16 }}>
      <Title level={4} style={{ marginBottom: 16, color: '#1890ff' }}>
        <ProjectOutlined style={{ marginRight: 8 }} />
        {projectName} ({requirements.length})
      </Title>

      <List
        dataSource={requirements}
        renderItem={(requirement) => renderRequirementItem(requirement)}
        split={false}
      />
    </Card>
  )

  const renderRequirementItem = (requirement: WeeklyRequirementItem) => (
    <List.Item key={requirement.id} style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
      <div style={{ width: '100%' }}>
        {/* 需求标题 */}
        <div style={{ marginBottom: 8 }}>
          <Title level={5} style={{ margin: 0, fontSize: 16 }}>
            {requirement.title}
            <Tag
              color={requirement.statusColor}
              style={{ marginLeft: 8 }}
            >
              {requirement.statusLabel}
            </Tag>
          </Title>
        </div>

        {/* 需求描述 */}
        <Paragraph
          style={{
            color: '#666',
            fontSize: 14,
            marginBottom: 8,
            lineHeight: 1.5
          }}
          ellipsis={{ rows: 2, expandable: true }}
        >
          {requirement.description}
        </Paragraph>

        {/* 负责人 */}
        {requirement.assignee && (
          <div style={{ marginBottom: 8 }}>
            <Space>
              <Avatar
                size="small"
                src={getFullAvatarUrl(requirement.assignee.avatarUrl)}
                icon={<UserOutlined />}
              />
              <Text type="secondary" style={{ fontSize: 13 }}>
                负责人: {requirement.assignee.nickname || requirement.assignee.username}
              </Text>
            </Space>
          </div>
        )}

        {/* 关联任务 */}
        {requirement.tasks.length > 0 && (
          <div style={{ marginTop: 12, paddingLeft: 16, borderLeft: '3px solid #f0f0f0' }}>
            <Text strong style={{ fontSize: 13, color: '#666' }}>
              关联任务 ({requirement.tasks.length}):
            </Text>
            <div style={{ marginTop: 6 }}>
              {requirement.tasks.map((task: WeeklyTaskItem) => (
                <div
                  key={task.id}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '4px 0',
                    fontSize: 13
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <Text style={{ fontSize: 13 }}>{task.title}</Text>
                    {task.assignee && (
                      <Text type="secondary" style={{ fontSize: 12, marginLeft: 8 }}>
                        ({task.assignee.nickname || task.assignee.username})
                      </Text>
                    )}
                  </div>
                  <Tag
                    color={getTaskStatusColor(task.status)}
                    style={{ fontSize: 11 }}
                  >
                    {getTaskStatusLabel(task.status)}
                  </Tag>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </List.Item>
  )

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <FileTextOutlined style={{ marginRight: 8 }} />
          周报
        </Title>
        <Text type="secondary">
          查看非归档状态的需求及其关联任务
        </Text>
      </div>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div>
              <Text strong>日期范围:</Text>
              <RangePicker
                style={{ width: '100%', marginTop: 4 }}
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                placeholder={['开始日期', '结束日期']}
                allowClear
              />
            </div>
          </Col>
          <Col span={5}>
            <div>
              <Text strong>项目筛选:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 4 }}
                placeholder="选择项目"
                value={selectedProjectIds}
                onChange={setSelectedProjectIds}
                allowClear
              >
                {projectOptions.map(project => (
                  <Option key={project.id} value={project.id}>
                    {project.name}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={5}>
            <div>
              <Text strong>负责人筛选:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 4 }}
                placeholder="选择负责人"
                value={selectedAssigneeIds}
                onChange={setSelectedAssigneeIds}
                allowClear
              >
                {users.map(user => (
                  <Option key={user.id} value={user.id}>
                    {user.nickname || user.username}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={4}>
            <div>
              <Text strong>包含归档:</Text>
              <div style={{ marginTop: 4 }}>
                <Switch
                  checked={includeArchived}
                  onChange={setIncludeArchived}
                  checkedChildren="是"
                  unCheckedChildren="否"
                />
              </div>
            </div>
          </Col>
          <Col span={4}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={isLoading}
              >
                刷新
              </Button>
              <Button
                icon={<FilePdfOutlined />}
                onClick={handleExport}
              >
                导出PDF
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : weeklyReport ? (
        <>
          {/* 统计摘要 */}
          <Card style={{ marginBottom: 24 }}>
            <Title level={4}>
              <CalendarOutlined style={{ marginRight: 8 }} />
              周报摘要 ({weeklyReport.weekRange})
            </Title>
            <Row gutter={16}>
              <Col span={4}>
                <Statistic
                  title="需求总数"
                  value={weeklyReport.summary.totalRequirements}
                  prefix={<ProjectOutlined />}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="任务总数"
                  value={weeklyReport.summary.totalTasks}
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="活跃项目"
                  value={weeklyReport.summary.activeProjects}
                  prefix={<ProjectOutlined />}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="已完成需求"
                  value={weeklyReport.summary.completedRequirements}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="已完成任务"
                  value={weeklyReport.summary.completedTasks}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 需求列表 - 按项目分组 */}
          <Card>
            <Title level={4}>
              需求详情 ({weeklyReport.requirements.length})
            </Title>
            {weeklyReport.requirements.length > 0 ? (
              <div>
                {Object.entries(groupRequirementsByProject(weeklyReport.requirements)).map(([projectName, requirements]) =>
                  renderProjectGroup(projectName, requirements)
                )}
              </div>
            ) : (
              <Empty
                description="暂无需求数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Card>
        </>
      ) : (
        <Card>
          <Empty description="暂无数据" />
        </Card>
      )}
    </div>
  )
}

export default WeeklyReportPage
