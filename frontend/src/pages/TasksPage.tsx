import React, { useState } from 'react'
// import { useNavigate } from 'react-router-dom'
import {
  Typography,
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
  Pagination
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  CheckSquareOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { taskApi, requirementApi, userApi } from '../services/api'
import type { Task, TaskCreateRequest } from '../types'
import { useAuthStore } from '../stores/authStore'
import TaskTableView from '../components/TaskTableView'
import TaskDetailModal from '../components/TaskDetailModal'
import TaskEditModal from '../components/TaskEditModal'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select


const TasksPage: React.FC = () => {
  // const navigate = useNavigate()
  const { user: currentUser } = useAuthStore()
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [selectedRequirementId, setSelectedRequirementId] = useState<number | null>(null)
  const [viewingTask, setViewingTask] = useState<Task | null>(null)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  // 移除视图切换，只使用树形视图
  const [createForm] = Form.useForm()
  const [searchForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 搜索参数状态 - 每页5条需求
  const [searchParams, setSearchParams] = useState<any>({
    page: 1,
    pageSize: 5 // 每页只显示5条需求
  })



  // 1. 先获取有任务的需求列表（分页，每页5条）
  const { data: requirementListData, isLoading: requirementsLoading } = useQuery({
    queryKey: ['requirements-with-tasks', searchParams],
    queryFn: () => requirementApi.getRequirementsWithTasks({
      page: searchParams.page,
      pageSize: searchParams.pageSize,
      search: searchParams.requirementSearch // 添加需求搜索参数
    })
  })

  // 2. 根据需求ID查询关联的任务（使用单个查询获取所有任务）
  const requirements = requirementListData?.requirements || []
  const requirementIds = requirements.map(req => req.id)

  const { data: tasksData, isLoading: tasksLoading } = useQuery({
    queryKey: ['tasks-by-requirements', requirementIds],
    queryFn: async () => {
      if (requirementIds.length === 0) {
        return { tasks: [], total: 0 }
      }

      // 为每个需求ID发起查询并合并结果
      const taskPromises = requirementIds.map(reqId =>
        taskApi.getTaskList({
          requirementId: reqId,
          page: 1,
          pageSize: 1000
        })
      )

      const taskResults = await Promise.all(taskPromises)
      const allTasks = taskResults.reduce((acc, result) => {
        if (result.tasks) {
          acc.push(...result.tasks)
        }
        return acc
      }, [] as any[])

      return {
        tasks: allTasks,
        total: allTasks.length
      }
    },
    enabled: requirementIds.length > 0
  })

  // 构造最终的任务列表数据
  const taskListData = {
    tasks: tasksData?.tasks || [],
    total: tasksData?.total || 0
  }

  const isLoading = requirementsLoading || tasksLoading

  // 获取需求选项
  const { data: requirementOptions } = useQuery({
    queryKey: ['requirement-options'],
    queryFn: () => requirementApi.getRequirementOptions()
  })

  // 获取上游任务选项（用于创建任务）
  const { data: upstreamTaskOptions } = useQuery({
    queryKey: ['upstream-task-options', selectedRequirementId],
    queryFn: () => taskApi.getUpstreamTaskOptions(selectedRequirementId!),
    enabled: !!selectedRequirementId
  })



  // 获取用户列表
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: () => userApi.getAllUsers()
  })



  // 创建任务
  const createTaskMutation = useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: () => {
      message.success('任务创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      // 刷新任务相关的所有查询缓存
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
      queryClient.invalidateQueries({ queryKey: ['kanban'] })
      queryClient.invalidateQueries({ queryKey: ['task-status-statistics'] }) // 刷新状态统计
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务创建失败')
    }
  })



  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      message.success('任务删除成功')
      // 刷新所有相关查询
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirement'] })
      queryClient.invalidateQueries({ queryKey: ['kanban'] })
      queryClient.invalidateQueries({ queryKey: ['requirements-with-tasks'] })
      queryClient.invalidateQueries({ queryKey: ['task-status-statistics'] }) // 刷新状态统计
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务删除失败')
    }
  })



  const handleCreateTask = (values: any) => {
    const taskData: TaskCreateRequest = {
      ...values,
      dueDate: values.dueDate ? values.dueDate.toISOString() : undefined,
      dependencies: values.dependencies || []
    }
    createTaskMutation.mutate(taskData)
  }

  const handleRequirementChange = async (requirementId: number) => {
    setSelectedRequirementId(requirementId)
    // 清空依赖任务选择
    createForm.setFieldsValue({ dependencies: [] })

    // 实时查询该需求下的所有任务，用于依赖选择
    try {
      // 刷新该需求下的任务数据，确保获取最新数据
      queryClient.invalidateQueries({ queryKey: ['tasks-by-requirement', requirementId] })
      queryClient.invalidateQueries({ queryKey: ['upstream-task-options', requirementId] })
    } catch (error) {
      console.error('刷新任务数据失败:', error)
    }
  }

  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setIsEditModalVisible(true)
  }

  // 搜索处理 - 只处理需求搜索
  const handleSearch = (values: any) => {
    const params = {
      requirementSearch: values.requirementSearch,
      page: 1,
      pageSize: searchParams.pageSize
    }
    setSearchParams(params)
  }

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields()
    setSearchParams({
      page: 1,
      pageSize: 5 // 保持每页5条需求的设置
    })
  }

  // 分页处理
  const handlePageChange = (page: number, pageSize?: number) => {
    setSearchParams({
      ...searchParams,
      page: page,
      pageSize: pageSize || searchParams.pageSize
    })
  }



  // 移除未使用的 handleDeleteTask 函数

  // 查看任务详情
  const handleViewTask = (task: Task) => {
    setViewingTask(task)
    setIsDetailModalVisible(true)
  }

  const handleCloseDetailModal = () => {
    setIsDetailModalVisible(false)
    setViewingTask(null)
  }

  // 树形视图的删除处理（需要传入 Task 对象）
  const handleTreeDeleteTask = (task: Task) => {
    deleteTaskMutation.mutate(task.id)
  }



  // 移除表格相关的辅助函数和列定义，只使用树形视图


  return (
    <div style={{
      padding: '24px',
      backgroundColor: '#fef7ff', // Material 3 surface-container-lowest
      minHeight: '100vh'
    }}>
      {/* Material 3 页面标题区域 */}
      <div style={{
        backgroundColor: '#ffffff',
        padding: '32px 24px',
        borderRadius: '28px', // Material 3 extra-large 圆角
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)', // Material 3 elevation-1
        marginBottom: '24px',
        border: 'none'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              padding: '12px',
              backgroundColor: '#eaddff', // Material 3 primary-container
              borderRadius: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CheckSquareOutlined style={{
                fontSize: '24px',
                color: '#6750a4' // Material 3 primary color
              }} />
            </div>
            <Title level={1} style={{
              margin: 0,
              // Material 3 Display Small
              fontSize: '36px',
              fontWeight: '400',
              lineHeight: '44px',
              letterSpacing: '0px',
              color: '#1d1b20' // Material 3 on-surface
            }}>任务管理</Title>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="large"
            onClick={() => {
              setIsCreateModalVisible(true)
              // 设置默认负责人为当前用户
              createForm.setFieldsValue({
                assigneeId: currentUser?.id
              })
            }}
            style={{
              borderRadius: '20px', // Material 3 圆角
              height: '48px',
              padding: '0 24px',
              fontSize: '14px',
              fontWeight: '500',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
            }}
          >
            创建任务
          </Button>
        </div>
        <Text style={{
          // Material 3 Body Large
          fontSize: '16px',
          lineHeight: '24px',
          letterSpacing: '0.5px',
          color: '#49454f' // Material 3 on-surface-variant
        }}>
          管理和跟踪项目任务，支持按需求分组查看和编辑
        </Text>
      </div>

      {/* Material 3 搜索筛选区域 */}
      <Card
        title={
          <span style={{
            // Material 3 Title Medium
            fontSize: '16px',
            fontWeight: '500',
            lineHeight: '24px',
            letterSpacing: '0.15px',
            color: '#1d1b20'
          }}>
            需求搜索
          </span>
        }
        style={{
          marginBottom: '24px',
          borderRadius: '16px', // Material 3 圆角
          border: 'none',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)', // Material 3 elevation-1
          backgroundColor: '#ffffff'
        }}
        styles={{
          header: {
            padding: '20px 24px 16px 24px',
            borderBottom: '1px solid #e7e0ec' // Material 3 outline-variant
          },
          body: {
            padding: '16px 24px 20px 24px'
          }
        }}
      >
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 0 }}
        >
          <Form.Item
            name="requirementSearch"
            label={
              <span style={{
                // Material 3 Body Medium
                fontSize: '14px',
                fontWeight: '500',
                lineHeight: '20px',
                letterSpacing: '0.25px',
                color: '#49454f'
              }}>
                关联需求
              </span>
            }
          >
            <Input
              placeholder="请输入需求标题进行搜索"
              allowClear
              style={{
                width: 320,
                height: '48px', // Material 3 标准高度
                borderRadius: '12px', // Material 3 圆角
                fontSize: '16px',
                border: '1px solid #79747e',
                backgroundColor: '#ffffff'
              }}
            />
          </Form.Item>
          <Form.Item>
            <Space size={12}>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                style={{
                  height: '48px',
                  borderRadius: '24px', // Material 3 圆角
                  padding: '0 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
                }}
              >
                搜索
              </Button>
              <Button
                onClick={handleResetSearch}
                icon={<ReloadOutlined />}
                style={{
                  height: '48px',
                  borderRadius: '24px', // Material 3 圆角
                  padding: '0 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: '1px solid #79747e',
                  backgroundColor: '#ffffff',
                  color: '#6750a4'
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 任务列表 - 表格视图 */}
      <TaskTableView
        tasks={taskListData?.tasks || []}
        requirements={requirementListData?.requirements || []}
        onTaskView={handleViewTask}
        onTaskEdit={handleEditTask}
        onTaskDelete={handleTreeDeleteTask}
        loading={isLoading}
      />

      {/* Material 3 分页控件 */}
      <div style={{
        marginTop: '32px',
        padding: '24px',
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
        textAlign: 'center'
      }}>
        <Pagination
          current={searchParams.page}
          pageSize={searchParams.pageSize}
          total={requirementListData?.total || 0}
          onChange={handlePageChange}
          showSizeChanger={false}
          showQuickJumper
          showTotal={(total, range) =>
            `第 ${range[0]}-${range[1]} 条需求，共 ${total} 条`
          }
          style={{
            // Material 3 分页样式
            fontSize: '14px',
            fontWeight: '500'
          }}
        />
      </div>

      {/* Material 3 创建任务模态框 */}
      <Modal
        title={
          <span style={{
            // Material 3 Headline Small
            fontSize: '24px',
            fontWeight: '400',
            lineHeight: '32px',
            letterSpacing: '0px',
            color: '#1d1b20'
          }}>
            创建任务
          </span>
        }
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={640}
        style={{
          borderRadius: '28px' // Material 3 extra-large 圆角
        }}
        styles={{
          header: {
            padding: '32px 32px 24px 32px',
            borderBottom: '1px solid #e7e0ec'
          },
          body: {
            padding: '24px 32px 32px 32px'
          }
        }}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateTask}
        >
          <Form.Item
            name="title"
            label={
              <span style={{
                // Material 3 Body Large
                fontSize: '16px',
                fontWeight: '400',
                lineHeight: '24px',
                letterSpacing: '0.5px',
                color: '#49454f'
              }}>
                任务标题
              </span>
            }
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input
              placeholder="请输入任务标题"
              style={{
                height: '56px', // Material 3 标准高度
                borderRadius: '12px',
                fontSize: '16px',
                border: '1px solid #79747e',
                backgroundColor: '#ffffff'
              }}
            />
          </Form.Item>

          <Form.Item
            name="requirementId"
            label={
              <span style={{
                // Material 3 Body Large
                fontSize: '16px',
                fontWeight: '400',
                lineHeight: '24px',
                letterSpacing: '0.5px',
                color: '#49454f'
              }}>
                关联需求
              </span>
            }
            rules={[{ required: true, message: '请选择关联需求' }]}
          >
            <Select
              placeholder="选择关联需求"
              onChange={handleRequirementChange}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
              style={{
                height: '56px', // Material 3 标准高度
                borderRadius: '12px',
                fontSize: '16px'
              }}
            >
              {requirementOptions?.map((requirement) => (
                <Option key={requirement.id} value={requirement.id}>
                  {requirement.title} ({requirement.status})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="taskType"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="选择任务类型">
              <Option value="DESIGN">设计</Option>
              <Option value="DEVELOPMENT">开发</Option>
              <Option value="TESTING">测试</Option>
              <Option value="DEPLOYMENT">部署</Option>
              <Option value="DOCUMENTATION">文档</Option>
              <Option value="RESEARCH">调研</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="priorityImportance"
              label="重要程度"
              style={{ flex: 1 }}
              initialValue="HIGH"
            >
              <Select placeholder="选择重要程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="priorityUrgency"
              label="紧急程度"
              style={{ flex: 1 }}
              initialValue="HIGH"
            >
              <Select placeholder="选择紧急程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="dependencies"
            label="上游任务依赖"
            help="选择当前任务依赖的其他任务，被依赖的任务完成后当前任务才能开始"
          >
            <Select
              mode="multiple"
              placeholder="选择上游任务（可多选）"
              disabled={!selectedRequirementId}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {upstreamTaskOptions?.map((task) => (
                <Option key={task.id} value={task.id}>
                  {task.title} ({task.taskType} - {task.status})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="estimatedHours"
              label="预估工时（小时）"
              style={{ flex: 1 }}
            >
              <InputNumber min={0} step={0.5} placeholder="预估工时" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="dueDate"
              label="截止时间"
              style={{ flex: 1 }}
            >
              <DatePicker
                showTime
                placeholder="选择截止时间"
                style={{ width: '100%' }}
                format="YYYY-MM-DD HH:mm"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="assigneeId"
            label="负责人"
          >
            <Select
              placeholder="选择负责人（可选）"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: '32px' }}>
            <Space size={16}>
              <Button
                onClick={() => setIsCreateModalVisible(false)}
                style={{
                  height: '48px',
                  borderRadius: '24px',
                  padding: '0 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: '1px solid #79747e',
                  backgroundColor: '#ffffff',
                  color: '#6750a4'
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createTaskMutation.isPending}
                style={{
                  height: '48px',
                  borderRadius: '24px',
                  padding: '0 24px',
                  fontSize: '14px',
                  fontWeight: '500',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
                }}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑任务模态框 */}
      <TaskEditModal
        visible={isEditModalVisible}
        task={editingTask}
        onClose={() => {
          setIsEditModalVisible(false)
          setEditingTask(null)
        }}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['tasks'] })
          queryClient.invalidateQueries({ queryKey: ['tasks-by-requirements'] })
          queryClient.invalidateQueries({ queryKey: ['kanban'] })
          queryClient.invalidateQueries({ queryKey: ['task-status-statistics'] })
        }}
      />


      {/* 任务详情弹出框 */}
      <TaskDetailModal
        task={viewingTask}
        visible={isDetailModalVisible}
        onClose={handleCloseDetailModal}
      />
    </div>
  )
}

export default TasksPage
