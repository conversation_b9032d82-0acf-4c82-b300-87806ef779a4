import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { authApi } from '../services/api'
import {
  MdFilledButton,
  MdTextButton,
  MdOutlinedTextField,
  MdCircularProgress
} from '../utils/material-imports'

interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  nickname: string
}

const RegisterPage: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<RegisterForm>({
    username: '',
    password: '',
    confirmPassword: '',
    nickname: ''
  })
  const [errors, setErrors] = useState<Partial<RegisterForm>>({})

  const registerMutation = useMutation({
    mutationFn: authApi.register,
    onSuccess: () => {
      navigate('/login')
    },
    onError: (error: any) => {
      console.error('注册失败:', error.response?.data?.message || '注册失败')
    }
  })

  const validateForm = (): boolean => {
    const newErrors: Partial<RegisterForm> = {}

    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名'
    } else if (formData.username.length < 3) {
      newErrors.username = '用户名至少3个字符'
    }

    if (!formData.nickname.trim()) {
      newErrors.nickname = '请输入昵称'
    }

    if (!formData.password) {
      newErrors.password = '请输入密码'
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6个字符'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      registerMutation.mutate({
        username: formData.username,
        password: formData.password,
        nickname: formData.nickname
      })
    }
  }

  const handleInputChange = (field: keyof RegisterForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // 检查表单是否可以提交
  const isFormValid = () => {
    return formData.username.trim().length >= 3 &&
           formData.password.length >= 6 &&
           formData.confirmPassword.length >= 6 &&
           formData.password === formData.confirmPassword
  }

  return (
    <div className="md-surface" style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, var(--md-sys-color-secondary) 0%, var(--md-sys-color-tertiary) 100%)',
      padding: '24px'
    }}>
      {/* 注册卡片 */}
      <div className="md-surface md-elevation-3" style={{
        width: '100%',
        maxWidth: '450px',
        borderRadius: 'var(--md-sys-shape-corner-extra-large)',
        padding: '48px 32px',
        backgroundColor: 'var(--md-sys-color-surface)',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 装饰性背景 */}
        <div style={{
          position: 'absolute',
          top: '-30px',
          left: '-30px',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          backgroundColor: 'var(--md-sys-color-secondary-container)',
          opacity: 0.1
        }} />

        {/* 头部 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            borderRadius: 'var(--md-sys-shape-corner-large)',
            backgroundColor: 'var(--md-sys-color-secondary-container)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            fontSize: '32px'
          }}>
            ✨
          </div>
          <h1 className="md-typescale-display-small" style={{
            margin: '0 0 8px 0',
            color: 'var(--md-sys-color-on-surface)'
          }}>
            创建新账户
          </h1>
          <p className="md-typescale-body-large" style={{
            margin: 0,
            color: 'var(--md-sys-color-on-surface-variant)'
          }}>
            加入猛男项目管理系统
          </p>
        </div>

        {/* 注册表单 */}
        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          <MdOutlinedTextField
            label="用户名"
            required
            value={formData.username}
            onInput={(e: any) => handleInputChange('username', e.target.value)}
            error={!!errors.username}
            errorText={errors.username}
            style={{ width: '100%' }}
            supportingText="英文字母、数字和下划线，至少3个字符"
          />

          <MdOutlinedTextField
            label="昵称"
            value={formData.nickname}
            onInput={(e: any) => handleInputChange('nickname', e.target.value)}
            error={!!errors.nickname}
            errorText={errors.nickname}
            style={{ width: '100%' }}
            supportingText="显示名称，可选填"
          />

          <MdOutlinedTextField
            label="密码"
            type="password"
            required
            value={formData.password}
            onInput={(e: any) => handleInputChange('password', e.target.value)}
            error={!!errors.password}
            errorText={errors.password}
            style={{ width: '100%' }}
            supportingText="至少6个字符"
          />

          <MdOutlinedTextField
            label="确认密码"
            type="password"
            required
            value={formData.confirmPassword}
            onInput={(e: any) => handleInputChange('confirmPassword', e.target.value)}
            error={!!errors.confirmPassword}
            errorText={errors.confirmPassword}
            style={{ width: '100%' }}
            supportingText="请再次输入密码"
          />

          {/* 错误提示 */}
          {registerMutation.isError && (
            <div style={{
              padding: '12px 16px',
              backgroundColor: 'var(--md-sys-color-error-container)',
              color: 'var(--md-sys-color-on-error-container)',
              borderRadius: 'var(--md-sys-shape-corner-medium)',
              fontSize: '14px'
            }}>
              ⚠️ {(registerMutation.error as any)?.response?.data?.message || '注册失败，请检查输入信息'}
            </div>
          )}

          <MdFilledButton
            type="submit"
            disabled={registerMutation.isPending || !isFormValid()}
            style={{
              width: '100%',
              height: '48px',
              marginTop: '8px',
              opacity: (registerMutation.isPending || !isFormValid()) ? 0.6 : 1
            }}
          >
            {registerMutation.isPending ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <MdCircularProgress
                  indeterminate
                  style={{ '--md-circular-progress-size': '20px' } as React.CSSProperties}
                />
                注册中...
              </div>
            ) : (
              <>
                <span slot="icon">🚀</span>
                创建账户
              </>
            )}
          </MdFilledButton>
        </form>

        {/* 底部链接 */}
        <div style={{
          textAlign: 'center',
          marginTop: '32px',
          paddingTop: '24px',
          borderTop: '1px solid var(--md-sys-color-outline-variant)'
        }}>
          <p className="md-typescale-body-medium" style={{
            margin: '0 0 12px 0',
            color: 'var(--md-sys-color-on-surface-variant)'
          }}>
            已有账户？
          </p>
          <Link to="/login" style={{ textDecoration: 'none' }}>
            <MdTextButton>
              <span slot="icon">🔐</span>
              立即登录
            </MdTextButton>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default RegisterPage
