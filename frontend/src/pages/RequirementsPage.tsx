import React, { useState } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Typography,
  Card,
  Row,
  Col,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { requirementApi, userApi, projectApi } from '../services/api'
import { statusApi } from '../services/statusApi'
import { useAuthStore } from '../stores/authStore'
import type {
  Requirement,
  RequirementCreateRequest,
  RequirementStatus,
  Priority,
  User,
} from '../types'
import RequirementEditModal from '../components/RequirementEditModal'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

// 状态颜色映射（用于 Ant Design Tag 组件）
const getStatusTagColor = (color: string): string => {
  // 将十六进制颜色映射到 Ant Design 预定义颜色
  const colorMap: Record<string, string> = {
    '#d9d9d9': 'default',
    '#faad14': 'processing',
    '#52c41a': 'success',
    '#1890ff': 'warning',
    '#722ed1': 'cyan',
    '#13c2c2': 'green',
    '#ff7875': 'error',
    '#ff4d4f': 'error',
  }
  return colorMap[color] || 'default'
}

// 优先级配置
const PRIORITY_CONFIG = {
  HIGH: { label: '高', color: 'red' },
  MEDIUM: { label: '中', color: 'orange' },
  LOW: { label: '低', color: 'green' },
}

const RequirementsPage: React.FC = () => {
  const { user: currentUser } = useAuthStore()
  const [searchForm] = Form.useForm()
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取状态配置
  const { data: statusConfigs = [] } = useQuery({
    queryKey: ['requirement-status-configs'],
    queryFn: statusApi.getRequirementStatusConfigs,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  })

  // 状态管理
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingRequirement, setEditingRequirement] = useState<Requirement | null>(null)
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 20,
    status: undefined as RequirementStatus | undefined,
    assigneeId: undefined as number | undefined,
    creatorId: undefined as number | undefined,
  })

  // 获取需求列表
  const { data: requirementData, isLoading } = useQuery({
    queryKey: ['requirements', searchParams],
    queryFn: () => requirementApi.getRequirementList(searchParams),
  })

  // 获取用户列表（用于分配负责人）
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: userApi.getAllUsers,
  })

  // 获取项目选项列表
  const { data: projectOptions, isLoading: projectOptionsLoading, error: projectOptionsError } = useQuery({
    queryKey: ['project-options'],
    queryFn: projectApi.getProjectOptions,
    retry: 1,
    onError: (error) => {
      console.error('获取项目选项失败:', error)
      message.error('获取项目列表失败，请检查网络连接')
    }
  })

  // 调试信息
  console.log('项目选项数据:', projectOptions)
  console.log('项目选项加载中:', projectOptionsLoading)
  console.log('项目选项错误:', projectOptionsError)

  // 创建需求
  const createMutation = useMutation({
    mutationFn: requirementApi.createRequirement,
    onSuccess: () => {
      message.success('需求创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '创建失败')
    },
  })



  // 删除需求
  const deleteMutation = useMutation({
    mutationFn: requirementApi.deleteRequirement,
    onSuccess: () => {
      message.success('需求删除成功')
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除失败')
    },
  })

  // 处理搜索
  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      ...values,
    })
  }

  // 处理重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields()
    setSearchParams({
      page: 1,
      pageSize: 20,
      status: undefined,
      assigneeId: undefined,
      creatorId: undefined,
    })
  }

  // 处理创建需求
  const handleCreateRequirement = async (values: any) => {
    const data: RequirementCreateRequest = {
      ...values,
      expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
    }
    createMutation.mutate(data)
  }

  // 处理编辑需求
  const handleEditRequirement = (requirement: Requirement) => {
    setEditingRequirement(requirement)
    editForm.setFieldsValue({
      ...requirement,
      projectId: requirement.projectId,
      expectedDeliveryDate: requirement.expectedDeliveryDate
        ? dayjs(requirement.expectedDeliveryDate)
        : undefined,
      actualDeliveryDate: requirement.actualDeliveryDate
        ? dayjs(requirement.actualDeliveryDate)
        : undefined,
    })
    setIsEditModalVisible(true)
  }



  // 处理删除需求
  const handleDeleteRequirement = (id: number) => {
    deleteMutation.mutate(id)
  }

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '所属项目',
      dataIndex: 'project',
      key: 'project',
      width: 150,
      render: (project: any) => (
        <Tooltip title={project?.name || '未分配项目'}>
          <Text ellipsis>{project?.name || '-'}</Text>
        </Tooltip>
      ),
    },
    {
      title: '业务描述',
      dataIndex: 'businessDescription',
      key: 'businessDescription',
      width: 250,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text ellipsis>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: RequirementStatus) => {
        const statusConfig = statusConfigs.find(config => config.key === status)
        if (statusConfig) {
          return (
            <Tag color={getStatusTagColor(statusConfig.color)}>
              {statusConfig.icon} {statusConfig.label}
            </Tag>
          )
        }
        return <Tag color="default">{status}</Tag>
      },
    },
    {
      title: '重要程度',
      dataIndex: 'priorityImportance',
      key: 'priorityImportance',
      width: 100,
      render: (priority: Priority) => {
        const config = PRIORITY_CONFIG[priority]
        return <Tag color={config.color}>{config.label}</Tag>
      },
    },
    {
      title: '紧急程度',
      dataIndex: 'priorityUrgency',
      key: 'priorityUrgency',
      width: 100,
      render: (priority: Priority) => {
        const config = PRIORITY_CONFIG[priority]
        return <Tag color={config.color}>{config.label}</Tag>
      },
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (assignee: User) => assignee?.nickname || assignee?.username || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Requirement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                // TODO: 实现查看详情功能
                message.info('查看详情功能待实现')
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditRequirement(record)}
            />
          </Tooltip>
          <Tooltip title={record.tasks && record.tasks.length > 0 ? "该需求下还有任务，无法删除" : "删除"}>
            {record.tasks && record.tasks.length > 0 ? (
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
                disabled
              />
            ) : (
              <Popconfirm
                title="确定要删除这个需求吗？"
                description="删除后无法恢复，请谨慎操作。"
                onConfirm={() => handleDeleteRequirement(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  size="small"
                  danger
                />
              </Popconfirm>
            )}
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>需求管理</Title>
        <Text type="secondary">管理项目需求，跟踪需求状态和进度</Text>
      </div>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="search" label="需求标题">
            <Input
              placeholder="请输入需求标题进行搜索"
              allowClear
              style={{ width: 250 }}
            />
          </Form.Item>
          <Form.Item name="status" label="状态">
            <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
              {statusConfigs.map(config => (
                <Option key={config.key} value={config.key}>
                  {config.icon} {config.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="assigneeId" label="负责人">
            <Select placeholder="选择负责人" allowClear style={{ width: 150 }}>
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 操作区域 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setIsCreateModalVisible(true)
              // 设置默认状态为第一个激活的状态
              const defaultStatus = statusConfigs.find(config => config.isActive)?.key || 'DRAFT'
              // 设置默认负责人为当前用户
              createForm.setFieldsValue({
                status: defaultStatus,
                assigneeId: currentUser?.id
              })
            }}
          >
            创建需求
          </Button>
        </div>
        <div>
          <Text type="secondary">
            共 {requirementData?.total || 0} 条记录
          </Text>
        </div>
      </div>

      {/* 需求列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={requirementData?.requirements || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: requirementData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({ ...searchParams, page, pageSize: pageSize || 20 })
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建需求模态框 */}
      <Modal
        title="创建需求"
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateRequirement}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="title"
                label="需求标题"
                rules={[{ required: true, message: '请输入需求标题' }]}
              >
                <Input placeholder="请输入需求标题" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="projectId"
                label="所属项目"
                rules={[{ required: true, message: '请选择所属项目' }]}
              >
                <Select
                  placeholder="请选择所属项目"
                  loading={projectOptionsLoading}
                  notFoundContent={projectOptionsLoading ? '加载中...' : '暂无项目'}
                >
                  {projectOptions?.map((project) => (
                    <Option key={project.id} value={project.id}>
                      {project.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="businessDescription"
                label="业务描述"
                rules={[{ required: true, message: '请输入业务描述' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入面向业务人员的需求描述"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="acceptanceCriteria" label="验收标准">
                <TextArea
                  rows={3}
                  placeholder="请输入验收标准"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priorityImportance"
                label="重要程度"
                initialValue="HIGH"
              >
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priorityUrgency"
                label="紧急程度"
                initialValue="HIGH"
              >
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="estimatedValue" label="预估价值">
                <Input placeholder="请输入预估价值或收益" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="targetUsers" label="目标用户">
                <Input placeholder="请输入目标用户群体" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="businessGoal" label="业务目标">
                <TextArea
                  rows={2}
                  placeholder="请输入业务目标"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="assigneeId" label="负责人">
                <Select placeholder="选择负责人" allowClear>
                  {users?.map((user) => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname || user.username}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="expectedDeliveryDate" label="期望交付时间">
                <DatePicker
                  showTime
                  placeholder="选择期望交付时间"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑需求模态框 */}
      <RequirementEditModal
        visible={isEditModalVisible}
        requirement={editingRequirement}
        onClose={() => {
          setIsEditModalVisible(false)
          setEditingRequirement(null)
        }}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['requirements'] })
        }}
      />
    </div>
  )
}

export default RequirementsPage
