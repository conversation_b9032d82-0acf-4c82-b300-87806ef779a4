import React, { useState } from 'react'
import {
  Card,
  Typo<PERSON>,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Space,
  message,
  Popconfirm,
  Tooltip,
  Tag
} from 'antd'
import {
  ProjectOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { projectApi } from '../services/api'
import type { Project, ProjectCreateRequest, ProjectUpdateRequest } from '../types'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input

const ProjectManagePage: React.FC = () => {
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取项目列表
  const { data: projects = [], isLoading } = useQuery({
    queryKey: ['projects'],
    queryFn: projectApi.getAllProjects
  })

  // 创建项目
  const createProjectMutation = useMutation({
    mutationFn: projectApi.createProject,
    onSuccess: () => {
      message.success('项目创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '创建失败')
    }
  })

  // 更新项目
  const updateProjectMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: ProjectUpdateRequest }) =>
      projectApi.updateProject(id, data),
    onSuccess: () => {
      message.success('项目更新成功')
      setIsEditModalVisible(false)
      setSelectedProject(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新失败')
    }
  })

  // 删除项目
  const deleteProjectMutation = useMutation({
    mutationFn: projectApi.deleteProject,
    onSuccess: () => {
      message.success('项目删除成功')
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除失败')
    }
  })

  // 处理创建项目
  const handleCreateProject = async (values: ProjectCreateRequest) => {
    createProjectMutation.mutate(values)
  }

  // 处理编辑项目
  const handleEditProject = (project: Project) => {
    setSelectedProject(project)
    editForm.setFieldsValue({
      name: project.name,
      description: project.description
    })
    setIsEditModalVisible(true)
  }

  // 处理更新项目
  const handleUpdateProject = async (values: ProjectUpdateRequest) => {
    if (!selectedProject) return
    updateProjectMutation.mutate({
      id: selectedProject.id,
      data: values
    })
  }

  // 处理删除项目
  const handleDeleteProject = async (project: Project) => {
    const requirementCount = project.requirementCount || 0
    if (requirementCount > 0) {
      message.warning('该项目下还有关联的需求，无法删除')
      return
    }
    deleteProjectMutation.mutate(project.id)
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <ProjectOutlined style={{ marginRight: '8px' }} />
          项目管理
        </Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setIsCreateModalVisible(true)}
        >
          创建项目
        </Button>
      </div>

      {/* 项目列表 */}
      <Card>
        <Table
          dataSource={projects}
          loading={isLoading}
          rowKey="id"
          columns={[
            {
              title: '项目名称',
              dataIndex: 'name',
              key: 'name',
              render: (text: string) => (
                <span style={{ fontWeight: 'bold' }}>{text}</span>
              )
            },
            {
              title: '项目描述',
              dataIndex: 'description',
              key: 'description',
              render: (text: string) => text || '-'
            },
            {
              title: '需求数量',
              dataIndex: 'requirementCount',
              key: 'requirementCount',
              render: (count: number) => {
                const requirementCount = count || 0
                return (
                  <Tag color={requirementCount > 0 ? 'blue' : 'default'}>
                    {requirementCount} 个需求
                  </Tag>
                )
              }
            },
            {
              title: '创建者',
              key: 'creator',
              render: (_, record: Project) => (
                record.creator?.nickname || record.creator?.username || '-'
              )
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
            },
            {
              title: '操作',
              key: 'actions',
              render: (_, record: Project) => (
                <Space>
                  <Tooltip title="编辑">
                    <Button 
                      type="text" 
                      icon={<EditOutlined />} 
                      size="small"
                      onClick={() => handleEditProject(record)}
                    />
                  </Tooltip>
                  <Popconfirm
                    title={(record.requirementCount || 0) > 0 ? "该项目下还有关联的需求，无法删除" : "确定删除这个项目吗？"}
                    onConfirm={() => handleDeleteProject(record)}
                    okText="确定"
                    cancelText="取消"
                    disabled={(record.requirementCount || 0) > 0}
                  >
                    <Tooltip title={(record.requirementCount || 0) > 0 ? "该项目下还有需求，无法删除" : "删除"}>
                      <Button
                        type="text"
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        disabled={(record.requirementCount || 0) > 0}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              )
            }
          ]}
          pagination={false} // 不分页，全部展示
        />
      </Card>

      {/* 创建项目模态框 */}
      <Modal
        title="创建项目"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false)
          createForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateProject}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="项目描述"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入项目描述"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false)
                createForm.resetFields()
              }}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createProjectMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑项目模态框 */}
      <Modal
        title="编辑项目"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false)
          setSelectedProject(null)
          editForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateProject}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="项目描述"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入项目描述"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditModalVisible(false)
                setSelectedProject(null)
                editForm.resetFields()
              }}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={updateProjectMutation.isPending}
              >
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProjectManagePage
