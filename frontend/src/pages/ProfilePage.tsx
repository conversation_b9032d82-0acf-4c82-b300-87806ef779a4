import React, { useState } from 'react'
import {
  Typography,
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Space,
  message,
  Divider,
  Row,
  Col,
  Upload,
  Modal
} from 'antd'
import {
  UserOutlined,
  EditOutlined,
  KeyOutlined,
  CameraOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userApi } from '../services/api'
import { useAuthStore } from '../stores/authStore'
import type { UserUpdateRequest, PasswordChangeRequest } from '../types'
import { getFullAvatarUrl } from '../utils/url'
import dayjs from 'dayjs'

const { Title, Text } = Typography

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuthStore()
  const [isEditingProfile, setIsEditingProfile] = useState(false)
  const [isChangePasswordVisible, setIsChangePasswordVisible] = useState(false)
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取当前用户信息
  const { data: currentUser, isLoading } = useQuery({
    queryKey: ['currentUser'],
    queryFn: userApi.getCurrentUser,
    initialData: user,
  })

  // 更新个人信息
  const updateProfileMutation = useMutation({
    mutationFn: userApi.updateProfile,
    onSuccess: (updatedUser) => {
      message.success('个人信息更新成功')
      updateUser(updatedUser)
      setIsEditingProfile(false)
      queryClient.invalidateQueries({ queryKey: ['currentUser'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新个人信息失败')
    },
  })

  // 修改密码
  const changePasswordMutation = useMutation({
    mutationFn: userApi.changePassword,
    onSuccess: () => {
      message.success('密码修改成功')
      setIsChangePasswordVisible(false)
      passwordForm.resetFields()
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '密码修改失败')
    },
  })

  // 上传头像
  const uploadAvatarMutation = useMutation({
    mutationFn: userApi.uploadAvatar,
    onSuccess: (data) => {
      message.success('头像上传成功')
      // 强制更新用户信息，包括新的头像URL
      updateUser({ ...currentUser!, avatarUrl: data.avatarUrl })
      // 刷新用户数据
      queryClient.invalidateQueries({ queryKey: ['currentUser'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '头像上传失败')
    },
  })

  const handleUpdateProfile = (values: UserUpdateRequest) => {
    updateProfileMutation.mutate(values)
  }

  const handleChangePassword = (values: any) => {
    // 过滤掉confirmPassword字段，只发送oldPassword和newPassword
    const { confirmPassword, ...passwordData } = values
    changePasswordMutation.mutate(passwordData as PasswordChangeRequest)
  }

  const handleEditProfile = () => {
    setIsEditingProfile(true)
    profileForm.setFieldsValue({
      nickname: currentUser?.nickname,
    })
  }

  const handleCancelEdit = () => {
    setIsEditingProfile(false)
    profileForm.resetFields()
  }

  const handleAvatarUpload = (file: File) => {
    // 验证文件类型（只允许指定格式）
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      message.error('只支持 JPG、PNG、WEBP 格式的图片！')
      return false
    }

    // 验证文件扩展名
    const fileName = file.name.toLowerCase()
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp']
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))

    if (!hasValidExtension) {
      message.error('只支持 JPG、PNG、WEBP 格式的图片！')
      return false
    }

    // 验证文件大小（限制为2MB）
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      message.error('图片大小不能超过2MB！')
      return false
    }

    uploadAvatarMutation.mutate(file)
    return false // 阻止默认上传行为
  }

  if (isLoading) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <div>加载中...</div>
      </div>
    )
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>个人设置</Title>
        <Text type="secondary">
          管理您的个人信息和账户设置
        </Text>
      </div>

      <Row gutter={24}>
        {/* 个人信息卡片 */}
        <Col xs={24} lg={16}>
          <Card
            title="个人信息"
            extra={
              !isEditingProfile && (
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={handleEditProfile}
                >
                  编辑
                </Button>
              )
            }
          >
            {!isEditingProfile ? (
              // 显示模式
              <div>
                <Space size="large" align="start">
                  <Avatar
                    size={80}
                    src={getFullAvatarUrl(currentUser?.avatarUrl)}
                    icon={<UserOutlined />}
                  />
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong style={{ fontSize: 18 }}>
                        {currentUser?.nickname || currentUser?.username}
                      </Text>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <Text type="secondary">用户名：</Text>
                      <Text>{currentUser?.username}</Text>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <Text type="secondary">用户类型：</Text>
                      <Text>
                        {currentUser?.userType === 'SUPER_ADMIN' ? '超级管理员' : '普通用户'}
                      </Text>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <Text type="secondary">注册时间：</Text>
                      <Text>
                        {currentUser?.createdAt ?
                          dayjs(currentUser.createdAt).format('YYYY-MM-DD HH:mm') :
                          '-'
                        }
                      </Text>
                    </div>
                  </div>
                </Space>
              </div>
            ) : (
              // 编辑模式
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleUpdateProfile}
              >
                <Row gutter={16}>
                  <Col xs={24} sm={8}>
                    <div style={{ textAlign: 'center', marginBottom: 16 }}>
                      <Avatar
                        size={80}
                        src={getFullAvatarUrl(currentUser?.avatarUrl)}
                        icon={<UserOutlined />}
                      />
                      <div style={{ marginTop: 8 }}>
                        <Upload
                          showUploadList={false}
                          beforeUpload={handleAvatarUpload}
                          accept=".jpg,.jpeg,.png,.webp"
                        >
                          <Button
                            size="small"
                            icon={<CameraOutlined />}
                            loading={uploadAvatarMutation.isPending}
                          >
                            更换头像
                          </Button>
                        </Upload>
                      </div>
                    </div>
                  </Col>
                  <Col xs={24} sm={16}>
                    <Form.Item
                      name="nickname"
                      label="昵称"
                      rules={[
                        { max: 50, message: '昵称最多50个字符' }
                      ]}
                    >
                      <Input placeholder="请输入昵称" />
                    </Form.Item>



                    <Form.Item style={{ marginBottom: 0 }}>
                      <Space>
                        <Button
                          type="primary"
                          htmlType="submit"
                          icon={<SaveOutlined />}
                          loading={updateProfileMutation.isPending}
                        >
                          保存
                        </Button>
                        <Button onClick={handleCancelEdit}>
                          取消
                        </Button>
                      </Space>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            )}
          </Card>
        </Col>

        {/* 账户安全卡片 */}
        <Col xs={24} lg={8}>
          <Card title="账户安全">
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Text strong>密码</Text>
              </div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                定期更换密码可以提高账户安全性
              </Text>
            </div>

            <Button
              type="primary"
              icon={<KeyOutlined />}
              onClick={() => setIsChangePasswordVisible(true)}
              block
            >
              修改密码
            </Button>

            <Divider />

            <div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>账户状态</Text>
              </div>
              <div style={{ marginBottom: 4 }}>
                <Text type="secondary">状态：</Text>
                <Text style={{ color: '#52c41a' }}>正常</Text>
              </div>
              <div style={{ marginBottom: 4 }}>
                <Text type="secondary">登录失败次数：</Text>
                <Text>{currentUser?.failedLoginCount || 0} 次</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={isChangePasswordVisible}
        onCancel={() => {
          setIsChangePasswordVisible(false)
          passwordForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="oldPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsChangePasswordVisible(false)
                passwordForm.resetFields()
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={changePasswordMutation.isPending}
              >
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProfilePage
