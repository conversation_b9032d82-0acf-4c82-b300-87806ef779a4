<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF导出测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body { 
            font-family: 'Microsoft YaHei', 'SimSun', sans-serif; 
            margin: 20px;
            line-height: 1.6;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #1890ff;
            padding-bottom: 15px;
        }
        .title { 
            font-size: 24px; 
            font-weight: bold; 
            color: #1890ff;
            margin-bottom: 10px;
        }
        .summary { 
            background: #f5f5f5; 
            padding: 15px; 
            border-radius: 5px; 
            margin-bottom: 20px;
        }
        .project-group { 
            margin-bottom: 25px; 
            border: 1px solid #e8e8e8;
            border-radius: 5px;
            overflow: hidden;
        }
        .project-title { 
            font-size: 16px; 
            font-weight: bold; 
            background: #1890ff; 
            color: white; 
            padding: 10px 15px;
            margin: 0;
        }
        .requirement { 
            margin: 15px; 
            padding: 15px;
            border-left: 4px solid #1890ff;
            background: #fafafa;
        }
        .status-tag { 
            display: inline-block; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-size: 10px; 
            color: white;
            margin-left: 8px;
        }
        .export-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px;
        }
        .export-btn:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>PDF导出功能测试</h1>
        <button class="export-btn" onclick="exportToPDF()">导出PDF</button>
        <button class="export-btn" onclick="testChineseSupport()">测试中文支持</button>
    </div>

    <div id="test-content" class="test-content">
        <div class="header">
            <div class="title">周报测试 (2024-01-01 ~ 2024-01-07)</div>
        </div>
        
        <div class="summary">
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #1890ff;">📊 统计摘要</div>
            <div>需求总数: 5</div>
            <div>任务总数: 12</div>
            <div>活跃项目: 3</div>
            <div>已完成需求: 2</div>
            <div>已完成任务: 8</div>
        </div>

        <div class="project-group">
            <h3 class="project-title">🏢 项目管理系统 (2)</h3>
            <div class="requirement">
                <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">
                    1. 用户权限管理功能
                    <span class="status-tag" style="background-color: #52c41a">已完成</span>
                </div>
                <div style="color: #666; margin-bottom: 8px;">实现用户角色权限管理，包括超级管理员和普通用户的权限控制</div>
                <div style="color: #888; font-size: 11px; margin-bottom: 10px;">负责人: 张三</div>
                <div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #e8e8e8;">
                    <div style="font-weight: bold; color: #666; margin-bottom: 5px;">关联任务 (3):</div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        1) 设计权限数据模型
                        <span class="status-tag" style="background-color: #52c41a">已完成</span>
                    </div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        2) 实现权限验证中间件
                        <span class="status-tag" style="background-color: #52c41a">已完成</span>
                    </div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        3) 前端权限控制组件
                        <span class="status-tag" style="background-color: #1890ff">进行中</span>
                    </div>
                </div>
            </div>
            
            <div class="requirement">
                <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">
                    2. 周报功能开发
                    <span class="status-tag" style="background-color: #1890ff">进行中</span>
                </div>
                <div style="color: #666; margin-bottom: 8px;">开发周报生成和导出功能，支持PDF导出</div>
                <div style="color: #888; font-size: 11px; margin-bottom: 10px;">负责人: 李四</div>
                <div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #e8e8e8;">
                    <div style="font-weight: bold; color: #666; margin-bottom: 5px;">关联任务 (2):</div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        1) 后端API开发
                        <span class="status-tag" style="background-color: #52c41a">已完成</span>
                    </div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        2) PDF导出功能
                        <span class="status-tag" style="background-color: #722ed1">测试中</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="project-group">
            <h3 class="project-title">🏢 移动应用 (1)</h3>
            <div class="requirement">
                <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">
                    1. 用户登录功能
                    <span class="status-tag" style="background-color: #faad14">评审中</span>
                </div>
                <div style="color: #666; margin-bottom: 8px;">实现移动端用户登录和身份验证功能</div>
                <div style="color: #888; font-size: 11px; margin-bottom: 10px;">负责人: 王五</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #e8e8e8; color: #888; font-size: 10px;">
            生成时间: 2024-01-08 10:30:00
        </div>
    </div>

    <script>
        async function exportToPDF() {
            try {
                const element = document.getElementById('test-content');
                
                // 使用html2canvas截图
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                });

                const imgData = canvas.toDataURL('image/png');
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                
                // 计算缩放比例
                const ratio = pdfWidth / imgWidth;
                const scaledHeight = imgHeight * ratio;
                
                let position = 0;
                
                // 分页处理
                while (position < scaledHeight) {
                    if (position > 0) {
                        pdf.addPage();
                    }
                    
                    pdf.addImage(
                        imgData, 
                        'PNG', 
                        0, 
                        -position, 
                        pdfWidth, 
                        scaledHeight
                    );
                    
                    position += pdfHeight;
                }

                pdf.save('周报测试.pdf');
                alert('PDF导出成功！');
            } catch (error) {
                console.error('PDF导出失败:', error);
                alert('PDF导出失败: ' + error.message);
            }
        }

        function testChineseSupport() {
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <div style="font-family: 'Microsoft YaHei', 'SimSun', sans-serif; padding: 20px;">
                    <h2>中文字体测试</h2>
                    <p>这是一段中文测试文本，包含各种字符：</p>
                    <ul>
                        <li>简体中文：项目管理系统</li>
                        <li>繁体中文：項目管理系統</li>
                        <li>特殊符号：📊 🏢 ✅ 🔄</li>
                        <li>数字和英文：Project Management System 2024</li>
                    </ul>
                </div>
            `;
            testDiv.style.position = 'absolute';
            testDiv.style.left = '-9999px';
            document.body.appendChild(testDiv);

            html2canvas(testDiv).then(canvas => {
                document.body.removeChild(testDiv);
                
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF();
                const imgData = canvas.toDataURL('image/png');
                
                pdf.addImage(imgData, 'PNG', 10, 10, 190, 0);
                pdf.save('中文字体测试.pdf');
                alert('中文字体测试PDF已生成！');
            }).catch(error => {
                document.body.removeChild(testDiv);
                alert('测试失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
