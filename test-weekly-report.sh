#!/bin/bash

# 测试周报功能的脚本

set -e

echo "🧪 测试周报功能..."
echo ""

# 配置变量
SERVER_HOST="coffee@**************"
API_BASE="http://coffee.local:8080"
FRONTEND_BASE="http://coffee.local:3000"

echo "📋 测试项目："
echo "1. 🔍 API健康检查"
echo "2. 🗄️  数据库状态检查"
echo "3. 🌐 前端页面访问"
echo "4. 📊 周报API测试"
echo ""

echo "🔍 第一步：API健康检查..."
if curl -s "$API_BASE/health" > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
    exit 1
fi

echo ""
echo "🗄️  第二步：数据库状态检查..."

ssh $SERVER_HOST << 'EOF'
mysql -hlocalhost -uroot -pxjy123456 coconut << 'SQL'
-- 检查归档状态是否存在
SELECT '=== 归档状态检查 ===' as info;
SELECT `key`, label, color, icon 
FROM requirement_status_configs 
WHERE `key` = 'ARCHIVED';

-- 检查状态转换配置
SELECT '=== 状态转换配置 ===' as info;
SELECT `key`, label, allowed_transitions 
FROM requirement_status_configs 
WHERE `key` IN ('DELIVERED', 'REJECTED');

-- 检查需求数据
SELECT '=== 需求数据统计 ===' as info;
SELECT status, COUNT(*) as count 
FROM requirements 
GROUP BY status;
SQL
EOF

echo ""
echo "🌐 第三步：前端页面访问测试..."

echo "测试主页访问..."
if curl -s "$FRONTEND_BASE" > /dev/null; then
    echo "✅ 前端主页可访问"
else
    echo "❌ 前端主页访问失败"
fi

echo "测试周报页面路由..."
if curl -s "$FRONTEND_BASE/weekly-report" > /dev/null; then
    echo "✅ 周报页面路由正常"
else
    echo "⚠️  周报页面需要登录（正常）"
fi

echo ""
echo "📊 第四步：周报API测试..."

echo "测试周报API（无认证）..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/api/weekly-report")

if [ "$HTTP_STATUS" = "401" ]; then
    echo "✅ 周报API需要认证（正常）"
elif [ "$HTTP_STATUS" = "200" ]; then
    echo "⚠️  周报API无需认证（可能的安全问题）"
else
    echo "❌ 周报API异常 (HTTP $HTTP_STATUS)"
fi

echo ""
echo "🔧 第五步：服务状态检查..."

ssh $SERVER_HOST << 'EOF'
echo "Java进程状态:"
ps aux | grep java | grep coconut | grep -v grep || echo "Java进程未运行"

echo ""
echo "端口监听状态:"
ss -tlnp | grep 8080 || echo "8080端口未监听"

echo ""
echo "最近日志:"
tail -10 ~/coconut/logs/app.log 2>/dev/null || echo "日志文件不存在"
EOF

echo ""
echo "🎉 测试完成！"
echo ""
echo "📝 测试结果总结："
echo ""
echo "✅ 已验证项目："
echo "  - API服务健康状态"
echo "  - 数据库归档状态配置"
echo "  - 前端页面路由"
echo "  - 周报API认证"
echo "  - 服务运行状态"
echo ""
echo "🌐 访问地址："
echo "  - 周报页面: $FRONTEND_BASE/weekly-report"
echo "  - 周报API: $API_BASE/api/weekly-report"
echo ""
echo "📋 功能特点："
echo "  - 🏢 需求按项目归类显示"
echo "  - 🏷️  任务状态中文标签和颜色"
echo "  - 📤 支持导出文本文件"
echo "  - 🔍 多维度筛选功能"
echo "  - 📊 统计摘要展示"
echo ""
echo "🔧 如果发现问题："
echo "1. 检查服务日志: ssh $SERVER_HOST 'tail -f ~/coconut/logs/app.log'"
echo "2. 重启服务: ssh $SERVER_HOST 'cd ~/coconut && pkill -f java && nohup java -jar coconut.jar > logs/app.log 2>&1 &'"
echo "3. 检查数据库: 登录数据库查看相关表数据"
echo ""
