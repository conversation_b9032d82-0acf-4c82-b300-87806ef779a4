#!/bin/bash

# 部署Material 3完整版本

set -e

echo "🚀 部署Material 3完整版本..."
echo ""

# 配置变量
SERVER_HOST="coffee@**************"
COCONUT_DIR="/home/<USER>/coconut"

echo "📋 功能说明："
echo "1. ✅ 引入Material Web官方库 (@material/web)"
echo "2. ✅ 创建Material 3主题配置"
echo "3. ✅ 实现Material 3任务管理页面"
echo "4. ✅ 使用原生Material 3 Web Components"
echo "5. ✅ 应用Material 3设计系统"
echo ""

echo "📤 部署到服务器..."

# 部署前端
echo "部署前端..."
ssh $SERVER_HOST "rm -rf $COCONUT_DIR/static/*"
scp -r frontend/dist/* $SERVER_HOST:$COCONUT_DIR/static/

echo ""
echo "🧪 测试功能..."

sleep 3

echo "测试API健康检查..."
if curl -s http://coffee.local:8080/health > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
    exit 1
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📝 Material 3实现详情："
echo ""
echo "🔹 Material Web库集成:"
echo "  - 包名: @material/web@2.3.0"
echo "  - 组件导入: 集中在 src/utils/material-imports.ts"
echo "  - 字体样式: 自动应用Material 3字体系统"
echo "  - 主题配置: src/styles/material-theme.css"
echo ""
echo "🔹 已实现的Material 3组件:"
echo "  - MdFilledButton (填充按钮)"
echo "  - MdOutlinedButton (轮廓按钮)"
echo "  - MdTextButton (文本按钮)"
echo "  - MdElevatedButton (浮起按钮)"
echo "  - MdFilledTonalButton (填充色调按钮)"
echo "  - MdOutlinedTextField (轮廓文本字段)"
echo "  - MdFilledTextField (填充文本字段)"
echo "  - MdOutlinedSelect (轮廓选择器)"
echo "  - MdSelectOption (选择选项)"
echo "  - MdDialog (对话框)"
echo "  - MdCircularProgress (圆形进度条)"
echo "  - MdCheckbox (复选框)"
echo "  - MdRadio (单选框)"
echo "  - MdIconButton (图标按钮)"
echo ""
echo "🔹 Material 3设计令牌:"
echo "  - Primary: #6750a4"
echo "  - Primary Container: #eaddff"
echo "  - Secondary: #625b71"
echo "  - Secondary Container: #e8def8"
echo "  - Surface: #fef7ff"
echo "  - Surface Container: #f3edf7"
echo "  - On Surface: #1d1b20"
echo "  - On Surface Variant: #49454f"
echo "  - Outline: #79747e"
echo "  - Error: #ba1a1a"
echo ""
echo "🔹 字体系统 (Roboto):"
echo "  - Display Large: 57px/64px, 400"
echo "  - Display Medium: 45px/52px, 400"
echo "  - Display Small: 36px/44px, 400"
echo "  - Headline Large: 32px/40px, 400"
echo "  - Headline Medium: 28px/36px, 400"
echo "  - Headline Small: 24px/32px, 400"
echo "  - Title Large: 22px/28px, 400"
echo "  - Title Medium: 16px/24px, 500"
echo "  - Title Small: 14px/20px, 500"
echo "  - Body Large: 16px/24px, 400"
echo "  - Body Medium: 14px/20px, 400"
echo "  - Body Small: 12px/16px, 400"
echo "  - Label Large: 14px/20px, 500"
echo "  - Label Medium: 12px/16px, 500"
echo "  - Label Small: 11px/16px, 500"
echo ""
echo "🔹 形状系统:"
echo "  - Extra Small: 4px"
echo "  - Small: 8px"
echo "  - Medium: 12px"
echo "  - Large: 16px"
echo "  - Extra Large: 28px"
echo "  - Full: 50px"
echo ""
echo "🔹 阴影系统:"
echo "  - Elevation 0: 无阴影"
echo "  - Elevation 1: 0px 1px 3px rgba(0,0,0,0.12), 0px 1px 2px rgba(0,0,0,0.24)"
echo "  - Elevation 2: 0px 3px 6px rgba(0,0,0,0.16), 0px 3px 6px rgba(0,0,0,0.23)"
echo "  - Elevation 3: 0px 10px 20px rgba(0,0,0,0.19), 0px 6px 6px rgba(0,0,0,0.23)"
echo ""
echo "🔹 Material 3任务页面特性:"
echo "  - 路径: /material3-tasks"
echo "  - 原生Web Components"
echo "  - Material 3设计语言"
echo "  - 响应式布局"
echo "  - 无障碍支持"
echo "  - 现代化交互"
echo ""
echo "🔹 页面结构:"
echo "  1. 页面标题区域 (Display Small + Primary Container)"
echo "  2. 搜索筛选区域 (Outlined TextField + Buttons)"
echo "  3. 任务列表区域 (Surface Container + Cards)"
echo "  4. 分页控件 (Outlined Buttons)"
echo "  5. 创建任务对话框 (Dialog + Form Fields)"
echo ""
echo "🔹 技术实现:"
echo "  - React + TypeScript"
echo "  - Material Web Components"
echo "  - CSS Custom Properties"
echo "  - Web Components API"
echo "  - 现代浏览器支持"
echo ""
echo "🌐 访问地址："
echo "  - 前端: http://coffee.local:3000"
echo "  - Material 3任务: http://coffee.local:3000/material3-tasks"
echo "  - 原版任务: http://coffee.local:3000/tasks"
echo ""
echo "🔧 验证步骤："
echo "1. 访问Material 3任务页面"
echo "2. 查看Material 3设计风格"
echo "3. 测试按钮交互效果"
echo "4. 测试文本字段输入"
echo "5. 测试选择器功能"
echo "6. 测试对话框弹出"
echo "7. 测试表单提交"
echo "8. 验证响应式布局"
echo "9. 检查无障碍功能"
echo "10. 对比原版页面差异"
echo ""
echo "🔧 Material 3 vs Ant Design对比:"
echo ""
echo "📊 设计系统:"
echo "  Material 3: Google官方设计系统，现代化"
echo "  Ant Design: 企业级设计语言，成熟稳定"
echo ""
echo "🎨 视觉风格:"
echo "  Material 3: 圆润、柔和、个性化"
echo "  Ant Design: 简洁、专业、一致性"
echo ""
echo "🧩 组件生态:"
echo "  Material 3: 原生Web Components，标准化"
echo "  Ant Design: React组件库，功能丰富"
echo ""
echo "📱 响应式:"
echo "  Material 3: 原生响应式设计"
echo "  Ant Design: 栅格系统响应式"
echo ""
echo "♿ 无障碍:"
echo "  Material 3: 内置ARIA支持"
echo "  Ant Design: 良好的无障碍支持"
echo ""
echo "🔧 定制性:"
echo "  Material 3: CSS Custom Properties"
echo "  Ant Design: Less变量 + CSS-in-JS"
echo ""
echo "📦 包大小:"
echo "  Material 3: 按需加载，较小"
echo "  Ant Design: 功能完整，较大"
echo ""
echo "🔧 如果遇到问题："
echo "1. 检查浏览器是否支持Web Components"
echo "2. 确认Material Web包版本正确"
echo "3. 检查CSS Custom Properties支持"
echo "4. 验证JavaScript模块加载"
echo "5. 查看浏览器控制台错误"
echo "6. 重新运行此脚本"
echo ""
echo "💡 下一步计划："
echo "1. 逐步替换更多页面为Material 3"
echo "2. 创建Material 3组件库"
echo "3. 优化主题定制功能"
echo "4. 增加更多Material 3组件"
echo "5. 完善无障碍功能"
echo ""
