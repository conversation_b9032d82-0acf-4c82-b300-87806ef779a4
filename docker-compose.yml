version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: beefcake-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: beefcake
      MYSQL_USER: beefcake_user
      MYSQL_PASSWORD: beefcake_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - beefcake-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: beefcake-backend
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: beefcake
      DB_USER: beefcake_user
      DB_PASSWORD: beefcake_pass
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      APP_ENV: production
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    volumes:
      - ./uploads:/app/uploads
    networks:
      - beefcake-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: beefcake-frontend
    environment:
      VITE_API_BASE_URL: http://coffee.local:8080
    ports:
      - "80:80"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - beefcake-network

volumes:
  mysql_data:

networks:
  beefcake-network:
    driver: bridge
