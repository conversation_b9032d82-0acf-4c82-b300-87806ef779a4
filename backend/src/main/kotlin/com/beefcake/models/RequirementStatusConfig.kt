package com.beefcake.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

// 需求状态配置表
object RequirementStatusConfigs : IntIdTable("requirement_status_configs") {
    val key = varchar("key", 50).uniqueIndex()
    val label = varchar("label", 100)
    val color = varchar("color", 20)
    val icon = varchar("icon", 10)
    val description = text("description")
    val order = integer("order")
    val isActive = bool("is_active").default(true)
    val allowedTransitions = text("allowed_transitions") // JSON 字符串
    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at").default(LocalDateTime.now())
    val createdBy = integer("created_by").nullable()
    val updatedBy = integer("updated_by").nullable()
}

// 需求状态配置实体
class RequirementStatusConfigEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<RequirementStatusConfigEntity>(RequirementStatusConfigs)

    var key by RequirementStatusConfigs.key
    var label by RequirementStatusConfigs.label
    var color by RequirementStatusConfigs.color
    var icon by RequirementStatusConfigs.icon
    var description by RequirementStatusConfigs.description
    var order by RequirementStatusConfigs.order
    var isActive by RequirementStatusConfigs.isActive
    var allowedTransitions by RequirementStatusConfigs.allowedTransitions
    var createdAt by RequirementStatusConfigs.createdAt
    var updatedAt by RequirementStatusConfigs.updatedAt
    var createdBy by RequirementStatusConfigs.createdBy
    var updatedBy by RequirementStatusConfigs.updatedBy

    fun toRequirementStatusConfig(): RequirementStatusConfig {
        return RequirementStatusConfig(
            key = key,
            label = label,
            color = color,
            icon = icon,
            description = description,
            order = order,
            isActive = isActive,
            allowedTransitions = parseAllowedTransitions(allowedTransitions),
            createdAt = createdAt.toString(),
            updatedAt = updatedAt.toString(),
            createdBy = null, // 暂时设为 null，后续可以根据 ID 查询用户信息
            updatedBy = null
        )
    }

    private fun parseAllowedTransitions(json: String): List<String> {
        return try {
            kotlinx.serialization.json.Json.decodeFromString<List<String>>(json)
        } catch (e: Exception) {
            emptyList()
        }
    }
}

// 需求状态配置数据类
@Serializable
data class RequirementStatusConfig(
    val key: String,
    val label: String,
    val color: String,
    val icon: String,
    val description: String,
    val order: Int,
    val isActive: Boolean,
    val allowedTransitions: List<String>,
    val createdAt: String? = null,
    val updatedAt: String? = null,
    val createdBy: User? = null,
    val updatedBy: User? = null
)

// 创建状态配置请求
@Serializable
data class CreateRequirementStatusConfigRequest(
    val key: String,
    val label: String,
    val color: String,
    val icon: String,
    val description: String,
    val order: Int,
    val isActive: Boolean = true,
    val allowedTransitions: List<String> = emptyList()
)

// 更新状态配置请求
@Serializable
data class UpdateRequirementStatusConfigRequest(
    val label: String? = null,
    val color: String? = null,
    val icon: String? = null,
    val description: String? = null,
    val order: Int? = null,
    val isActive: Boolean? = null,
    val allowedTransitions: List<String>? = null
)

// 批量更新状态配置请求
@Serializable
data class BatchUpdateRequirementStatusConfigsRequest(
    val configs: List<RequirementStatusConfig>
)

// 状态转换验证请求
@Serializable
data class StatusTransitionValidationRequest(
    val fromStatus: String,
    val toStatus: String
)

// 状态转换验证响应
@Serializable
data class StatusTransitionValidationResponse(
    val isAllowed: Boolean,
    val reason: String? = null
)

// 状态统计数据
@Serializable
data class StatusStatistics(
    val statusKey: String,
    val statusLabel: String,
    val count: Int,
    val percentage: Double
)
