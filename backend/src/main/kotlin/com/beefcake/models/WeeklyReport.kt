package com.beefcake.models

import kotlinx.serialization.Serializable

// 周报数据模型
@Serializable
data class WeeklyReport(
    val requirements: List<WeeklyRequirementItem>,
    val summary: WeeklyReportSummary,
    val generatedAt: String,
    val weekRange: String // 例如: "2024-01-01 ~ 2024-01-07"
)

// 周报中的需求项
@Serializable
data class WeeklyRequirementItem(
    val id: Long,
    val title: String,
    val description: String,
    val status: String,
    val statusLabel: String,
    val statusColor: String,
    val project: ProjectInfo,
    val assignee: User?,
    val tasks: List<WeeklyTaskItem>,
    val createdAt: String,
    val updatedAt: String
)

// 周报中的任务项
@Serializable
data class WeeklyTaskItem(
    val id: Long,
    val title: String,
    val status: String,
    val statusLabel: String,
    val statusColor: String,
    val assignee: User?,
    val estimatedHours: Double?,
    val actualHours: Double?,
    val dueDate: String?
)

// 项目信息（简化版）
@Serializable
data class ProjectInfo(
    val id: Long,
    val name: String,
    val description: String?
)

// 周报摘要
@Serializable
data class WeeklyReportSummary(
    val totalRequirements: Int,
    val requirementsByStatus: Map<String, Int>,
    val totalTasks: Int,
    val tasksByStatus: Map<String, Int>,
    val activeProjects: Int,
    val completedRequirements: Int,
    val completedTasks: Int
)

// 周报查询请求
@Serializable
data class WeeklyReportRequest(
    val startDate: String? = null, // 可选，默认为本周
    val endDate: String? = null,   // 可选，默认为本周
    val projectIds: List<Long>? = null, // 可选，筛选特定项目
    val assigneeIds: List<Long>? = null, // 可选，筛选特定负责人
    val includeArchived: Boolean = false // 是否包含已归档的需求
)
