package com.beefcake.models

import com.beefcake.database.tables.Priority
import kotlinx.serialization.Serializable

@Serializable
data class Requirement(
    val id: Long,
    val title: String,
    val businessDescription: String, // 业务描述，面向老板
    val acceptanceCriteria: String?, // 验收标准
    val projectId: Long, // 关联项目
    val status: String, // 动态状态，对应状态配置的 key
    val priorityImportance: Priority,
    val priorityUrgency: Priority,
    val estimatedValue: String?, // 预估价值/收益
    val targetUsers: String?, // 目标用户群体
    val businessGoal: String?, // 业务目标
    val creatorId: Long,
    val assigneeId: Long?, // 需求负责人
    val expectedDeliveryDate: String?, // 期望交付时间
    val actualDeliveryDate: String?, // 实际交付时间
    val createdAt: String,
    val updatedAt: String,
    val creator: User?,
    val assignee: User?,
    val project: Project?, // 关联的项目信息
    val tasks: List<Task>? // 关联的任务列表
)

@Serializable
data class RequirementCreateRequest(
    val title: String,
    val businessDescription: String,
    val acceptanceCriteria: String? = null,
    val projectId: Long, // 必须选择项目
    val priorityImportance: Priority = Priority.HIGH,
    val priorityUrgency: Priority = Priority.HIGH,
    val estimatedValue: String? = null,
    val targetUsers: String? = null,
    val businessGoal: String? = null,
    val assigneeId: Long? = null,
    val expectedDeliveryDate: String? = null
)

@Serializable
data class RequirementUpdateRequest(
    val title: String? = null,
    val businessDescription: String? = null,
    val acceptanceCriteria: String? = null,
    val projectId: Long? = null,
    val status: String? = null, // 动态状态，对应状态配置的 key
    val priorityImportance: Priority? = null,
    val priorityUrgency: Priority? = null,
    val estimatedValue: String? = null,
    val targetUsers: String? = null,
    val businessGoal: String? = null,
    val assigneeId: Long? = null,
    val expectedDeliveryDate: String? = null,
    val actualDeliveryDate: String? = null
)

@Serializable
data class RequirementListResponse(
    val requirements: List<Requirement>,
    val total: Int,
    val page: Int,
    val pageSize: Int
)

// 需求分解为任务的请求
@Serializable
data class RequirementBreakdownRequest(
    val requirementId: Long,
    val tasks: List<TaskBreakdownItem>
)

@Serializable
data class TaskBreakdownItem(
    val title: String,
    val description: String?,
    val taskType: String, // DESIGN, DEVELOPMENT, TESTING, etc.
    val estimatedHours: Double?,
    val assigneeId: Long?,
    val dueDate: String?,
    val dependencies: List<Long> = emptyList() // 依赖的任务ID列表
)

// 需求进度概览
@Serializable
data class RequirementProgress(
    val requirementId: Long,
    val totalTasks: Int,
    val completedTasks: Int,
    val inProgressTasks: Int,
    val blockedTasks: Int,
    val progressPercentage: Double,
    val estimatedTotalHours: Double,
    val actualTotalHours: Double,
    val isOnTrack: Boolean, // 是否按计划进行
    val riskLevel: String // LOW, MEDIUM, HIGH
)

// 业务价值报告（给老板看的）
@Serializable
data class BusinessValueReport(
    val requirementId: Long,
    val title: String,
    val businessDescription: String,
    val targetUsers: String?,
    val businessGoal: String?,
    val estimatedValue: String?,
    val status: String, // 动态状态，对应状态配置的 key
    val progressPercentage: Double,
    val expectedDeliveryDate: String?,
    val actualDeliveryDate: String?,
    val keyMilestones: List<Milestone>,
    val risks: List<String>,
    val achievements: List<String>
)

@Serializable
data class Milestone(
    val name: String,
    val description: String,
    val targetDate: String,
    val actualDate: String?,
    val status: String // PENDING, COMPLETED, DELAYED
)

// 需求选项（用于任务创建时选择）
@Serializable
data class RequirementOption(
    val id: Long,
    val title: String,
    val status: String // 动态状态，对应状态配置的 key
)

// 任务选项（用于选择上游任务）
@Serializable
data class TaskOption(
    val id: Long,
    val title: String,
    val status: String,
    val taskType: String
)
