package com.beefcake.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

// 任务状态配置表
object TaskStatusConfigs : IntIdTable("task_status_configs") {
    val key = varchar("key", 50).uniqueIndex()
    val label = varchar("label", 100)
    val color = varchar("color", 20)
    val icon = varchar("icon", 10)
    val description = text("description")
    val order = integer("order")
    val isActive = bool("is_active").default(true)
    val allowedTransitions = text("allowed_transitions") // JSON 字符串
    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at").default(LocalDateTime.now())
    val createdBy = integer("created_by").nullable()
    val updatedBy = integer("updated_by").nullable()
}

// 任务状态配置实体
class TaskStatusConfigEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<TaskStatusConfigEntity>(TaskStatusConfigs)

    var key by TaskStatusConfigs.key
    var label by TaskStatusConfigs.label
    var color by TaskStatusConfigs.color
    var icon by TaskStatusConfigs.icon
    var description by TaskStatusConfigs.description
    var order by TaskStatusConfigs.order
    var isActive by TaskStatusConfigs.isActive
    var allowedTransitions by TaskStatusConfigs.allowedTransitions
    var createdAt by TaskStatusConfigs.createdAt
    var updatedAt by TaskStatusConfigs.updatedAt
    var createdBy by TaskStatusConfigs.createdBy
    var updatedBy by TaskStatusConfigs.updatedBy

    fun toTaskStatusConfig(): TaskStatusConfig {
        return TaskStatusConfig(
            key = key,
            label = label,
            color = color,
            icon = icon,
            description = description,
            order = order,
            isActive = isActive,
            allowedTransitions = parseAllowedTransitions(allowedTransitions),
            createdAt = createdAt.toString(),
            updatedAt = updatedAt.toString(),
            createdBy = null, // 暂时设为 null，后续可以根据 ID 查询用户信息
            updatedBy = null
        )
    }

    private fun parseAllowedTransitions(json: String): List<String> {
        return try {
            kotlinx.serialization.json.Json.decodeFromString<List<String>>(json)
        } catch (e: Exception) {
            emptyList()
        }
    }
}

// 任务状态配置数据类
@Serializable
data class TaskStatusConfig(
    val key: String,
    val label: String,
    val color: String,
    val icon: String,
    val description: String,
    val order: Int,
    val isActive: Boolean,
    val allowedTransitions: List<String>,
    val createdAt: String? = null,
    val updatedAt: String? = null,
    val createdBy: User? = null,
    val updatedBy: User? = null
)

// 创建任务状态配置请求
@Serializable
data class CreateTaskStatusConfigRequest(
    val key: String,
    val label: String,
    val color: String,
    val icon: String,
    val description: String,
    val order: Int,
    val isActive: Boolean = true,
    val allowedTransitions: List<String> = emptyList()
)

// 更新任务状态配置请求
@Serializable
data class UpdateTaskStatusConfigRequest(
    val label: String? = null,
    val color: String? = null,
    val icon: String? = null,
    val description: String? = null,
    val order: Int? = null,
    val isActive: Boolean? = null,
    val allowedTransitions: List<String>? = null
)

// 批量更新任务状态配置请求
@Serializable
data class BatchUpdateTaskStatusConfigsRequest(
    val configs: List<TaskStatusConfig>
)

// 任务状态转换验证请求
@Serializable
data class TaskStatusTransitionValidationRequest(
    val fromStatus: String,
    val toStatus: String
)

// 任务状态转换验证响应
@Serializable
data class TaskStatusTransitionValidationResponse(
    val isAllowed: Boolean,
    val reason: String? = null
)

// 任务状态统计数据
@Serializable
data class TaskStatusStatistics(
    val statusKey: String,
    val statusLabel: String,
    val count: Int,
    val percentage: Double
)

// 任务状态变更日志
@Serializable
data class TaskStatusChangeLog(
    val id: Int,
    val taskId: Long,
    val fromStatus: String,
    val toStatus: String,
    val fromStatusLabel: String,
    val toStatusLabel: String,
    val changedBy: Long,
    val changedAt: String,
    val reason: String? = null,
    val metadata: String? = null
)
