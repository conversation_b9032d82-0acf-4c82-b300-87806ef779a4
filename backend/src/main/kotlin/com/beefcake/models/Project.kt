package com.beefcake.models

import kotlinx.serialization.Serializable

@Serializable
data class Project(
    val id: Long,
    val name: String,
    val description: String?,
    val createdBy: Long,
    val createdAt: String,
    val updatedAt: String,
    val creator: User? = null,
    val requirementCount: Int = 0 // 关联的需求数量
)

@Serializable
data class ProjectCreateRequest(
    val name: String,
    val description: String? = null
)

@Serializable
data class ProjectUpdateRequest(
    val name: String? = null,
    val description: String? = null
)

@Serializable
data class ProjectListResponse(
    val projects: List<Project>,
    val total: Int
)

@Serializable
data class ProjectOption(
    val id: Long,
    val name: String
)
