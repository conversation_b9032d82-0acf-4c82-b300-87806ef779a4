package com.beefcake.constants

/**
 * 任务状态常量
 * 这些常量对应数据库中 task_status_configs 表的默认状态配置
 */
object TaskStatusConstants {
    const val TODO = "TODO"
    const val IN_PROGRESS = "IN_PROGRESS"
    const val BLOCKED = "BLOCKED"
    const val REVIEW = "REVIEW"
    const val TESTING = "TESTING"
    const val DONE = "DONE"
    
    // 所有状态的列表
    val ALL_STATUSES = listOf(
        TODO,
        IN_PROGRESS,
        BLOCKED,
        REVIEW,
        TESTING,
        DONE
    )
    
    // 进行中的状态（用于统计）
    val IN_PROGRESS_STATUSES = listOf(
        IN_PROGRESS,
        REVIEW,
        TESTING
    )
    
    // 已完成的状态
    val COMPLETED_STATUSES = listOf(
        DONE
    )
    
    // 阻塞状态
    val BLOCKED_STATUSES = listOf(
        BLOCKED
    )
    
    // 待开始状态
    val TODO_STATUSES = listOf(
        TODO
    )
}
