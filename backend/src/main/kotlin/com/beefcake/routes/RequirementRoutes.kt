package com.beefcake.routes

// RequirementStatus 枚举已移除，现在使用动态状态配置
import com.beefcake.models.*
import com.beefcake.services.RequirementService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondNotFound
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.requirementRoutes() {
    val requirementService = RequirementService()
    val logger = LoggerFactory.getLogger("RequirementRoutes")
    
    route("/requirements") {
        authenticate("auth-jwt") {
            // 创建需求
            post {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }
                    
                    val request = call.receive<RequirementCreateRequest>()
                    val result = requirementService.createRequirement(request, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "需求创建成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "需求创建失败")
                    }
                } catch (e: Exception) {
                    logger.error("创建需求异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 获取需求列表
            get {
                try {
                    val status = call.request.queryParameters["status"] // 直接使用字符串状态
                    val assigneeId = call.request.queryParameters["assigneeId"]?.toLongOrNull()
                    val creatorId = call.request.queryParameters["creatorId"]?.toLongOrNull()
                    val search = call.request.queryParameters["search"] // 添加搜索参数
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20

                    val result = requirementService.getRequirementList(status, assigneeId, creatorId, search, page, pageSize)
                    call.respondSuccess(result, "获取需求列表成功")
                } catch (e: Exception) {
                    logger.error("获取需求列表异常", e)
                    call.respondError("获取需求列表失败")
                }
            }

            // 获取简化的需求选项列表（用于任务创建时选择）
            get("/options") {
                try {
                    val result = requirementService.getRequirementOptions()
                    call.respondSuccess(result, "获取需求选项成功")
                } catch (e: Exception) {
                    logger.error("获取需求选项异常", e)
                    call.respondError("获取需求选项失败")
                }
            }

            // 获取有任务的需求列表（用于任务管理页面）
            get("/with-tasks") {
                try {
                    val status = call.request.queryParameters["status"]
                    val assigneeId = call.request.queryParameters["assigneeId"]?.toLongOrNull()
                    val creatorId = call.request.queryParameters["creatorId"]?.toLongOrNull()
                    val search = call.request.queryParameters["search"] // 添加搜索参数
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20

                    val result = requirementService.getRequirementsWithTasks(status, assigneeId, creatorId, search, page, pageSize)
                    call.respondSuccess(result, "获取有任务的需求列表成功")
                } catch (e: Exception) {
                    logger.error("获取有任务的需求列表异常", e)
                    call.respondError("获取有任务的需求列表失败")
                }
            }

            // 获取单个需求
            get("/{id}") {
                try {
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@get
                    }
                    
                    val requirement = requirementService.getRequirementById(id)
                    if (requirement != null) {
                        call.respondSuccess(requirement, "获取需求成功")
                    } else {
                        call.respondNotFound("需求不存在")
                    }
                } catch (e: Exception) {
                    logger.error("获取需求异常", e)
                    call.respondError("获取需求失败")
                }
            }
            
            // 更新需求
            put("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@put
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@put
                    }
                    
                    val request = call.receive<RequirementUpdateRequest>()
                    val result = requirementService.updateRequirement(id, request, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "需求更新成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "需求更新失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新需求异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 更新需求状态
            patch("/{id}/status") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@patch
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@patch
                    }
                    
                    val request = call.receive<Map<String, String>>()
                    val status = request["status"]
                        ?: return@patch call.respondError("状态参数错误")
                    
                    val result = requirementService.updateRequirementStatus(id, status, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "需求状态更新成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "需求状态更新失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新需求状态异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 删除需求
            delete("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@delete
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@delete
                    }
                    
                    val result = requirementService.deleteRequirement(id, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess("需求删除成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "需求删除失败")
                    }
                } catch (e: Exception) {
                    logger.error("删除需求异常", e)
                    call.respondError("删除需求失败")
                }
            }
            
            // 需求分解为任务
            post("/{id}/breakdown") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@post
                    }
                    
                    val request = call.receive<RequirementBreakdownRequest>()
                    val result = requirementService.breakdownRequirement(request, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "需求分解成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "需求分解失败")
                    }
                } catch (e: Exception) {
                    logger.error("需求分解异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 获取需求看板数据
            get("/kanban") {
                try {
                    val kanbanData = requirementService.getRequirementKanbanData()
                    call.respondSuccess(kanbanData, "获取需求看板数据成功")
                } catch (e: Exception) {
                    logger.error("获取需求看板数据异常", e)
                    call.respondError("获取需求看板数据失败")
                }
            }
            
            // 获取已归档需求列表
            get("/archived") {
                try {
                    val assigneeId = call.request.queryParameters["assigneeId"]?.toLongOrNull()
                    val creatorId = call.request.queryParameters["creatorId"]?.toLongOrNull()
                    val search = call.request.queryParameters["search"] // 标题模糊搜索
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20

                    val result = requirementService.getArchivedRequirements(assigneeId, creatorId, search, page, pageSize)
                    call.respondSuccess(result, "获取已归档需求列表成功")
                } catch (e: Exception) {
                    logger.error("获取已归档需求列表异常", e)
                    call.respondError("获取已归档需求列表失败")
                }
            }

            // 获取业务价值报告
            get("/{id}/business-report") {
                try {
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("需求ID格式错误")
                        return@get
                    }

                    val report = requirementService.getBusinessValueReport(id)
                    if (report != null) {
                        call.respondSuccess(report, "获取业务价值报告成功")
                    } else {
                        call.respondNotFound("需求不存在")
                    }
                } catch (e: Exception) {
                    logger.error("获取业务价值报告异常", e)
                    call.respondError("获取业务价值报告失败")
                }
            }
        }
    }
}
