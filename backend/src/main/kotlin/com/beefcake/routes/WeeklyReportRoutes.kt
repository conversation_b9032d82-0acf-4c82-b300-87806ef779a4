package com.beefcake.routes

import com.beefcake.models.WeeklyReportRequest
import com.beefcake.services.WeeklyReportService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.weeklyReportRoutes(weeklyReportService: WeeklyReportService) {
    val logger = LoggerFactory.getLogger("WeeklyReportRoutes")

    route("/weekly-report") {
        authenticate("auth-jwt") {
            // 生成周报
            post {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)

                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }

                    val request = try {
                        call.receive<WeeklyReportRequest>()
                    } catch (e: Exception) {
                        // 如果没有请求体，使用默认参数
                        WeeklyReportRequest()
                    }

                    val report = weeklyReportService.generateWeeklyReport(request)
                    call.respondSuccess(report, "周报生成成功")

                } catch (e: Exception) {
                    logger.error("生成周报异常", e)
                    call.respondError("生成周报失败: ${e.message}")
                }
            }

            // 获取默认周报（本周）
            get {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)

                    if (userId == null) {
                        call.respondUnauthorized()
                        return@get
                    }

                    // 从查询参数获取筛选条件
                    val projectIds = call.request.queryParameters.getAll("projectId")
                        ?.mapNotNull { it.toLongOrNull() }
                    val assigneeIds = call.request.queryParameters.getAll("assigneeId")
                        ?.mapNotNull { it.toLongOrNull() }
                    val includeArchived = call.request.queryParameters["includeArchived"]?.toBoolean() ?: false

                    val request = WeeklyReportRequest(
                        projectIds = projectIds,
                        assigneeIds = assigneeIds,
                        includeArchived = includeArchived
                    )

                    val report = weeklyReportService.generateWeeklyReport(request)
                    call.respondSuccess(report, "周报获取成功")

                } catch (e: Exception) {
                    logger.error("获取周报异常", e)
                    call.respondError("获取周报失败: ${e.message}")
                }
            }
        }
    }
}
