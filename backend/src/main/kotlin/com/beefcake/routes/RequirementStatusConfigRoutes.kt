package com.beefcake.routes

import com.beefcake.models.*
import com.beefcake.services.RequirementStatusConfigService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

fun Route.requirementStatusConfigRoutes(statusConfigService: RequirementStatusConfigService) {
    route("/requirement-status") {

        // 获取所有状态配置（公开接口）
        get("/configs") {
            try {
                val configs = statusConfigService.getActiveStatusConfigs()
                call.respond(configs)
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, "获取状态配置失败: ${e.message}")
            }
        }

        // 创建状态配置
        post("/configs") {
            try {
                val request = call.receive<CreateRequirementStatusConfigRequest>()
                val config = statusConfigService.createStatusConfig(request, 1) // 暂时使用固定用户ID
                call.respond(HttpStatusCode.Created, config)
            } catch (e: IllegalArgumentException) {
                call.respond(HttpStatusCode.BadRequest, "创建状态配置失败: ${e.message}")
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, "创建状态配置失败: ${e.message}")
            }
        }

        // 更新状态配置
        put("/configs/{key}") {
            try {
                val key = call.parameters["key"] ?: throw IllegalArgumentException("缺少状态 key")
                val request = call.receive<UpdateRequirementStatusConfigRequest>()
                val config = statusConfigService.updateStatusConfig(key, request, 1) // 暂时使用固定用户ID
                call.respond(config)
            } catch (e: IllegalArgumentException) {
                call.respond(HttpStatusCode.BadRequest, "更新状态配置失败: ${e.message}")
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, "更新状态配置失败: ${e.message}")
            }
        }

        // 删除状态配置
        delete("/configs/{key}") {
            try {
                val key = call.parameters["key"] ?: throw IllegalArgumentException("缺少状态 key")
                val deleted = statusConfigService.deleteStatusConfig(key)
                if (deleted) {
                    call.respond(HttpStatusCode.OK, "删除状态配置成功")
                } else {
                    call.respond(HttpStatusCode.NotFound, "状态配置不存在")
                }
            } catch (e: IllegalStateException) {
                call.respond(HttpStatusCode.Conflict, "删除状态配置失败: ${e.message}")
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, "删除状态配置失败: ${e.message}")
            }
        }

        // 获取单个状态配置
        get("/configs/{key}") {
            try {
                val key = call.parameters["key"] ?: throw IllegalArgumentException("缺少状态 key")
                val config = statusConfigService.getStatusConfigByKey(key)
                if (config != null) {
                    call.respond(config)
                } else {
                    call.respond(HttpStatusCode.NotFound, "状态配置不存在")
                }
            } catch (e: Exception) {
                call.respond(HttpStatusCode.BadRequest, "获取状态配置失败: ${e.message}")
            }
        }

    }
}
