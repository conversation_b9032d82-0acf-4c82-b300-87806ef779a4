package com.beefcake.routes

import com.beefcake.database.tables.UserType
import com.beefcake.models.ProjectCreateRequest
import com.beefcake.models.ProjectUpdateRequest
import com.beefcake.services.ProjectService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondForbidden
import com.beefcake.utils.ResponseUtils.respondNotFound
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

private val logger = LoggerFactory.getLogger("ProjectRoutes")

fun Route.projectRoutes() {
    val projectService = ProjectService()

    route("/projects") {
        // 所有项目相关操作都需要超级管理员权限
        authenticate("auth-jwt") {
            
            // 获取所有项目
            get {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@get
                    }

                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以访问项目管理")
                        return@get
                    }

                    val projects = projectService.getAllProjects()
                    call.respondSuccess(projects, "获取项目列表成功")
                } catch (e: Exception) {
                    logger.error("获取项目列表异常", e)
                    call.respondError("获取项目列表失败")
                }
            }

            // 创建项目
            post {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@post
                    }

                    val userId = principal.payload.getClaim("userId").asLong()
                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以创建项目")
                        return@post
                    }

                    if (userId == null) {
                        call.respondUnauthorized("无效的用户ID")
                        return@post
                    }

                    val request = call.receive<ProjectCreateRequest>()
                    val project = projectService.createProject(
                        name = request.name,
                        description = request.description,
                        createdBy = userId
                    )

                    call.respondSuccess(project, "项目创建成功")
                } catch (e: Exception) {
                    logger.error("创建项目异常", e)
                    call.respondError("创建项目失败: ${e.message}")
                }
            }

            // 获取单个项目
            get("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@get
                    }

                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以访问项目详情")
                        return@get
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的项目ID")
                        return@get
                    }

                    val project = projectService.getProject(id)
                    if (project == null) {
                        call.respondNotFound("项目不存在")
                        return@get
                    }

                    call.respondSuccess(project, "获取项目详情成功")
                } catch (e: Exception) {
                    logger.error("获取项目详情异常", e)
                    call.respondError("获取项目详情失败")
                }
            }

            // 更新项目
            put("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@put
                    }

                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以更新项目")
                        return@put
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的项目ID")
                        return@put
                    }

                    val request = call.receive<ProjectUpdateRequest>()
                    val project = projectService.updateProject(
                        id = id,
                        name = request.name,
                        description = request.description
                    )

                    if (project == null) {
                        call.respondNotFound("项目不存在")
                        return@put
                    }

                    call.respondSuccess(project, "项目更新成功")
                } catch (e: Exception) {
                    logger.error("更新项目异常", e)
                    call.respondError("更新项目失败: ${e.message}")
                }
            }

            // 删除项目
            delete("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@delete
                    }

                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以删除项目")
                        return@delete
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的项目ID")
                        return@delete
                    }

                    val deleted = projectService.deleteProject(id)
                    if (!deleted) {
                        call.respondError("无法删除项目，该项目下还有关联的需求")
                        return@delete
                    }

                    call.respondSuccess("项目删除成功")
                } catch (e: Exception) {
                    logger.error("删除项目异常", e)
                    call.respondError("删除项目失败: ${e.message}")
                }
            }

            // 检查项目是否可以删除
            get("/{id}/can-delete") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@get
                    }

                    val userTypeStr = principal.payload.getClaim("userType").asString()
                    val userType = try {
                        UserType.valueOf(userTypeStr ?: "NORMAL")
                    } catch (e: Exception) {
                        UserType.NORMAL
                    }

                    if (userType != UserType.SUPER_ADMIN) {
                        call.respondForbidden("只有超级管理员可以检查项目删除权限")
                        return@get
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的项目ID")
                        return@get
                    }

                    val canDelete = projectService.canDeleteProject(id)
                    call.respondSuccess(mapOf("canDelete" to canDelete), "检查删除权限成功")
                } catch (e: Exception) {
                    logger.error("检查项目删除权限异常", e)
                    call.respondError("检查删除权限失败")
                }
            }
        }

        // 获取项目选项（用于需求创建时选择项目）- 所有认证用户都可以访问
        authenticate("auth-jwt") {
            get("/options") {
                try {
                    val options = projectService.getProjectOptions()
                    call.respondSuccess(options, "获取项目选项成功")
                } catch (e: Exception) {
                    logger.error("获取项目选项异常", e)
                    call.respondError("获取项目选项失败")
                }
            }
        }
    }
}
