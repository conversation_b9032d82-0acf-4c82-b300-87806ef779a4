package com.beefcake.routes

import com.beefcake.database.tables.UserType
import com.beefcake.models.PasswordChangeRequest
import com.beefcake.models.PasswordResetRequest
import com.beefcake.models.UserUpdateRequest
import com.beefcake.services.UserService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondForbidden
import com.beefcake.utils.ResponseUtils.respondNotFound
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*

fun Route.userRoutes() {
    val userService = UserService()
    val logger = LoggerFactory.getLogger("UserRoutes")
    
    route("/users") {
        authenticate("auth-jwt") {
            get("/me") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@get
                    }
                    
                    val user = userService.getUserById(userId)
                    if (user != null) {
                        call.respondSuccess(user, "获取用户信息成功")
                    } else {
                        call.respondNotFound("用户不存在")
                    }
                } catch (e: Exception) {
                    logger.error("获取用户信息异常", e)
                    call.respondError("获取用户信息失败")
                }
            }
            
            put("/me") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@put
                    }
                    
                    val request = call.receive<UserUpdateRequest>()
                    val result = userService.updateProfile(userId, request)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "更新用户信息成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "更新用户信息失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新用户信息异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            post("/change-password") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }
                    
                    val request = call.receive<PasswordChangeRequest>()
                    val result = userService.changePassword(userId, request)
                    
                    if (result.isSuccess) {
                        call.respondSuccess("密码修改成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "密码修改失败")
                    }
                } catch (e: Exception) {
                    logger.error("修改密码异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 管理员功能
            get("/list") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)
                    
                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@get
                    }
                    
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20
                    
                    val result = userService.getUserList(page, pageSize)
                    call.respondSuccess(result, "获取用户列表成功")
                } catch (e: Exception) {
                    logger.error("获取用户列表异常", e)
                    call.respondError("获取用户列表失败")
                }
            }

            // 管理员功能
            get("/all") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)

                    if (userId == null) {
                        call.respondUnauthorized()
                        return@get
                    }

                    val result = userService.getAllUsers()
                    call.respondSuccess(result, "获取用户列表成功")
                } catch (e: Exception) {
                    logger.error("获取用户列表异常", e)
                    call.respondError("获取用户列表失败")
                }
            }
            
            post("/reset-password") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val adminUserId = principal?.getClaim("userId", Long::class)
                    val userType = principal?.getClaim("userType", String::class)

                    if (adminUserId == null || userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@post
                    }

                    val request = call.receive<PasswordResetRequest>()
                    val result = userService.resetPassword(adminUserId, request.userId)

                    if (result.isSuccess) {
                        call.respondSuccess("密码重置成功，新密码为：1234")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "密码重置失败")
                    }
                } catch (e: Exception) {
                    logger.error("重置密码异常", e)
                    call.respondError("请求格式错误")
                }
            }

            // 管理员编辑用户信息
            put("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val adminUserId = principal?.getClaim("userId", Long::class)
                    val userType = principal?.getClaim("userType", String::class)

                    if (adminUserId == null || userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@put
                    }

                    val userId = call.parameters["id"]?.toLongOrNull()
                    if (userId == null) {
                        call.respondError("无效的用户ID")
                        return@put
                    }

                    val request = call.receive<UserUpdateRequest>()
                    val result = userService.updateUserByAdmin(adminUserId, userId, request)

                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "更新用户信息成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "更新用户信息失败")
                    }
                } catch (e: Exception) {
                    logger.error("管理员更新用户信息异常", e)
                    call.respondError("请求格式错误")
                }
            }

            // 管理员更新用户状态
            put("/{id}/status") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val adminUserId = principal?.getClaim("userId", Long::class)
                    val userType = principal?.getClaim("userType", String::class)

                    if (adminUserId == null || userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@put
                    }

                    val userId = call.parameters["id"]?.toLongOrNull()
                    if (userId == null) {
                        call.respondError("无效的用户ID")
                        return@put
                    }

                    val request = call.receive<Map<String, String>>()
                    val status = request["status"]
                    if (status == null) {
                        call.respondError("缺少状态参数")
                        return@put
                    }

                    val result = userService.updateUserStatus(adminUserId, userId, status)

                    if (result.isSuccess) {
                        call.respondSuccess("用户状态更新成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "用户状态更新失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新用户状态异常", e)
                    call.respondError("请求格式错误")
                }
            }

            // 头像上传
            post("/upload-avatar") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)

                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }

                    val multipartData = call.receiveMultipart()
                    var fileName: String? = null

                    multipartData.forEachPart { part ->
                        when (part) {
                            is PartData.FileItem -> {
                                val originalFileName = part.originalFileName
                                if (originalFileName != null) {
                                    // 验证文件类型
                                    val contentType = part.contentType
                                    val allowedTypes = listOf(
                                        "image/jpeg",
                                        "image/jpg",
                                        "image/png",
                                        "image/webp"
                                    )

                                    if (contentType == null || !allowedTypes.contains(contentType.toString().lowercase())) {
                                        call.respondError("只支持 JPG、PNG、WEBP 格式的图片")
                                        return@forEachPart
                                    }

                                    // 获取文件扩展名并验证
                                    val fileExtension = originalFileName.substringAfterLast(".", "").lowercase()
                                    val allowedExtensions = listOf("jpg", "jpeg", "png", "webp")

                                    if (!allowedExtensions.contains(fileExtension)) {
                                        call.respondError("只支持 JPG、PNG、WEBP 格式的图片")
                                        return@forEachPart
                                    }

                                    // 生成唯一文件名，避免缓存问题
                                    val uniqueFileName = "avatar_${userId}_${UUID.randomUUID()}.${fileExtension}"

                                    // 确保上传目录存在
                                    val uploadDir = File("uploads/avatars")
                                    if (!uploadDir.exists()) {
                                        uploadDir.mkdirs()
                                    }

                                    // 删除该用户的旧头像文件
                                    uploadDir.listFiles()?.forEach { file ->
                                        if (file.name.startsWith("avatar_${userId}_") && file.name != uniqueFileName) {
                                            file.delete()
                                            logger.info("删除旧头像文件: ${file.name}")
                                        }
                                    }

                                    // 保存新文件
                                    val file = File(uploadDir, uniqueFileName)
                                    part.streamProvider().use { input ->
                                        file.outputStream().buffered().use { output ->
                                            input.copyTo(output)
                                        }
                                    }

                                    fileName = uniqueFileName
                                    logger.info("用户 $userId 上传头像成功: $uniqueFileName")
                                }
                            }
                            else -> {}
                        }
                        part.dispose()
                    }

                    if (fileName != null) {
                        // 构建头像URL，使用文件名
                        val avatarUrl = "/api/files/avatars/$fileName"

                        // 更新用户头像URL
                        val updateRequest = UserUpdateRequest(nickname = null, avatarUrl = avatarUrl)
                        val result = userService.updateProfile(userId, updateRequest)

                        if (result.isSuccess) {
                            call.respondSuccess(mapOf("avatarUrl" to avatarUrl), "头像上传成功")
                        } else {
                            call.respondError("更新头像失败")
                        }
                    } else {
                        call.respondError("未找到有效的图片文件")
                    }
                } catch (e: Exception) {
                    logger.error("头像上传异常", e)
                    call.respondError("头像上传失败")
                }
            }
        }
    }
}
