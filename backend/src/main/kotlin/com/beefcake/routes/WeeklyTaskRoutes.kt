package com.beefcake.routes

import com.beefcake.database.tables.UserType
import com.beefcake.services.WeeklyTaskService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

private val logger = LoggerFactory.getLogger("WeeklyTaskRoutes")

fun Route.weeklyTaskRoutes() {
    val weeklyTaskService = WeeklyTaskService()

    route("/weekly-tasks") {
        // 获取本周任务 - 需要认证
        authenticate("auth-jwt") {
            get {
                try {
                    // 从JWT中获取用户信息
                    val principal = call.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respondUnauthorized("未找到认证信息")
                        return@get
                    }

                    val userId = principal.payload.getClaim("userId").asLong()
                    val userTypeStr = principal.payload.getClaim("userType").asString()

                    if (userId == null) {
                        call.respondUnauthorized("无效的用户ID")
                        return@get
                    }

                    // 判断是否为管理员
                    val isAdmin = try {
                        val userType = UserType.valueOf(userTypeStr ?: "NORMAL")
                        userType == UserType.SUPER_ADMIN
                    } catch (e: Exception) {
                        logger.warn("无法解析用户类型: $userTypeStr", e)
                        false
                    }

                    // 获取查询参数
                    val startDate = call.request.queryParameters["startDate"]
                    val endDate = call.request.queryParameters["endDate"]

                    logger.info("用户 $userId (类型: $userTypeStr, 管理员: $isAdmin) 请求周任务, 日期范围: $startDate - $endDate")

                    val tasks = if (startDate != null && endDate != null) {
                        weeklyTaskService.getWeeklyTasksByDateRange(userId, isAdmin, startDate, endDate)
                    } else {
                        weeklyTaskService.getWeeklyTasks(userId, isAdmin)
                    }

                    call.respondSuccess(tasks, "获取周任务成功")
                } catch (e: Exception) {
                    logger.error("获取本周任务异常", e)
                    call.respondError("获取本周任务失败: ${e.message}")
                }
            }
        }
    }
}
