package com.beefcake.routes

import com.beefcake.utils.ResponseUtils.respondError
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory
import java.io.File

fun Route.fileRoutes() {
    val logger = LoggerFactory.getLogger("FileRoutes")

    // 用户头像访问接口（隐藏文件路径）
    get("/avatar/{userId}") {
        try {
            val userId = call.parameters["userId"]?.toLongOrNull()
            if (userId == null) {
                call.respond(HttpStatusCode.BadRequest, "无效的用户ID")
                return@get
            }

            // 忽略时间戳参数（用于避免浏览器缓存）
            // 时间戳参数会自动被忽略，不影响文件查找

            // 查找用户头像文件（按优先级：webp > png > jpg > jpeg）
            val uploadDir = File("uploads/avatars")
            val extensions = listOf("webp", "png", "jpg", "jpeg")
            var avatarFile: File? = null

            for (ext in extensions) {
                val file = File(uploadDir, "avatar_${userId}.${ext}")
                if (file.exists()) {
                    avatarFile = file
                    break
                }
            }

            if (avatarFile == null || !avatarFile.exists()) {
                call.respond(HttpStatusCode.NotFound, "头像不存在")
                return@get
            }

            // 根据文件扩展名设置Content-Type
            val contentType = when (avatarFile.extension.lowercase()) {
                "jpg", "jpeg" -> ContentType.Image.JPEG
                "png" -> ContentType.Image.PNG
                "webp" -> ContentType.parse("image/webp")
                else -> ContentType.Application.OctetStream
            }

            call.response.header(HttpHeaders.ContentType, contentType.toString())
            call.response.header(HttpHeaders.CacheControl, "public, max-age=86400") // 缓存1天
            call.response.header(HttpHeaders.ETag, "\"avatar-${userId}-${avatarFile.lastModified()}\"") // ETag for better caching
            call.respondFile(avatarFile)

        } catch (e: Exception) {
            logger.error("头像服务异常", e)
            call.respond(HttpStatusCode.InternalServerError, "头像服务错误")
        }
    }

    route("/files") {
        // 保留原有的文件服务（用于其他文件类型）
        get("/avatars/{filename}") {
            try {
                val filename = call.parameters["filename"]
                if (filename == null) {
                    call.respondError("文件名不能为空")
                    return@get
                }

                // 验证文件名安全性（防止路径遍历攻击）
                if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
                    call.respondError("无效的文件名")
                    return@get
                }

                val file = File("uploads/avatars", filename)
                if (!file.exists()) {
                    call.respond(HttpStatusCode.NotFound, "文件不存在")
                    return@get
                }

                // 根据文件扩展名设置Content-Type
                val contentType = when (file.extension.lowercase()) {
                    "jpg", "jpeg" -> ContentType.Image.JPEG
                    "png" -> ContentType.Image.PNG
                    "gif" -> ContentType.Image.GIF
                    "webp" -> ContentType.parse("image/webp")
                    else -> ContentType.Application.OctetStream
                }

                call.response.header(HttpHeaders.ContentType, contentType.toString())
                call.response.header(HttpHeaders.CacheControl, "public, max-age=86400") // 缓存1天
                call.respondFile(file)

            } catch (e: Exception) {
                logger.error("文件服务异常", e)
                call.respondError("文件服务错误")
            }
        }
    }
}
