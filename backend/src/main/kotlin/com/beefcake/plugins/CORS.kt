package com.beefcake.plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.server.plugins.cors.routing.*
import org.slf4j.LoggerFactory

fun Application.configureCORS(config: ApplicationConfig) {
    val log = LoggerFactory.getLogger("CORS")

    install(CORS) {
        // 允许所有HTTP方法
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Get)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Patch)
        allowMethod(HttpMethod.Head)

        // 允许常用请求头
        allowHeader(HttpHeaders.Authorization)
        allowHeader(HttpHeaders.ContentType)
        allowHeader(HttpHeaders.Accept)
        allowHeader(HttpHeaders.Origin)
        allowHeader("X-Requested-With")

        // 允许携带凭证
        allowCredentials = true

        // 设置预检请求的缓存时间
        maxAgeInSeconds = 3600

        anyHost()
        allowCredentials = true
        allowNonSimpleContentTypes = true

        log.info("CORS: 生产环境，允许所有来源")

//        // 开发环境允许所有来源
//        val environment = config.propertyOrNull("app.environment")?.getString() ?: "development"
//        if (environment == "development") {
//            anyHost()
//            log.info("CORS: 开发环境，允许所有来源")
//        } else {
//            // 生产环境指定允许的域名
//            allowHost("localhost:3000")
//            allowHost("127.0.0.1:3000")
//            allowHost("*************:3000", schemes = listOf("http", "https"))
//            log.info("CORS: 生产环境，只允许指定域名")
//        }
    }
}
