package com.beefcake.plugins

import com.beefcake.routes.authRoutes
import com.beefcake.routes.userRoutes
import com.beefcake.routes.taskRoutes
import com.beefcake.routes.requirementRoutes
import com.beefcake.routes.requirementStatusConfigRoutes
import com.beefcake.routes.weeklyTaskRoutes
import com.beefcake.routes.projectRoutes
import com.beefcake.routes.fileRoutes
import com.beefcake.routes.weeklyReportRoutes
import com.beefcake.services.RequirementStatusConfigService
import com.beefcake.services.WeeklyReportService
import com.beefcake.repositories.RequirementRepository
import com.beefcake.repositories.TaskRepository
import com.beefcake.repositories.UserRepository
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

fun Application.configureRouting() {
    routing {
        get("/") {
            call.respondText("猛男项目管理系统 API 服务正在运行")
        }
        
        get("/health") {
            call.respondText("OK")
        }
        
        route("/api") {
            authRoutes()
            userRoutes()
            taskRoutes()
            requirementRoutes()
            weeklyTaskRoutes()
            projectRoutes()
            fileRoutes()

            // 状态配置路由
            val statusConfigService = RequirementStatusConfigService(RequirementRepository())
            requirementStatusConfigRoutes(statusConfigService)

            // 周报路由
            val weeklyReportService = WeeklyReportService(
                RequirementRepository(),
                TaskRepository(),
                UserRepository(),
                statusConfigService
            )
            weeklyReportRoutes(weeklyReportService)
        }
    }
}
