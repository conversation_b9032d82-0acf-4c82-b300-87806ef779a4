package com.beefcake.repositories

import com.beefcake.models.*
import com.beefcake.database.tables.Tasks as TasksTable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime

class TaskStatusConfigRepository {

    // 获取所有任务状态配置
    fun findAll(): List<TaskStatusConfig> = transaction {
        TaskStatusConfigEntity.all()
            .orderBy(TaskStatusConfigs.order to SortOrder.ASC)
            .map { it.toTaskStatusConfig() }
    }

    // 获取激活的任务状态配置
    fun findAllActive(): List<TaskStatusConfig> = transaction {
        TaskStatusConfigEntity.find { TaskStatusConfigs.isActive eq true }
            .orderBy(TaskStatusConfigs.order to SortOrder.ASC)
            .map { it.toTaskStatusConfig() }
    }

    // 根据 key 查找状态配置
    fun findByKey(key: String): TaskStatusConfig? = transaction {
        TaskStatusConfigEntity.find { TaskStatusConfigs.key eq key }
            .firstOrNull()?.toTaskStatusConfig()
    }

    // 创建任务状态配置
    fun create(request: CreateTaskStatusConfigRequest): TaskStatusConfig = transaction {
        val entity = TaskStatusConfigEntity.new {
            key = request.key
            label = request.label
            color = request.color
            icon = request.icon
            description = request.description
            order = request.order
            isActive = request.isActive
            allowedTransitions = Json.encodeToString(request.allowedTransitions)
            createdAt = LocalDateTime.now()
            updatedAt = LocalDateTime.now()
        }
        entity.toTaskStatusConfig()
    }

    // 更新任务状态配置
    fun update(key: String, request: UpdateTaskStatusConfigRequest): TaskStatusConfig? = transaction {
        val entity = TaskStatusConfigEntity.find { TaskStatusConfigs.key eq key }.firstOrNull()
        entity?.let {
            request.label?.let { label -> it.label = label }
            request.color?.let { color -> it.color = color }
            request.icon?.let { icon -> it.icon = icon }
            request.description?.let { description -> it.description = description }
            request.order?.let { order -> it.order = order }
            request.isActive?.let { isActive -> it.isActive = isActive }
            request.allowedTransitions?.let { transitions -> 
                it.allowedTransitions = Json.encodeToString(transitions)
            }
            it.updatedAt = LocalDateTime.now()
            it.toTaskStatusConfig()
        }
    }

    // 删除任务状态配置
    fun delete(key: String): Boolean = transaction {
        val entity = TaskStatusConfigEntity.find { TaskStatusConfigs.key eq key }.firstOrNull()
        entity?.let {
            it.delete()
            true
        } ?: false
    }

    // 批量更新任务状态配置
    fun batchUpdate(request: BatchUpdateTaskStatusConfigsRequest): List<TaskStatusConfig> = transaction {
        val results = mutableListOf<TaskStatusConfig>()
        
        request.configs.forEach { config ->
            val entity = TaskStatusConfigEntity.find { TaskStatusConfigs.key eq config.key }.firstOrNull()
            entity?.let {
                it.label = config.label
                it.color = config.color
                it.icon = config.icon
                it.description = config.description
                it.order = config.order
                it.isActive = config.isActive
                it.allowedTransitions = Json.encodeToString(config.allowedTransitions)
                it.updatedAt = LocalDateTime.now()
                results.add(it.toTaskStatusConfig())
            }
        }
        
        results
    }

    // 验证状态转换是否允许
    fun validateTransition(fromStatus: String, toStatus: String): TaskStatusTransitionValidationResponse = transaction {
        val fromConfig = findByKey(fromStatus)
        
        if (fromConfig == null) {
            return@transaction TaskStatusTransitionValidationResponse(
                isAllowed = false,
                reason = "源状态 '$fromStatus' 不存在"
            )
        }
        
        if (!fromConfig.isActive) {
            return@transaction TaskStatusTransitionValidationResponse(
                isAllowed = false,
                reason = "源状态 '$fromStatus' 未激活"
            )
        }
        
        val toConfig = findByKey(toStatus)
        if (toConfig == null) {
            return@transaction TaskStatusTransitionValidationResponse(
                isAllowed = false,
                reason = "目标状态 '$toStatus' 不存在"
            )
        }
        
        if (!toConfig.isActive) {
            return@transaction TaskStatusTransitionValidationResponse(
                isAllowed = false,
                reason = "目标状态 '$toStatus' 未激活"
            )
        }
        
        if (toStatus in fromConfig.allowedTransitions) {
            TaskStatusTransitionValidationResponse(isAllowed = true)
        } else {
            TaskStatusTransitionValidationResponse(
                isAllowed = false,
                reason = "不允许从 '${fromConfig.label}' 转换到 '${toConfig.label}'"
            )
        }
    }

    // 获取任务状态统计
    fun getStatusStatistics(): List<TaskStatusStatistics> = transaction {
        val totalTasks = TaskStatusConfigs
            .join(TasksTable, JoinType.LEFT, TaskStatusConfigs.key, TasksTable.status)
            .selectAll()
            .count()
            .toDouble()

        if (totalTasks == 0.0) {
            return@transaction emptyList()
        }

        TaskStatusConfigs
            .join(TasksTable, JoinType.LEFT, TaskStatusConfigs.key, TasksTable.status)
            .slice(TaskStatusConfigs.key, TaskStatusConfigs.label, TasksTable.id.count())
            .selectAll()
            .groupBy(TaskStatusConfigs.key, TaskStatusConfigs.label)
            .orderBy(TaskStatusConfigs.order to SortOrder.ASC)
            .map { row ->
                val count = row[TasksTable.id.count()].toInt()
                TaskStatusStatistics(
                    statusKey = row[TaskStatusConfigs.key],
                    statusLabel = row[TaskStatusConfigs.label],
                    count = count,
                    percentage = (count / totalTasks) * 100
                )
            }
    }

    // 检查状态是否被任务使用
    fun isStatusInUse(key: String): Boolean = transaction {
        TasksTable.select { TasksTable.status eq key }.count() > 0
    }

    // 获取状态的使用次数
    fun getStatusUsageCount(key: String): Long = transaction {
        TasksTable.select { TasksTable.status eq key }.count()
    }
}
