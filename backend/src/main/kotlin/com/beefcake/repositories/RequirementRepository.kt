package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.Requirement
import com.beefcake.models.Task
import com.beefcake.models.User
import com.beefcake.models.Project
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class RequirementRepository {
    
    suspend fun create(
        title: String,
        businessDescription: String,
        acceptanceCriteria: String?,
        projectId: Long,
        priorityImportance: Priority,
        priorityUrgency: Priority,
        estimatedValue: String?,
        targetUsers: String?,
        businessGoal: String?,
        creatorId: Long,
        assigneeId: Long?,
        expectedDeliveryDate: LocalDateTime?
    ): Requirement? = dbQuery {
        val insertStatement = Requirements.insert {
            it[Requirements.title] = title
            it[Requirements.businessDescription] = businessDescription
            it[Requirements.acceptanceCriteria] = acceptanceCriteria
            it[Requirements.projectId] = projectId
            it[Requirements.priorityImportance] = priorityImportance
            it[Requirements.priorityUrgency] = priorityUrgency
            it[Requirements.estimatedValue] = estimatedValue
            it[Requirements.targetUsers] = targetUsers
            it[Requirements.businessGoal] = businessGoal
            it[Requirements.creatorId] = creatorId
            it[Requirements.assigneeId] = assigneeId
            it[Requirements.expectedDeliveryDate] = expectedDeliveryDate
            it[Requirements.createdAt] = LocalDateTime.now()
            it[Requirements.updatedAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToRequirement(it) }
    }
    
    suspend fun findById(id: Long): Requirement? = dbQuery {
        Requirements.selectAll().where { Requirements.id eq id }
            .map { row ->
                val requirement = resultRowToRequirement(row)
                // 加载关联信息
                val creator = loadUserById(requirement.creatorId)
                val assignee = requirement.assigneeId?.let { loadUserById(it) }
                val project = loadProjectById(requirement.projectId)
                val tasks = loadTasksForRequirement(requirement.id)

                requirement.copy(
                    creator = creator,
                    assignee = assignee,
                    project = project,
                    tasks = tasks
                )
            }
            .singleOrNull()
    }
    
    suspend fun findAll(
        status: String? = null, // 动态状态，对应状态配置的 key
        assigneeId: Long? = null,
        creatorId: Long? = null,
        search: String? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): Pair<List<Requirement>, Int> = dbQuery {
        var query = Requirements.selectAll()

        // 排除归档状态的需求
        query = query.andWhere { Requirements.status neq "ARCHIVED" }

        // 添加过滤条件
        if (status != null) {
            query = query.andWhere { Requirements.status eq status }
        }
        if (assigneeId != null) {
            query = query.andWhere { Requirements.assigneeId eq assigneeId }
        }
        if (creatorId != null) {
            query = query.andWhere { Requirements.creatorId eq creatorId }
        }
        if (search != null && search.isNotBlank()) {
            query = query.andWhere { Requirements.title like "%$search%" }
        }

        // 计算总数
        val total = query.count().toInt()

        // 添加分页和排序
        val requirements = query
            .orderBy(Requirements.createdAt, SortOrder.DESC)
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .map { row ->
                val requirement = resultRowToRequirement(row)
                // 加载关联信息
                val creator = loadUserById(requirement.creatorId)
                val assignee = requirement.assigneeId?.let { loadUserById(it) }
                val project = loadProjectById(requirement.projectId)
                val tasks = loadTasksForRequirement(requirement.id)

                requirement.copy(
                    creator = creator,
                    assignee = assignee,
                    project = project,
                    tasks = tasks
                )
            }

        requirements to total
    }

    // 查找有任务的需求
    suspend fun findRequirementsWithTasks(
        status: String? = null,
        assigneeId: Long? = null,
        creatorId: Long? = null,
        search: String? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): Pair<List<Requirement>, Int> = dbQuery {
        // 使用 JOIN 查询只获取有任务的需求
        var query = Requirements
            .join(Tasks, JoinType.INNER, Requirements.id, Tasks.requirementId)
            .slice(Requirements.columns)
            .selectAll()
            .groupBy(*Requirements.columns.toTypedArray()) // 去重

        // 排除归档状态的需求
        query = query.andWhere { Requirements.status neq "ARCHIVED" }

        // 添加过滤条件
        if (status != null) {
            query = query.andWhere { Requirements.status eq status }
        }
        if (assigneeId != null) {
            query = query.andWhere { Requirements.assigneeId eq assigneeId }
        }
        if (creatorId != null) {
            query = query.andWhere { Requirements.creatorId eq creatorId }
        }
        if (search != null && search.isNotBlank()) {
            query = query.andWhere { Requirements.title like "%$search%" }
        }

        // 计算总数
        val total = query.count().toInt()

        // 添加分页和排序
        val requirements = query
            .orderBy(Requirements.createdAt, SortOrder.DESC)
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .map { row ->
                val requirement = resultRowToRequirement(row)
                // 加载关联信息
                val creator = loadUserById(requirement.creatorId)
                val assignee = requirement.assigneeId?.let { loadUserById(it) }
                val project = loadProjectById(requirement.projectId)
                val tasks = loadTasksForRequirement(requirement.id)

                requirement.copy(
                    creator = creator,
                    assignee = assignee,
                    project = project,
                    tasks = tasks
                )
            }

        requirements to total
    }

    // 查找已归档的需求
    suspend fun findArchivedRequirements(
        assigneeId: Long? = null,
        creatorId: Long? = null,
        search: String? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): Pair<List<Requirement>, Int> = dbQuery {
        var query = Requirements.selectAll()

        // 只查询归档状态的需求
        query = query.andWhere { Requirements.status eq "ARCHIVED" }

        // 添加过滤条件
        if (assigneeId != null) {
            query = query.andWhere { Requirements.assigneeId eq assigneeId }
        }
        if (creatorId != null) {
            query = query.andWhere { Requirements.creatorId eq creatorId }
        }
        if (search != null && search.isNotBlank()) {
            query = query.andWhere { Requirements.title like "%$search%" }
        }

        // 计算总数
        val total = query.count().toInt()

        // 添加分页和排序
        val requirements = query
            .orderBy(Requirements.updatedAt, SortOrder.DESC) // 按更新时间倒序
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .map { row ->
                val requirement = resultRowToRequirement(row)
                // 加载关联信息
                val creator = loadUserById(requirement.creatorId)
                val assignee = requirement.assigneeId?.let { loadUserById(it) }
                val project = loadProjectById(requirement.projectId)
                val tasks = loadTasksForRequirement(requirement.id)

                requirement.copy(
                    creator = creator,
                    assignee = assignee,
                    project = project,
                    tasks = tasks
                )
            }

        requirements to total
    }

    suspend fun update(
        id: Long,
        title: String?,
        businessDescription: String?,
        acceptanceCriteria: String?,
        projectId: Long?,
        status: String?, // 动态状态，对应状态配置的 key
        priorityImportance: Priority?,
        priorityUrgency: Priority?,
        estimatedValue: String?,
        targetUsers: String?,
        businessGoal: String?,
        assigneeId: Long?,
        expectedDeliveryDate: LocalDateTime?,
        actualDeliveryDate: LocalDateTime?
    ): Boolean = dbQuery {
        Requirements.update({ Requirements.id eq id }) {
            if (title != null) it[Requirements.title] = title
            if (businessDescription != null) it[Requirements.businessDescription] = businessDescription
            if (acceptanceCriteria != null) it[Requirements.acceptanceCriteria] = acceptanceCriteria
            if (projectId != null) it[Requirements.projectId] = projectId
            if (status != null) it[Requirements.status] = status
            if (priorityImportance != null) it[Requirements.priorityImportance] = priorityImportance
            if (priorityUrgency != null) it[Requirements.priorityUrgency] = priorityUrgency
            if (estimatedValue != null) it[Requirements.estimatedValue] = estimatedValue
            if (targetUsers != null) it[Requirements.targetUsers] = targetUsers
            if (businessGoal != null) it[Requirements.businessGoal] = businessGoal
            if (assigneeId != null) it[Requirements.assigneeId] = assigneeId
            if (expectedDeliveryDate != null) it[Requirements.expectedDeliveryDate] = expectedDeliveryDate
            if (actualDeliveryDate != null) it[Requirements.actualDeliveryDate] = actualDeliveryDate
            it[Requirements.updatedAt] = LocalDateTime.now()
        } > 0
    }
    
    suspend fun updateStatus(id: Long, status: String): Boolean = dbQuery {
        Requirements.update({ Requirements.id eq id }) {
            it[Requirements.status] = status
            it[Requirements.updatedAt] = LocalDateTime.now()

            // 如果状态是已交付，设置实际交付时间
            if (status == "DELIVERED") {
                it[Requirements.actualDeliveryDate] = LocalDateTime.now()
            }
        } > 0
    }
    
    suspend fun delete(id: Long): Boolean = dbQuery {
        Requirements.deleteWhere { Requirements.id eq id } > 0
    }
    
    suspend fun findByStatus(status: String): List<Requirement> = dbQuery {
        Requirements.selectAll().where { Requirements.status eq status }
            .orderBy(Requirements.createdAt, SortOrder.DESC)
            .map { row ->
                val requirement = resultRowToRequirement(row)
                // 加载关联信息
                val creator = loadUserById(requirement.creatorId)
                val assignee = requirement.assigneeId?.let { loadUserById(it) }
                val project = loadProjectById(requirement.projectId)
                val tasks = loadTasksForRequirement(requirement.id)

                requirement.copy(
                    creator = creator,
                    assignee = assignee,
                    project = project,
                    tasks = tasks
                )
            }
    }
    
    suspend fun getRequirementStatistics(): Map<String, Any> = dbQuery {
        val total = Requirements.selectAll().count().toInt()
        val delivered = Requirements.selectAll().where { Requirements.status eq "DELIVERED" }.count().toInt()
        val inProgress = Requirements.selectAll().where {
            Requirements.status inList listOf(
                "APPROVED",
                "IN_PROGRESS",
                "TESTING"
            )
        }.count().toInt()

        val overdue = Requirements.selectAll().where {
            (Requirements.expectedDeliveryDate less LocalDateTime.now()) and
            (Requirements.status neq "DELIVERED")
        }.count().toInt()
        
        mapOf(
            "totalRequirements" to total,
            "deliveredRequirements" to delivered,
            "inProgressRequirements" to inProgress,
            "overdueRequirements" to overdue
        )
    }
    
    private fun resultRowToRequirement(row: ResultRow): Requirement {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return Requirement(
            id = row[Requirements.id].value,
            title = row[Requirements.title],
            businessDescription = row[Requirements.businessDescription],
            acceptanceCriteria = row[Requirements.acceptanceCriteria],
            projectId = row[Requirements.projectId],
            status = row[Requirements.status],
            priorityImportance = row[Requirements.priorityImportance],
            priorityUrgency = row[Requirements.priorityUrgency],
            estimatedValue = row[Requirements.estimatedValue],
            targetUsers = row[Requirements.targetUsers],
            businessGoal = row[Requirements.businessGoal],
            creatorId = row[Requirements.creatorId],
            assigneeId = row[Requirements.assigneeId],
            expectedDeliveryDate = row[Requirements.expectedDeliveryDate]?.format(formatter),
            actualDeliveryDate = row[Requirements.actualDeliveryDate]?.format(formatter),
            createdAt = row[Requirements.createdAt].format(formatter),
            updatedAt = row[Requirements.updatedAt].format(formatter),
            creator = null, // 需要时单独查询
            assignee = null, // 需要时单独查询
            project = null, // 需要时单独查询
            tasks = null // 需要时单独查询
        )
    }

    /**
     * 加载用户信息
     */
    private suspend fun loadUserById(userId: Long): User? = dbQuery {
        Users.selectAll().where { Users.id eq userId }
            .singleOrNull()
            ?.let { row ->
                User(
                    id = row[Users.id].value,
                    username = row[Users.username],
                    nickname = row[Users.nickname],
                    avatarUrl = row[Users.avatarUrl],
                    userType = row[Users.userType],
                    status = row[Users.status],
                    failedLoginCount = row[Users.failedLoginCount],
                    lockedUntil = row[Users.lockedUntil]?.toString(),
                    createdAt = row[Users.createdAt].toString(),
                    updatedAt = row[Users.updatedAt].toString()
                )
            }
    }

    /**
     * 加载项目信息
     */
    private suspend fun loadProjectById(projectId: Long): Project? = dbQuery {
        Projects.selectAll().where { Projects.id eq projectId }
            .singleOrNull()
            ?.let { row ->
                Project(
                    id = row[Projects.id].value,
                    name = row[Projects.name],
                    description = row[Projects.description],
                    createdBy = row[Projects.createdBy],
                    createdAt = row[Projects.createdAt].toString(),
                    updatedAt = row[Projects.updatedAt].toString(),
                    creator = null, // 避免循环查询
                    requirementCount = 0 // 这里不计算，避免性能问题
                )
            }
    }

    /**
     * 加载需求关联的任务信息
     */
    private suspend fun loadTasksForRequirement(requirementId: Long): List<Task> = dbQuery {
        Tasks.selectAll().where { Tasks.requirementId eq requirementId }
            .orderBy(Tasks.createdAt, SortOrder.ASC)
            .map { row ->
                val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
                Task(
                    id = row[Tasks.id].value,
                    title = row[Tasks.title],
                    description = row[Tasks.description],
                    requirementId = row[Tasks.requirementId],
                    taskType = row[Tasks.taskType],
                    status = row[Tasks.status],
                    priorityImportance = row[Tasks.priorityImportance],
                    priorityUrgency = row[Tasks.priorityUrgency],
                    estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
                    actualHours = row[Tasks.actualHours]?.toDouble(),
                    creatorId = row[Tasks.creatorId],
                    assigneeId = row[Tasks.assigneeId],
                    dueDate = row[Tasks.dueDate]?.format(formatter),
                    startedAt = row[Tasks.startedAt]?.format(formatter),
                    completedAt = row[Tasks.completedAt]?.format(formatter),
                    createdAt = row[Tasks.createdAt].format(formatter),
                    updatedAt = row[Tasks.updatedAt].format(formatter),
                    creator = null,
                    assignee = null,
                    requirement = null,
                    dependencies = null,
                    dependents = null,
                    canStart = true
                )
            }
    }
}
