package com.beefcake.services

import com.beefcake.models.*
import com.beefcake.repositories.TaskStatusConfigRepository

class TaskStatusConfigService(
    private val taskStatusConfigRepository: TaskStatusConfigRepository
) {

    // 获取所有任务状态配置
    fun getAllTaskStatusConfigs(): List<TaskStatusConfig> {
        return taskStatusConfigRepository.findAll()
    }

    // 获取激活的任务状态配置
    fun getActiveTaskStatusConfigs(): List<TaskStatusConfig> {
        return taskStatusConfigRepository.findAllActive()
    }

    // 根据 key 获取状态配置
    fun getTaskStatusConfigByKey(key: String): TaskStatusConfig? {
        return taskStatusConfigRepository.findByKey(key)
    }

    // 创建任务状态配置
    fun createTaskStatusConfig(request: CreateTaskStatusConfigRequest): TaskStatusConfig {
        // 检查 key 是否已存在
        val existing = taskStatusConfigRepository.findByKey(request.key)
        if (existing != null) {
            throw IllegalArgumentException("状态键 '${request.key}' 已存在")
        }

        // 验证允许转换的状态是否存在
        request.allowedTransitions.forEach { transitionKey ->
            if (taskStatusConfigRepository.findByKey(transitionKey) == null) {
                throw IllegalArgumentException("转换状态 '$transitionKey' 不存在")
            }
        }

        return taskStatusConfigRepository.create(request)
    }

    // 更新任务状态配置
    fun updateTaskStatusConfig(key: String, request: UpdateTaskStatusConfigRequest): TaskStatusConfig {
        val existing = taskStatusConfigRepository.findByKey(key)
            ?: throw IllegalArgumentException("状态键 '$key' 不存在")

        // 验证允许转换的状态是否存在
        request.allowedTransitions?.forEach { transitionKey ->
            if (taskStatusConfigRepository.findByKey(transitionKey) == null) {
                throw IllegalArgumentException("转换状态 '$transitionKey' 不存在")
            }
        }

        return taskStatusConfigRepository.update(key, request)
            ?: throw IllegalArgumentException("更新失败")
    }

    // 删除任务状态配置
    fun deleteTaskStatusConfig(key: String) {
        val existing = taskStatusConfigRepository.findByKey(key)
            ?: throw IllegalArgumentException("状态键 '$key' 不存在")

        // 检查是否有任务正在使用此状态
        if (taskStatusConfigRepository.isStatusInUse(key)) {
            val usageCount = taskStatusConfigRepository.getStatusUsageCount(key)
            throw IllegalArgumentException("无法删除状态 '${existing.label}'，有 $usageCount 个任务正在使用此状态")
        }

        val deleted = taskStatusConfigRepository.delete(key)
        if (!deleted) {
            throw IllegalArgumentException("删除失败")
        }
    }

    // 批量更新任务状态配置
    fun batchUpdateTaskStatusConfigs(request: BatchUpdateTaskStatusConfigsRequest): List<TaskStatusConfig> {
        // 验证所有配置的 key 都存在
        request.configs.forEach { config ->
            val existing = taskStatusConfigRepository.findByKey(config.key)
                ?: throw IllegalArgumentException("状态键 '${config.key}' 不存在")
        }

        // 验证所有允许转换的状态都存在
        request.configs.forEach { config ->
            config.allowedTransitions.forEach { transitionKey ->
                if (taskStatusConfigRepository.findByKey(transitionKey) == null) {
                    throw IllegalArgumentException("转换状态 '$transitionKey' 不存在")
                }
            }
        }

        return taskStatusConfigRepository.batchUpdate(request)
    }

    // 验证状态转换
    fun validateStatusTransition(request: TaskStatusTransitionValidationRequest): TaskStatusTransitionValidationResponse {
        return taskStatusConfigRepository.validateTransition(request.fromStatus, request.toStatus)
    }

    // 获取任务状态统计
    fun getTaskStatusStatistics(): List<TaskStatusStatistics> {
        return taskStatusConfigRepository.getStatusStatistics()
    }

    // 检查状态是否被使用
    fun isStatusInUse(key: String): Boolean {
        return taskStatusConfigRepository.isStatusInUse(key)
    }

    // 获取状态使用次数
    fun getStatusUsageCount(key: String): Long {
        return taskStatusConfigRepository.getStatusUsageCount(key)
    }

    // 获取状态转换图（用于可视化）
    fun getStatusTransitionGraph(): Map<String, List<String>> {
        val configs = taskStatusConfigRepository.findAllActive()
        return configs.associate { config ->
            config.key to config.allowedTransitions
        }
    }

    // 获取可以转换到指定状态的所有状态
    fun getStatusesCanTransitionTo(targetStatus: String): List<TaskStatusConfig> {
        val allConfigs = taskStatusConfigRepository.findAllActive()
        return allConfigs.filter { config ->
            targetStatus in config.allowedTransitions
        }
    }

    // 获取指定状态可以转换到的所有状态
    fun getStatusesCanTransitionFrom(sourceStatus: String): List<TaskStatusConfig> {
        val sourceConfig = taskStatusConfigRepository.findByKey(sourceStatus)
            ?: return emptyList()
        
        return sourceConfig.allowedTransitions.mapNotNull { transitionKey ->
            taskStatusConfigRepository.findByKey(transitionKey)
        }
    }

    // 重置为默认状态配置
    fun resetToDefaultConfigs(): List<TaskStatusConfig> {
        // 删除所有现有配置
        val existingConfigs = taskStatusConfigRepository.findAll()
        existingConfigs.forEach { config ->
            if (!taskStatusConfigRepository.isStatusInUse(config.key)) {
                taskStatusConfigRepository.delete(config.key)
            }
        }

        // 创建默认配置
        val defaultConfigs = listOf(
            CreateTaskStatusConfigRequest(
                key = "TODO",
                label = "待开始",
                color = "#d9d9d9",
                icon = "📋",
                description = "任务待开始，准备阶段",
                order = 1,
                isActive = true,
                allowedTransitions = listOf("IN_PROGRESS", "BLOCKED")
            ),
            CreateTaskStatusConfigRequest(
                key = "IN_PROGRESS",
                label = "进行中",
                color = "#1890ff",
                icon = "🔄",
                description = "任务正在进行中",
                order = 2,
                isActive = true,
                allowedTransitions = listOf("REVIEW", "TESTING", "BLOCKED", "TODO")
            ),
            CreateTaskStatusConfigRequest(
                key = "BLOCKED",
                label = "阻塞",
                color = "#ff7875",
                icon = "🚫",
                description = "任务被阻塞，等待依赖",
                order = 3,
                isActive = true,
                allowedTransitions = listOf("TODO", "IN_PROGRESS")
            ),
            CreateTaskStatusConfigRequest(
                key = "REVIEW",
                label = "评审中",
                color = "#faad14",
                icon = "👁️",
                description = "任务代码评审阶段",
                order = 4,
                isActive = true,
                allowedTransitions = listOf("IN_PROGRESS", "TESTING", "DONE")
            ),
            CreateTaskStatusConfigRequest(
                key = "TESTING",
                label = "测试中",
                color = "#722ed1",
                icon = "🧪",
                description = "任务功能测试阶段",
                order = 5,
                isActive = true,
                allowedTransitions = listOf("REVIEW", "DONE", "IN_PROGRESS")
            ),
            CreateTaskStatusConfigRequest(
                key = "DONE",
                label = "已完成",
                color = "#52c41a",
                icon = "✅",
                description = "任务已完成",
                order = 6,
                isActive = true,
                allowedTransitions = listOf("TESTING", "REVIEW")
            )
        )

        return defaultConfigs.map { config ->
            val existing = taskStatusConfigRepository.findByKey(config.key)
            existing ?: taskStatusConfigRepository.create(config)
        }
    }
}
