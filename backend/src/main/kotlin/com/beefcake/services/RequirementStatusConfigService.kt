package com.beefcake.services

import com.beefcake.models.*
import com.beefcake.repositories.RequirementRepository
import com.beefcake.database.tables.Requirements
import kotlinx.serialization.json.Json
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.builtins.serializer
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime

class RequirementStatusConfigService(
    private val requirementRepository: RequirementRepository
) {

    // 获取激活的状态配置
    suspend fun getActiveStatusConfigs(): List<RequirementStatusConfig> = transaction {
        RequirementStatusConfigEntity.find { RequirementStatusConfigs.isActive eq true }
            .orderBy(RequirementStatusConfigs.order to SortOrder.ASC)
            .map { it.toRequirementStatusConfig() }
    }

    // 获取所有状态配置
    suspend fun getAllStatusConfigs(): List<RequirementStatusConfig> = transaction {
        RequirementStatusConfigEntity.all()
            .orderBy(RequirementStatusConfigs.order to SortOrder.ASC)
            .map { it.toRequirementStatusConfig() }
    }

    // 根据 key 获取状态配置
    suspend fun getStatusConfigByKey(key: String): RequirementStatusConfig? = transaction {
        RequirementStatusConfigEntity.find { RequirementStatusConfigs.key eq key }
            .firstOrNull()?.toRequirementStatusConfig()
    }

    // 创建状态配置
    suspend fun createStatusConfig(
        request: CreateRequirementStatusConfigRequest,
        userId: Int
    ): RequirementStatusConfig = transaction {
        // 检查 key 是否已存在
        val existing = RequirementStatusConfigEntity.find { RequirementStatusConfigs.key eq request.key }.firstOrNull()
        if (existing != null) {
            throw IllegalArgumentException("状态配置 key '${request.key}' 已存在")
        }

        val entity = RequirementStatusConfigEntity.new {
            key = request.key
            label = request.label
            color = request.color
            icon = request.icon
            description = request.description
            order = request.order
            isActive = request.isActive
            allowedTransitions = Json.encodeToString(ListSerializer(String.serializer()), request.allowedTransitions)
            createdAt = LocalDateTime.now()
            updatedAt = LocalDateTime.now()
            createdBy = userId
            updatedBy = userId
        }
        entity.toRequirementStatusConfig()
    }

    // 更新状态配置
    suspend fun updateStatusConfig(
        key: String,
        request: UpdateRequirementStatusConfigRequest,
        userId: Int
    ): RequirementStatusConfig = transaction {
        val entity = RequirementStatusConfigEntity.find { RequirementStatusConfigs.key eq key }
            .firstOrNull() ?: throw IllegalArgumentException("状态配置不存在: $key")

        request.label?.let { entity.label = it }
        request.color?.let { entity.color = it }
        request.icon?.let { entity.icon = it }
        request.description?.let { entity.description = it }
        request.order?.let { entity.order = it }
        request.isActive?.let { entity.isActive = it }
        request.allowedTransitions?.let {
            entity.allowedTransitions = Json.encodeToString(ListSerializer(String.serializer()), it)
        }
        entity.updatedAt = LocalDateTime.now()
        entity.updatedBy = userId

        entity.toRequirementStatusConfig()
    }

    // 删除状态配置
    suspend fun deleteStatusConfig(key: String): Boolean = transaction {
        // 检查是否有需求使用此状态
        val requirementsUsingStatus = Requirements.select { Requirements.status eq key }.count()
        if (requirementsUsingStatus > 0) {
            throw IllegalStateException("无法删除状态配置 '$key'，还有 $requirementsUsingStatus 个需求正在使用此状态")
        }

        val entity = RequirementStatusConfigEntity.find { RequirementStatusConfigs.key eq key }
            .firstOrNull() ?: return@transaction false

        entity.delete()
        true
    }

}
