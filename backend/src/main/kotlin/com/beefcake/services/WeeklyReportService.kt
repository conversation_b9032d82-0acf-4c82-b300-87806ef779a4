package com.beefcake.services

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.*
import com.beefcake.repositories.RequirementRepository
import com.beefcake.repositories.TaskRepository
import com.beefcake.repositories.UserRepository
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class WeeklyReportService(
    private val requirementRepository: RequirementRepository,
    private val taskRepository: TaskRepository,
    private val userRepository: UserRepository,
    private val statusConfigService: RequirementStatusConfigService
) {

    companion object {
        private const val ARCHIVED_STATUS = "ARCHIVED"
    }

    suspend fun generateWeeklyReport(request: WeeklyReportRequest): WeeklyReport = dbQuery {
        // 确定日期范围
        val (startDate, endDate) = determineDateRange(request.startDate, request.endDate)
        val weekRange = "${startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))} ~ ${endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))}"

        // 获取状态配置
        val statusConfigs = statusConfigService.getAllStatusConfigs()
        val statusConfigMap = statusConfigs.associateBy { it.key }

        // 构建查询条件
        val requirementQuery = Requirements.selectAll()

        // 排除归档状态（除非明确要求包含）
        if (!request.includeArchived) {
            requirementQuery.andWhere { Requirements.status neq ARCHIVED_STATUS }
        }

        // 按项目筛选
        request.projectIds?.let { projectIds ->
            if (projectIds.isNotEmpty()) {
                requirementQuery.andWhere { Requirements.projectId inList projectIds }
            }
        }

        // 按负责人筛选
        request.assigneeIds?.let { assigneeIds ->
            if (assigneeIds.isNotEmpty()) {
                requirementQuery.andWhere { Requirements.assigneeId inList assigneeIds }
            }
        }

        // 执行查询并转换为周报项
        val requirements = requirementQuery.map { row ->
            val requirementId = row[Requirements.id].value
            val projectId = row[Requirements.projectId]
            val assigneeId = row[Requirements.assigneeId]

            // 获取项目信息
            val project = dbQuery {
                Projects.selectAll().where { Projects.id eq projectId }
                    .singleOrNull()?.let { projectRow ->
                        ProjectInfo(
                            id = projectRow[Projects.id].value,
                            name = projectRow[Projects.name],
                            description = projectRow[Projects.description]
                        )
                    }
            } ?: ProjectInfo(projectId, "未知项目", null)

            // 获取负责人信息
            val assignee = assigneeId?.let { userRepository.findById(it) }

            // 获取关联任务
            val tasks = getRequirementTasks(requirementId, statusConfigMap)

            // 获取状态配置
            val status = row[Requirements.status]
            val statusConfig = statusConfigMap[status]

            WeeklyRequirementItem(
                id = requirementId,
                title = row[Requirements.title],
                description = row[Requirements.businessDescription],
                status = status,
                statusLabel = statusConfig?.label ?: status,
                statusColor = statusConfig?.color ?: "#d9d9d9",
                project = project,
                assignee = assignee,
                tasks = tasks,
                createdAt = row[Requirements.createdAt].toString(),
                updatedAt = row[Requirements.updatedAt].toString()
            )
        }

        // 生成摘要
        val summary = generateSummary(requirements, statusConfigMap)

        WeeklyReport(
            requirements = requirements,
            summary = summary,
            generatedAt = LocalDate.now().toString(),
            weekRange = weekRange
        )
    }

    private suspend fun getRequirementTasks(
        requirementId: Long,
        statusConfigMap: Map<String, RequirementStatusConfig>
    ): List<WeeklyTaskItem> = dbQuery {
        Tasks.selectAll().where { Tasks.requirementId eq requirementId }
            .map { row ->
                val assigneeId = row[Tasks.assigneeId]
                val assignee = assigneeId?.let { userRepository.findById(it) }
                val status = row[Tasks.status].toString()
                // 任务状态映射
                val statusLabel = when(status) {
                    "TODO" -> "待开始"
                    "IN_PROGRESS" -> "进行中"
                    "BLOCKED" -> "阻塞"
                    "REVIEW" -> "评审中"
                    "TESTING" -> "测试中"
                    "DONE" -> "已完成"
                    else -> status
                }
                val statusColor = when(status) {
                    "TODO" -> "#d9d9d9"
                    "IN_PROGRESS" -> "#1890ff"
                    "BLOCKED" -> "#ff7875"
                    "REVIEW" -> "#faad14"
                    "TESTING" -> "#722ed1"
                    "DONE" -> "#52c41a"
                    else -> "#d9d9d9"
                }

                WeeklyTaskItem(
                    id = row[Tasks.id].value,
                    title = row[Tasks.title],
                    status = status,
                    statusLabel = statusLabel,
                    statusColor = statusColor,
                    assignee = assignee,
                    estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
                    actualHours = row[Tasks.actualHours]?.toDouble(),
                    dueDate = row[Tasks.dueDate]?.toString()
                )
            }
    }

    private fun generateSummary(
        requirements: List<WeeklyRequirementItem>,
        statusConfigMap: Map<String, RequirementStatusConfig>
    ): WeeklyReportSummary {
        val requirementsByStatus = requirements.groupingBy { it.status }.eachCount()
        val allTasks = requirements.flatMap { it.tasks }
        val tasksByStatus = allTasks.groupingBy { it.status }.eachCount()
        val activeProjects = requirements.map { it.project.id }.distinct().size
        val completedRequirements = requirements.count { it.status == "DELIVERED" }
        val completedTasks = allTasks.count { it.status == "COMPLETED" }

        return WeeklyReportSummary(
            totalRequirements = requirements.size,
            requirementsByStatus = requirementsByStatus,
            totalTasks = allTasks.size,
            tasksByStatus = tasksByStatus,
            activeProjects = activeProjects,
            completedRequirements = completedRequirements,
            completedTasks = completedTasks
        )
    }

    private fun determineDateRange(startDate: String?, endDate: String?): Pair<LocalDate, LocalDate> {
        return if (startDate != null && endDate != null) {
            Pair(LocalDate.parse(startDate), LocalDate.parse(endDate))
        } else {
            // 默认为本周（周一到周日）
            val today = LocalDate.now()
            val monday = today.minusDays(today.dayOfWeek.value - 1L)
            val sunday = monday.plusDays(6)
            Pair(monday, sunday)
        }
    }
}
