package com.beefcake.services

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDate
import java.time.DayOfWeek
import java.time.temporal.TemporalAdjusters

class WeeklyTaskService {

    // 获取本周任务
    suspend fun getWeeklyTasks(userId: Long?, isAdmin: Boolean = false): List<Task> = dbQuery {
        // 计算本周的开始和结束日期
        val today = LocalDate.now()
        val weekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atStartOfDay()
        val weekEnd = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59)

        var query = Tasks
            .leftJoin(Users, { Tasks.assigneeId }, { Users.id })
            .leftJoin(Requirements, { Tasks.requirementId }, { Requirements.id })
            .selectAll()
            .where {
                Tasks.dueDate.isNotNull() and
                (Tasks.dueDate greaterEq weekStart) and
                (Tasks.dueDate lessEq weekEnd) and
                (Requirements.status neq "ARCHIVED")
            }

        // 根据用户权限过滤
        if (!isAdmin && userId != null) {
            query = query.andWhere { Tasks.assigneeId eq userId }
        }

        val tasks = query
            .orderBy(Tasks.dueDate, SortOrder.ASC)
            .map { row ->
                val task = resultRowToTask(row)
                // 加载依赖任务信息
                val dependencies = getTaskDependencies(task.id)
                task.copy(dependencies = dependencies)
            }

        tasks
    }

    // 获取指定日期范围内的任务
    suspend fun getWeeklyTasksByDateRange(userId: Long?, isAdmin: Boolean = false, startDate: String, endDate: String): List<Task> = dbQuery {
        // 解析日期字符串
        val start = LocalDate.parse(startDate).atStartOfDay()
        val end = LocalDate.parse(endDate).atTime(23, 59, 59)

        var query = Tasks
            .leftJoin(Users, { Tasks.assigneeId }, { Users.id })
            .leftJoin(Requirements, { Tasks.requirementId }, { Requirements.id })
            .selectAll()
            .where {
                Tasks.dueDate.isNotNull() and
                (Tasks.dueDate greaterEq start) and
                (Tasks.dueDate lessEq end)
            }

        // 根据用户权限过滤
        if (!isAdmin && userId != null) {
            query = query.andWhere { Tasks.assigneeId eq userId }
        }

        val tasks = query
            .orderBy(Tasks.dueDate, SortOrder.ASC)
            .map { row ->
                val task = resultRowToTask(row)
                // 加载依赖任务信息
                val dependencies = getTaskDependencies(task.id)
                task.copy(dependencies = dependencies)
            }

        tasks
    }

    // 获取任务的依赖关系
    private suspend fun getTaskDependencies(taskId: Long): List<TaskDependency> = dbQuery {
        TaskDependencies
            .leftJoin(Tasks, { dependsOnTaskId }, { Tasks.id })
            .leftJoin(Users, { Tasks.assigneeId }, { Users.id })
            .selectAll()
            .where { TaskDependencies.taskId eq taskId }
            .map { row ->
                TaskDependency(
                    id = row[TaskDependencies.id].value,
                    taskId = row[TaskDependencies.taskId],
                    dependsOnTaskId = row[TaskDependencies.dependsOnTaskId],
                    dependencyType = row[TaskDependencies.dependencyType].toString(),
                    dependsOnTask = if (row.hasValue(Tasks.id) && row[Tasks.id] != null) {
                        Task(
                            id = row[Tasks.id].value,
                            title = row[Tasks.title],
                            description = row[Tasks.description],
                            requirementId = row[Tasks.requirementId],
                            taskType = row[Tasks.taskType],
                            status = row[Tasks.status],
                            priorityImportance = row[Tasks.priorityImportance],
                            priorityUrgency = row[Tasks.priorityUrgency],
                            estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
                            actualHours = row[Tasks.actualHours]?.toDouble(),
                            creatorId = row[Tasks.creatorId],
                            assigneeId = row[Tasks.assigneeId],
                            dueDate = row[Tasks.dueDate]?.toString(),
                            startedAt = row[Tasks.startedAt]?.toString(),
                            completedAt = row[Tasks.completedAt]?.toString(),
                            createdAt = row[Tasks.createdAt].toString(),
                            updatedAt = row[Tasks.updatedAt].toString(),
                            creator = null,
                            assignee = if (row.hasValue(Users.id) && row[Users.id] != null) {
                                User(
                                    id = row[Users.id].value,
                                    username = row[Users.username],
                                    nickname = row[Users.nickname],
                                    avatarUrl = row[Users.avatarUrl],
                                    userType = row[Users.userType],
                                    status = row[Users.status],
                                    failedLoginCount = row[Users.failedLoginCount],
                                    lockedUntil = row[Users.lockedUntil]?.toString(),
                                    createdAt = row[Users.createdAt].toString(),
                                    updatedAt = row[Users.updatedAt].toString()
                                )
                            } else null,
                            requirement = null,
                            dependencies = null,
                            dependents = null
                        )
                    } else null
                )
            }
    }

    // 转换 ResultRow 到 Task
    private fun resultRowToTask(row: ResultRow): Task {
        return Task(
            id = row[Tasks.id].value,
            title = row[Tasks.title],
            description = row[Tasks.description],
            requirementId = row[Tasks.requirementId],
            taskType = row[Tasks.taskType],
            status = row[Tasks.status],
            priorityImportance = row[Tasks.priorityImportance],
            priorityUrgency = row[Tasks.priorityUrgency],
            estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
            actualHours = row[Tasks.actualHours]?.toDouble(),
            creatorId = row[Tasks.creatorId],
            assigneeId = row[Tasks.assigneeId],
            dueDate = row[Tasks.dueDate]?.toString(),
            startedAt = row[Tasks.startedAt]?.toString(),
            completedAt = row[Tasks.completedAt]?.toString(),
            createdAt = row[Tasks.createdAt].toString(),
            updatedAt = row[Tasks.updatedAt].toString(),
            creator = null,
            assignee = if (row.hasValue(Users.id) && row[Users.id] != null) {
                User(
                    id = row[Users.id].value,
                    username = row[Users.username],
                    nickname = row[Users.nickname],
                    avatarUrl = row[Users.avatarUrl],
                    userType = row[Users.userType],
                    status = row[Users.status],
                    failedLoginCount = row[Users.failedLoginCount],
                    lockedUntil = row[Users.lockedUntil]?.toString(),
                    createdAt = row[Users.createdAt].toString(),
                    updatedAt = row[Users.updatedAt].toString()
                )
            } else null,
            requirement = if (row.hasValue(Requirements.id) && row[Requirements.id] != null) {
                Requirement(
                    id = row[Requirements.id].value,
                    title = row[Requirements.title],
                    businessDescription = row[Requirements.businessDescription],
                    acceptanceCriteria = row[Requirements.acceptanceCriteria],
                    projectId = row[Requirements.projectId],
                    status = row[Requirements.status],
                    priorityImportance = row[Requirements.priorityImportance],
                    priorityUrgency = row[Requirements.priorityUrgency],
                    estimatedValue = row[Requirements.estimatedValue],
                    targetUsers = row[Requirements.targetUsers],
                    businessGoal = row[Requirements.businessGoal],
                    creatorId = row[Requirements.creatorId],
                    assigneeId = row[Requirements.assigneeId],
                    expectedDeliveryDate = row[Requirements.expectedDeliveryDate]?.toString(),
                    actualDeliveryDate = row[Requirements.actualDeliveryDate]?.toString(),
                    createdAt = row[Requirements.createdAt].toString(),
                    updatedAt = row[Requirements.updatedAt].toString(),
                    creator = null,
                    assignee = null,
                    project = null,
                    tasks = null
                )
            } else null,
            dependencies = null, // 将在调用处填充
            dependents = null
        )
    }
}
