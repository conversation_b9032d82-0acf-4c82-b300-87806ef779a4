package com.beefcake.services

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.Project
import com.beefcake.models.User
import com.beefcake.models.ProjectOption
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime

class ProjectService {

    // 获取所有项目
    suspend fun getAllProjects(): List<Project> = dbQuery {
        Projects
            .leftJoin(Users, { createdBy }, { Users.id })
            .selectAll()
            .orderBy(Projects.createdAt, SortOrder.DESC)
            .map { row ->
                val projectId = row[Projects.id].value
                val requirementCount = getRequirementCount(projectId)

                Project(
                    id = projectId,
                    name = row[Projects.name],
                    description = row[Projects.description],
                    createdBy = row[Projects.createdBy],
                    createdAt = row[Projects.createdAt].toString(),
                    updatedAt = row[Projects.updatedAt].toString(),
                    creator = if (row.hasValue(Users.id) && row[Users.id] != null) {
                        User(
                            id = row[Users.id].value,
                            username = row[Users.username],
                            nickname = row[Users.nickname],
                            avatarUrl = row[Users.avatarUrl],
                            userType = row[Users.userType],
                            status = row[Users.status],
                            failedLoginCount = row[Users.failedLoginCount],
                            lockedUntil = row[Users.lockedUntil]?.toString(),
                            createdAt = row[Users.createdAt].toString(),
                            updatedAt = row[Users.updatedAt].toString()
                        )
                    } else null,
                    requirementCount = requirementCount
                )
            }
    }

    // 获取单个项目
    suspend fun getProject(id: Long): Project? = dbQuery {
        Projects
            .leftJoin(Users, { createdBy }, { Users.id })
            .selectAll()
            .where { Projects.id eq id }
            .map { row ->
                val projectId = row[Projects.id].value
                val requirementCount = getRequirementCount(projectId)

                Project(
                    id = projectId,
                    name = row[Projects.name],
                    description = row[Projects.description],
                    createdBy = row[Projects.createdBy],
                    createdAt = row[Projects.createdAt].toString(),
                    updatedAt = row[Projects.updatedAt].toString(),
                    creator = if (row.hasValue(Users.id) && row[Users.id] != null) {
                        User(
                            id = row[Users.id].value,
                            username = row[Users.username],
                            nickname = row[Users.nickname],
                            avatarUrl = row[Users.avatarUrl],
                            userType = row[Users.userType],
                            status = row[Users.status],
                            failedLoginCount = row[Users.failedLoginCount],
                            lockedUntil = row[Users.lockedUntil]?.toString(),
                            createdAt = row[Users.createdAt].toString(),
                            updatedAt = row[Users.updatedAt].toString()
                        )
                    } else null,
                    requirementCount = requirementCount
                )
            }
            .singleOrNull()
    }

    // 创建项目
    suspend fun createProject(
        name: String,
        description: String?,
        createdBy: Long
    ): Project = dbQuery {
        val now = LocalDateTime.now()
        val insertStatement = Projects.insert {
            it[Projects.name] = name
            it[Projects.description] = description
            it[Projects.createdBy] = createdBy
            it[Projects.createdAt] = now
            it[Projects.updatedAt] = now
        }

        val projectId = insertStatement[Projects.id].value

        // 查询创建者信息
        val creator = Users
            .selectAll()
            .where { Users.id eq createdBy }
            .singleOrNull()
            ?.let { userRow ->
                User(
                    id = userRow[Users.id].value,
                    username = userRow[Users.username],
                    nickname = userRow[Users.nickname],
                    avatarUrl = userRow[Users.avatarUrl],
                    userType = userRow[Users.userType],
                    status = userRow[Users.status],
                    failedLoginCount = userRow[Users.failedLoginCount],
                    lockedUntil = userRow[Users.lockedUntil]?.toString(),
                    createdAt = userRow[Users.createdAt].toString(),
                    updatedAt = userRow[Users.updatedAt].toString()
                )
            }

        // 直接构造返回对象，避免事务内查询问题
        Project(
            id = projectId,
            name = name,
            description = description,
            createdBy = createdBy,
            createdAt = now.toString(),
            updatedAt = now.toString(),
            creator = creator,
            requirementCount = 0 // 新建项目没有需求
        )
    }

    // 更新项目
    suspend fun updateProject(
        id: Long,
        name: String?,
        description: String?
    ): Project? = dbQuery {
        Projects.update({ Projects.id eq id }) {
            if (name != null) it[Projects.name] = name
            if (description != null) it[Projects.description] = description
            it[Projects.updatedAt] = LocalDateTime.now()
        }

        getProject(id)
    }

    // 删除项目
    suspend fun deleteProject(id: Long): Boolean = dbQuery {
        // 检查是否有关联的需求
        val requirementCount = getRequirementCount(id)
        if (requirementCount > 0) {
            return@dbQuery false // 有关联需求，不能删除
        }

        Projects.deleteWhere { Projects.id eq id } > 0
    }

    // 检查项目是否可以删除（没有关联需求）
    suspend fun canDeleteProject(id: Long): Boolean = dbQuery {
        getRequirementCount(id) == 0
    }

    // 获取项目下的需求数量
    private suspend fun getRequirementCount(projectId: Long): Int = dbQuery {
        try {
            Requirements
                .selectAll()
                .where { Requirements.projectId eq projectId }
                .count()
                .toInt()
        } catch (e: Exception) {
            // 如果 Requirements 表不存在或没有 projectId 字段，返回 0
            0
        }
    }

    // 获取项目选项列表（用于下拉选择）
    suspend fun getProjectOptions(): List<ProjectOption> = dbQuery {
        Projects
            .selectAll()
            .orderBy(Projects.name)
            .map { row ->
                ProjectOption(
                    id = row[Projects.id].value,
                    name = row[Projects.name]
                )
            }
    }
}


