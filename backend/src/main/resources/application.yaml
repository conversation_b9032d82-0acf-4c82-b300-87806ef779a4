ktor:
  application:
    modules:
      - com.beefcake.ApplicationKt.module
  deployment:
    port: 8081
    host: 0.0.0.0

database:
  host: coffee.local
  port: 3306
  name: coconut
  user: root
  password: xjy123456
  driver: com.mysql.cj.jdbc.Driver
  maxPoolSize: 20

jwt:
  secret: xjy2025
  issuer: beefcake-system
  audience: beefcake-users
  realm: beefcake
  expirationTime: 86400000 # 24 hours in milliseconds

app:
  environment: development
  uploadDir: ./uploads
  maxFileSize: 10485760 # 10MB
  allowedFileTypes: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]

security:
  maxLoginAttempts: 5
  lockoutDurationMinutes: 30
  passwordMinLength: 8
