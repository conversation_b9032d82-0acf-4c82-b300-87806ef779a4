-- 创建任务状态配置表
CREATE TABLE IF NOT EXISTS task_status_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(50) NOT NULL UNIQUE COMMENT '状态键',
    label VARCHAR(100) NOT NULL COMMENT '显示标签',
    color VARCHAR(20) NOT NULL COMMENT '颜色',
    icon VARCHAR(10) NOT NULL COMMENT '图标',
    description TEXT NOT NULL COMMENT '描述',
    `order` INT NOT NULL COMMENT '排序',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    allowed_transitions TEXT NOT NULL COMMENT '允许转换的状态（JSON格式）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NULL COMMENT '创建者ID',
    updated_by BIGINT NULL COMMENT '更新者ID',
    
    INDEX idx_key (`key`),
    INDEX idx_order (`order`),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) COMMENT='任务状态配置表';

-- 插入默认任务状态配置数据（如果不存在）
INSERT IGNORE INTO task_status_configs (`key`, label, color, icon, description, `order`, is_active, allowed_transitions) VALUES
('TODO', '待开始', '#d9d9d9', '📋', '任务待开始，准备阶段', 1, TRUE, '["IN_PROGRESS", "BLOCKED"]'),
('IN_PROGRESS', '进行中', '#1890ff', '🔄', '任务正在进行中', 2, TRUE, '["REVIEW", "TESTING", "BLOCKED", "TODO"]'),
('BLOCKED', '阻塞', '#ff7875', '🚫', '任务被阻塞，等待依赖', 3, TRUE, '["TODO", "IN_PROGRESS"]'),
('REVIEW', '评审中', '#faad14', '👁️', '任务代码评审阶段', 4, TRUE, '["IN_PROGRESS", "TESTING", "DONE"]'),
('TESTING', '测试中', '#722ed1', '🧪', '任务功能测试阶段', 5, TRUE, '["REVIEW", "DONE", "IN_PROGRESS"]'),
('DONE', '已完成', '#52c41a', '✅', '任务已完成', 6, TRUE, '["TESTING", "REVIEW"]');

-- 创建任务状态变更日志表
CREATE TABLE IF NOT EXISTS task_status_change_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    from_status VARCHAR(50) NOT NULL COMMENT '原状态',
    to_status VARCHAR(50) NOT NULL COMMENT '新状态',
    from_status_label VARCHAR(100) NOT NULL COMMENT '原状态标签',
    to_status_label VARCHAR(100) NOT NULL COMMENT '新状态标签',
    changed_by BIGINT NOT NULL COMMENT '变更人ID',
    changed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    reason TEXT NULL COMMENT '变更原因',
    metadata JSON NULL COMMENT '元数据',
    
    INDEX idx_task_id (task_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_changed_at (changed_at),
    INDEX idx_from_status (from_status),
    INDEX idx_to_status (to_status),
    
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='任务状态变更日志表';

-- 修改任务表的状态字段为字符串类型（支持动态状态配置）
ALTER TABLE tasks MODIFY COLUMN status VARCHAR(50) NOT NULL DEFAULT 'TODO' COMMENT '任务状态';

-- 更新现有任务的状态值（如果需要）
UPDATE tasks SET status = 'TODO' WHERE status = 'TODO';
UPDATE tasks SET status = 'IN_PROGRESS' WHERE status = 'IN_PROGRESS';
UPDATE tasks SET status = 'BLOCKED' WHERE status = 'BLOCKED';
UPDATE tasks SET status = 'REVIEW' WHERE status = 'REVIEW';
UPDATE tasks SET status = 'TESTING' WHERE status = 'TESTING';
UPDATE tasks SET status = 'DONE' WHERE status = 'DONE';
