-- 将需求状态从枚举类型改为字符串类型，支持动态状态配置

-- 1. 添加新的状态字段（临时）
ALTER TABLE requirements ADD COLUMN status_new VARCHAR(50) DEFAULT 'DRAFT';

-- 2. 将现有枚举值转换为字符串
UPDATE requirements SET status_new = 
    CASE status
        WHEN 'DRAFT' THEN 'DRAFT'
        WHEN 'REVIEW' THEN 'REVIEW'
        WHEN 'APPROVED' THEN 'APPROVED'
        WHEN 'IN_PROGRESS' THEN 'IN_PROGRESS'
        WHEN 'TESTING' THEN 'TESTING'
        WHEN 'DELIVERED' THEN 'DELIVERED'
        WHEN 'REJECTED' THEN 'REJECTED'
        ELSE 'DRAFT'
    END;

-- 3. 删除旧的枚举字段
ALTER TABLE requirements DROP COLUMN status;

-- 4. 重命名新字段为原字段名
ALTER TABLE requirements CHANGE COLUMN status_new status VARCHAR(50) NOT NULL DEFAULT 'DRAFT';

-- 5. 添加索引以提高查询性能
CREATE INDEX idx_requirements_status ON requirements(status);

-- 6. 添加注释
ALTER TABLE requirements MODIFY COLUMN status VARCHAR(50) NOT NULL DEFAULT 'DRAFT' COMMENT '需求状态，对应状态配置表的key';
