-- 创建项目表
CREATE TABLE projects (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_name ON projects(name);

-- 添加项目字段到需求表
ALTER TABLE requirements ADD COLUMN project_id BIGINT;

-- 创建一个默认项目用于现有需求
INSERT INTO projects (name, description, created_by) 
VALUES ('默认项目', '系统自动创建的默认项目，用于迁移现有需求', 1);

-- 将现有需求关联到默认项目
UPDATE requirements SET project_id = (SELECT id FROM projects WHERE name = '默认项目' LIMIT 1);

-- 设置项目字段为非空并添加外键约束
ALTER TABLE requirements MODIFY COLUMN project_id BIGINT NOT NULL;
ALTER TABLE requirements ADD CONSTRAINT fk_requirements_project_id
    FOREIGN KEY (project_id) REFERENCES projects(id);

-- 创建需求表的项目索引
CREATE INDEX idx_requirements_project_id ON requirements(project_id);
