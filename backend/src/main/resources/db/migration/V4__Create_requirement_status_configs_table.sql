-- 创建需求状态配置表
CREATE TABLE IF NOT EXISTS requirement_status_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(50) NOT NULL UNIQUE COMMENT '状态键',
    label VARCHAR(100) NOT NULL COMMENT '显示标签',
    color VARCHAR(20) NOT NULL COMMENT '颜色',
    icon VARCHAR(10) NOT NULL COMMENT '图标',
    description TEXT NOT NULL COMMENT '描述',
    `order` INT NOT NULL COMMENT '排序',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    allowed_transitions TEXT NOT NULL COMMENT '允许转换的状态（JSON格式）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NULL COMMENT '创建者ID',
    updated_by BIGINT NULL COMMENT '更新者ID',
    
    INDEX idx_key (`key`),
    INDEX idx_order (`order`),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) COMMENT='需求状态配置表';

-- 插入默认状态配置数据（如果不存在）
INSERT IGNORE INTO requirement_status_configs (`key`, label, color, icon, description, `order`, is_active, allowed_transitions) VALUES
('DRAFT', '草稿', '#d9d9d9', '📄', '需求草稿阶段，待完善', 1, TRUE, '["REVIEW", "REJECTED"]'),
('REVIEW', '评审中', '#faad14', '👁️', '需求评审阶段，待批准', 2, TRUE, '["APPROVED", "DRAFT", "REJECTED"]'),
('APPROVED', '已批准', '#52c41a', '✅', '需求已批准，可以开始实施', 3, TRUE, '["IN_PROGRESS", "REVIEW"]'),
('IN_PROGRESS', '进行中', '#1890ff', '🔄', '需求正在开发实施中', 4, TRUE, '["TESTING", "APPROVED", "BLOCKED"]'),
('TESTING', '测试中', '#722ed1', '🧪', '需求功能测试阶段', 5, TRUE, '["DELIVERED", "IN_PROGRESS", "BLOCKED"]'),
('DELIVERED', '已交付', '#13c2c2', '🚀', '需求已完成并交付', 6, TRUE, '["TESTING"]'),
('BLOCKED', '阻塞', '#ff7875', '🚫', '需求被阻塞，无法继续', 7, TRUE, '["IN_PROGRESS", "TESTING", "REJECTED"]'),
('REJECTED', '已拒绝', '#ff4d4f', '❌', '需求被拒绝，不予实施', 8, TRUE, '["DRAFT"]');

-- 创建状态变更日志表
CREATE TABLE IF NOT EXISTS requirement_status_change_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    requirement_id BIGINT NOT NULL COMMENT '需求ID',
    from_status VARCHAR(50) NOT NULL COMMENT '原状态',
    to_status VARCHAR(50) NOT NULL COMMENT '新状态',
    from_status_label VARCHAR(100) NOT NULL COMMENT '原状态标签',
    to_status_label VARCHAR(100) NOT NULL COMMENT '新状态标签',
    changed_by BIGINT NOT NULL COMMENT '变更人ID',
    changed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    reason TEXT NULL COMMENT '变更原因',
    metadata JSON NULL COMMENT '元数据',
    
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_changed_at (changed_at),
    INDEX idx_from_status (from_status),
    INDEX idx_to_status (to_status),
    
    FOREIGN KEY (requirement_id) REFERENCES requirements(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='需求状态变更日志表';
